module ProductFeed
  module DesignUrl<PERSON><PERSON><PERSON>

    def link_with_query(link, query_strings = [])
      query_strings = Array.wrap(query_strings).compact.join("&")
      if query_strings.present?
        "#{link}?#{query_strings}"
      else
        link
      end
    end

    def domain_url(platform=:Desktop)
      case platform.to_s.downcase
      when "desktop"
        "#{ENV['FEED_DESKTOP_DOMAIN']}"
      when "luxe"
        "#{ENV['FEED_LUXE_DOMAIN']}"
      else
        "#{ENV['FEED_MOBILE_DOMAIN']}"
      end
    end

    def get_url(platform: :Desktop, type: :product, query_string: nil, with_country_code: true)
      query_strings = Array.wrap(query_string)
      query_strings << "country_code=#{@country_code}" if with_country_code


      url = domain_url(platform)
      case type
      when :product
        # Use appropriate URL pattern based on design creation date
        if uses_new_url_structure?
          url += "/designs/#{cached_slug}"
        else
          url += "/designers/#{designer.cached_slug}/designs/#{cached_slug}"
        end
      when :pid_super_category
        url += super_category.url_path
        query_strings << "pid=#{id}"
      when :pid_category
        url += category.url_path
        query_strings << "pid=#{id}"
      when :pid_bmgn
        url += "/buy-m-get-n-free"
        query_strings << "pid=#{id}"
      when :pid_designer
        url += "/designers/#{designer.cached_slug}"
        query_strings << "pid=#{id}"
      when :pid_designer_preference
        url += category.url_path
        query_strings << "pid=#{id}"
        query_strings << "preference=designer_ids~#{designer.id}"
      when :luxe_category
        url += "/collections/luxe"
        query_strings << "category_ids=#{category.id}&pid=#{id}"
      when :luxe_pid_category
        url += category.luxe_url_path
        query_strings << "pid=#{id}"
      when :luxe_pid_super_category
        url += super_category.luxe_url_path
        query_strings << "pid=#{id}"
      when :luxe_product
        url += "/#{designer.cached_slug}/buy/#{title.parameterize}/#{id}"
      when :luxe_pid_category_sort
        url += category.luxe_url_path
        query_strings << "pid=#{id}&sort=new"
      when :luxe_pid_super_category_sort_new
        url += super_category.luxe_url_path
        query_strings << "pid=#{id}&sort=new"
      when :luxe_pid_super_category_sort_l2h
        url += super_category.luxe_url_path
        query_strings << "pid=#{id}&sort=l2h"
      end

      link_with_query(url, query_strings)
    end
  end
end

