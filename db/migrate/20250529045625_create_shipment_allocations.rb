class CreateShipmentAllocations < ActiveRecord::Migration
  def change
    create_table :shipment_allocations do |t|
      t.references :order, index: true, foreign_key: true
      t.references :shipper, index: true, foreign_key: true
      t.references :shipper_service, index: true, foreign_key: true
      t.string :mirraw_uniq_number
      t.string :master_tracking_number

      t.timestamps null: false
    end
  end
end
