class CreateGaRpvDesignAnalytics < ActiveRecord::Migration
  def change
    create_table :ga_rpv_design_analytics do |t|
      t.bigint :design_id, null: false
      t.string :country, null: false
      t.integer :period, null: false
      t.decimal :revenue, precision: 15, scale: 6
      t.integer :views
      t.integer :sales
      t.timestamps
    end

    add_index :ga_rpv_design_analytics, [:design_id, :country, :period], unique: true, name: 'index_grda_on_design_country_period'
  end
end
