class AddUrlPatternToDesigns < ActiveRecord::Migration
  def change
    add_column :designs, :url_pattern, :string, default: 'legacy'
    add_index :designs, :url_pattern
    
    # Set default values based on creation date
    reversible do |dir|
      dir.up do
        # Mark designs created after cutoff date as using new URL pattern
        cutoff_date = Date.parse(ENV['NEW_URL_STRUCTURE_CUTOFF_DATE'] || Date.current.to_s)
        execute "UPDATE designs SET url_pattern = 'simplified' WHERE created_at >= '#{cutoff_date}'"
      end
    end
  end
end
