#!/usr/bin/env ruby

# Quick script to check dynamic_prices table structure
require_relative 'config/environment'

puts "Checking dynamic_prices table structure..."
puts "=" * 50

# Check if table exists
if ActiveRecord::Base.connection.table_exists?('dynamic_prices')
  puts "✓ dynamic_prices table exists"
  
  # Get column information
  columns = ActiveRecord::Base.connection.columns('dynamic_prices')
  puts "\nColumns:"
  columns.each do |col|
    puts "  #{col.name}: #{col.type} (#{col.sql_type})"
  end
  
  # Check indexes
  indexes = ActiveRecord::Base.connection.indexes('dynamic_prices')
  puts "\nIndexes:"
  indexes.each do |idx|
    unique_str = idx.unique? ? " (UNIQUE)" : ""
    puts "  #{idx.name}: #{idx.columns.join(', ')}#{unique_str}"
  end
  
  # Check if variant_id column exists
  has_variant_id = columns.any? { |col| col.name == 'variant_id' }
  puts "\n" + "=" * 50
  if has_variant_id
    puts "✓ variant_id column EXISTS - ready for variant CSV uploads"
  else
    puts "✗ variant_id column MISSING - need to run migration:"
    puts "  bundle exec rails db:migrate"
  end
  
  # Check sample data
  design_count = DynamicPrice.where.not(design_id: nil).count
  variant_count = has_variant_id ? DynamicPrice.where.not(variant_id: nil).count : 0
  
  puts "\nCurrent data:"
  puts "  Design pricing entries: #{design_count}"
  puts "  Variant pricing entries: #{variant_count}" if has_variant_id
  
else
  puts "✗ dynamic_prices table does not exist!"
  puts "  Run: bundle exec rails db:migrate"
end
