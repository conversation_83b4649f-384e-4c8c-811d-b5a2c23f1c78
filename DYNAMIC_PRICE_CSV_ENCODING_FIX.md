# Dynamic Price CSV Upload Fix

## Database Structure

The `dynamic_prices` table supports both design and variant pricing:

```sql
CREATE TABLE dynamic_prices (
  id          SERIAL PRIMARY KEY,
  design_id   INTEGER,           -- For design-level pricing
  variant_id  INTEGER,           -- For variant-level pricing
  country_id  INTEGER NOT NULL,
  scale       FLOAT DEFAULT 1.0,
  country_code VARCHAR(255),
  created_at  TIMESTAMP,
  updated_at  TIMESTAMP
);

-- Unique constraints
CREATE UNIQUE INDEX index_dynamic_prices_on_country_code_and_design_id
  ON dynamic_prices (country_code, design_id);
CREATE UNIQUE INDEX index_dynamic_prices_on_country_code_and_variant_id
  ON dynamic_prices (country_code, variant_id);
```

**Key Points:**
- Each row has EITHER `design_id` OR `variant_id` (never both)
- Separate unique constraints for each type
- Model validation ensures data integrity

## Supported CSV Formats

The system supports two distinct CSV formats for dynamic pricing uploads:

### Format 1: Design CSV
```
design_id | country_id | scale | country_code
12345     | 840        | 1.2   | US
67890     | 826        | 1.1   | UK
```

### Format 2: Variant CSV
```
variant_id | country_id | scale | country_code
98765      | 840        | 1.3   | US
54321      | 826        | 1.2   | UK
```

**Important**: Each CSV file should contain ONLY one format (either all design_id OR all variant_id entries).

## Problems Fixed

### 1. Encoding Error
When uploading CSV files for dynamic pricing, users encountered the error:
```
Encoding::UndefinedConversionError: "\xA0" from ASCII-8BIT to UTF-8
```

This error occurred because:
1. The CSV file contains non-breaking space characters (`\xA0`)
2. The file encoding is not properly handled during CSV parsing
3. Ruby's CSV parser fails when encountering invalid UTF-8 sequences

### 2. Variant ID Processing Error
When uploading variant CSV files, users encountered:
```
ActiveRecord::RecordNotUnique: PG::UniqueViolation: ERROR: duplicate key value violates unique constraint "index_dynamic_prices_on_country_code_and_design_id"
```

This occurred because the code wasn't properly distinguishing between design and variant CSV formats.

## Root Cause Analysis

### Original Code Issue:
```ruby
CSV(open(full_path), headers:true, :header_converters => :symbol) do |csv_file|
  # Processing without encoding handling
end
```

### Problems:
1. **No encoding specification**: The `open()` method doesn't specify encoding
2. **No character sanitization**: Non-breaking spaces and other special characters aren't handled
3. **No error handling**: Encoding errors crash the entire process

## Solution Implemented

### 1. Enhanced CSV Processing (`app/models/dynamic_price.rb`)

#### Before:
```ruby
def self.update_designs(filename,remove_option = nil)
  @remove_option = remove_option.nil? ? {dynamic_pricing: true,stop_indexing: true} : JSON.parse(remove_option).symbolize_keys
  full_path = 'https://s3-'+ ENV['AWS_REGION'] +'.amazonaws.com/' + ENV['S3_BUCKET'] + '/' + filename
  scale=1.0 if (@remove_option[:dynamic_pricing] == false && @remove_option['buy_get_free'] == nil)
  CSV(open(full_path), headers:true, :header_converters => :symbol) do |csv_file|
    # Processing...
  end
end
```

#### After:
```ruby
def self.update_designs(filename,remove_option = nil)
  @remove_option = remove_option.nil? ? {dynamic_pricing: true,stop_indexing: true} : JSON.parse(remove_option).symbolize_keys
  full_path = 'https://s3-'+ ENV['AWS_REGION'] +'.amazonaws.com/' + ENV['S3_BUCKET'] + '/' + filename
  scale=1.0 if (@remove_option[:dynamic_pricing] == false && @remove_option['buy_get_free'] == nil)
  
  begin
    # Download and clean the CSV content to handle encoding issues
    csv_content = open(full_path).read
    
    # Handle encoding issues by converting to UTF-8 and removing problematic characters
    csv_content = csv_content.force_encoding('UTF-8')
    unless csv_content.valid_encoding?
      # If not valid UTF-8, try to convert from common encodings
      csv_content = csv_content.force_encoding('ISO-8859-1').encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
    end
    
    # Remove non-breaking spaces and other problematic characters
    csv_content = csv_content.gsub(/\xA0/, ' ')  # Replace non-breaking space with regular space
    csv_content = csv_content.gsub(/[^\x00-\x7F]/, '')  # Remove non-ASCII characters if needed
    
    CSV.parse(csv_content, headers: true, header_converters: :symbol) do |csv_file|
      csv_file.each_slice(1000) do |raws|
        dynamic_price_entries = load_dynamic_price_entries(raws,scale)
        if (DynamicPrice.check_file(dynamic_price_entries))
          DynamicPrice.update_and_insert(dynamic_price_entries)
        end
      end
    end
  rescue Encoding::UndefinedConversionError => e
    Rails.logger.error "Encoding error in CSV file #{filename}: #{e.message}"
    raise "CSV file contains invalid characters. Please save the file as UTF-8 encoded CSV and try again."
  rescue => e
    Rails.logger.error "Error processing CSV file #{filename}: #{e.message}"
    raise e
  end
end
```

### 2. Enhanced Data Processing

#### Before:
```ruby
def self.load_dynamic_price_entries(parsed_file,scale)
  parsed_file.collect do |line|
    entry = {
      country_id: line[:country_id].to_i,
      scale: scale || line[:scale].to_f,
      country_code: line[:country_code].to_s.upcase.gsub(/\n/,'')
    }
    # ...
  end
end
```

#### After:
```ruby
def self.load_dynamic_price_entries(parsed_file,scale)
  parsed_file.collect do |line|
    # Clean and sanitize string values to handle encoding issues
    country_code = line[:country_code].to_s.upcase
                                      .gsub(/\n/, '')
                                      .gsub(/\xA0/, ' ')  # Replace non-breaking space
                                      .strip
    
    entry = {
      country_id: line[:country_id].to_i,
      scale: scale || line[:scale].to_f,
      country_code: country_code
    }
    # ...
  end
end
```

## Key Improvements

### 1. **CSV Format Validation**
- Validates CSV headers match expected formats
- Supports both Design CSV and Variant CSV formats
- Provides clear error messages for invalid formats
- Logs detected format for debugging

### 2. **Multi-Stage Encoding Handling**
- First attempts UTF-8 encoding
- Falls back to ISO-8859-1 if UTF-8 fails
- Replaces invalid characters instead of crashing

### 3. **Character Sanitization**
- Replaces non-breaking spaces (`\xA0`) with regular spaces
- Removes non-ASCII characters that could cause issues
- Strips whitespace from processed values

### 4. **Comprehensive Error Handling**
- Catches `Encoding::UndefinedConversionError` specifically
- Provides user-friendly error messages
- Logs detailed error information for debugging

### 5. **Robust CSV Processing**
- Uses `CSV.parse()` instead of `CSV()` for better control
- Processes content in memory after cleaning
- Maintains existing batch processing logic

### 6. **Enhanced Variant Support**
- Proper detection of variant_id vs design_id entries
- Separate processing paths for each format
- Correct data structure handling for imports

### 7. **Unique Constraint Handling**
- Separate unique constraints for design and variant entries
- Graceful handling of duplicate key violations
- Fallback to individual record updates when bulk import fails
- Proper error logging for debugging

## Testing the Fix

### 1. **Test Design CSV Format**
Create a design CSV file:
```csv
design_id,country_id,scale,country_code
12345,840,1.2,US
67890,826,1.1,UK
```

### 2. **Test Variant CSV Format**
Create a variant CSV file:
```csv
variant_id,country_id,scale,country_code
98765,840,1.3,US
54321,826,1.2,UK
```

### 3. **Test with Encoding Issues**
Create a CSV file with non-breaking spaces:
```csv
design_id,country_id,scale,country_code
12345,840,1.2,US 1
67890,826,1.1,UK 1
```
(Where ` ` represents a non-breaking space character)

### 4. **Expected Behavior**
- **Valid Format**: File processes successfully with format detection logged
- **Encoding Issues**: Non-breaking spaces converted to regular spaces
- **Invalid Format**: Clear error message about expected headers
- **Mixed Format**: Warning logged but processing continues

### 5. **Error Scenarios**
- **Invalid Headers**: "Invalid CSV format. Expected headers: 'design_id | country_id | scale | country_code' OR 'variant_id | country_id | scale | country_code'"
- **Encoding Issues**: "CSV file contains invalid characters. Please save the file as UTF-8 encoded CSV and try again."
- **Empty/Invalid Data**: Processing skips invalid rows and continues

## Prevention Strategies

### 1. **User Guidelines**
Provide users with CSV formatting guidelines:
- Save files as UTF-8 encoded CSV
- Avoid copying data from Excel (which can introduce non-breaking spaces)
- Use plain text editors for CSV creation

### 2. **File Validation**
Consider adding pre-upload validation:
```ruby
def validate_csv_encoding(file_content)
  return true if file_content.valid_encoding?
  
  # Try common encodings
  ['ISO-8859-1', 'Windows-1252'].each do |encoding|
    begin
      file_content.force_encoding(encoding).encode('UTF-8')
      return true
    rescue
      next
    end
  end
  
  false
end
```

### 3. **Enhanced Upload Interface**
- Add encoding detection and conversion options
- Provide CSV template downloads
- Show preview of parsed data before processing

## Monitoring and Maintenance

### 1. **Log Analysis**
Monitor for encoding-related errors:
```bash
grep "Encoding error in CSV file" log/production.log
```

### 2. **Performance Impact**
The additional processing adds minimal overhead:
- String operations are fast
- Only affects CSV upload process
- No impact on regular application performance

### 3. **Future Enhancements**
- Add support for more character encodings
- Implement automatic encoding detection
- Provide CSV validation API endpoint

## Additional Fix: Variant ID Support

### Problem Discovered
After fixing the encoding issue, a new error emerged:
```
ActiveRecord::RecordNotUnique: PG::UniqueViolation: ERROR: duplicate key value violates unique constraint "index_dynamic_prices_on_country_code_and_design_id"
```

This occurred because the code wasn't properly handling `variant_id` uploads and was incorrectly trying to insert with `design_id=1`.

### Root Cause
1. **Data Processing Logic**: Methods assumed only `design_id` entries
2. **Array vs Hash Confusion**: `insert_and_turn_flag_on` expected arrays but received hashes
3. **Missing Variant Support**: Several methods didn't handle `variant_id` entries

### Additional Fixes Applied

#### 1. Fixed `insert_and_turn_flag_on` Method
- Added proper hash-to-array conversion for both design and variant entries
- Added duplicate removal before import
- Added `on_duplicate_key_update` for handling existing records

#### 2. Fixed `update_and_insert` Method
- Enhanced design_id extraction to handle both design_id and variant_id entries
- Added variant-to-design lookup for buy_get_free logic

#### 3. Fixed `upload_all_country_entries` Method
- Added separate handling for design and variant entries
- Enhanced uniqueness logic for both types
- Modified data structure to support both entry types

#### 4. Fixed `make_upload_hash` Method
- Added conditional logic to create proper hash structure
- Support for both design_id and variant_id in upload hash

### Key Improvements for Variant Support

```ruby
# Enhanced entry processing
if entry[:variant_id].present?
  # Convert hash to array format for import: [country_id, variant_id, scale, country_code]
  variant_entries << [entry[:country_id], entry[:variant_id], entry[:scale], entry[:country_code]]
elsif entry[:design_id].present?
  # Convert hash to array format for import: [country_id, design_id, scale, country_code]
  design_entries << [entry[:country_id], entry[:design_id], entry[:scale], entry[:country_code]]
end

# Remove duplicates and handle conflicts
design_entries.uniq! { |entry| [entry[3], entry[1]] } # unique by [country_code, design_id]
variant_entries.uniq! { |entry| [entry[3], entry[1]] } # unique by [country_code, variant_id]

# Import with duplicate handling
DynamicPrice.import fields_to_insert, variant_entries, validate: false, on_duplicate_key_update: [:scale]
```

## Complete Solution Summary

The fix now handles:
1. **Encoding Issues**: UTF-8 conversion and character sanitization
2. **Variant ID Support**: Proper processing of variant_id entries
3. **Duplicate Prevention**: Unique constraint handling
4. **Data Structure Consistency**: Hash-to-array conversion for imports
5. **Error Handling**: Comprehensive error catching and logging

## Rollback Plan

If issues arise, revert to original code:
```ruby
# Simple rollback - remove encoding handling
CSV(open(full_path), headers:true, :header_converters => :symbol) do |csv_file|
  # Original processing logic
end
```

However, this will bring back both the encoding error and variant_id issues, so consider:
1. Fixing specific encoding issues case-by-case
2. Implementing more targeted character replacement
3. Adding user education about proper CSV formatting
4. Testing variant_id uploads separately
