# Dynamic Price CSV Encoding Error Fix

## Problem
When uploading CSV files for dynamic pricing, users encounter the error:
```
Encoding::UndefinedConversionError: "\xA0" from ASCII-8BIT to UTF-8
```

This error occurs because:
1. The CSV file contains non-breaking space characters (`\xA0`)
2. The file encoding is not properly handled during CSV parsing
3. <PERSON>'s CSV parser fails when encountering invalid UTF-8 sequences

## Root Cause Analysis

### Original Code Issue:
```ruby
CSV(open(full_path), headers:true, :header_converters => :symbol) do |csv_file|
  # Processing without encoding handling
end
```

### Problems:
1. **No encoding specification**: The `open()` method doesn't specify encoding
2. **No character sanitization**: Non-breaking spaces and other special characters aren't handled
3. **No error handling**: Encoding errors crash the entire process

## Solution Implemented

### 1. Enhanced CSV Processing (`app/models/dynamic_price.rb`)

#### Before:
```ruby
def self.update_designs(filename,remove_option = nil)
  @remove_option = remove_option.nil? ? {dynamic_pricing: true,stop_indexing: true} : JSON.parse(remove_option).symbolize_keys
  full_path = 'https://s3-'+ ENV['AWS_REGION'] +'.amazonaws.com/' + ENV['S3_BUCKET'] + '/' + filename
  scale=1.0 if (@remove_option[:dynamic_pricing] == false && @remove_option['buy_get_free'] == nil)
  CSV(open(full_path), headers:true, :header_converters => :symbol) do |csv_file|
    # Processing...
  end
end
```

#### After:
```ruby
def self.update_designs(filename,remove_option = nil)
  @remove_option = remove_option.nil? ? {dynamic_pricing: true,stop_indexing: true} : JSON.parse(remove_option).symbolize_keys
  full_path = 'https://s3-'+ ENV['AWS_REGION'] +'.amazonaws.com/' + ENV['S3_BUCKET'] + '/' + filename
  scale=1.0 if (@remove_option[:dynamic_pricing] == false && @remove_option['buy_get_free'] == nil)
  
  begin
    # Download and clean the CSV content to handle encoding issues
    csv_content = open(full_path).read
    
    # Handle encoding issues by converting to UTF-8 and removing problematic characters
    csv_content = csv_content.force_encoding('UTF-8')
    unless csv_content.valid_encoding?
      # If not valid UTF-8, try to convert from common encodings
      csv_content = csv_content.force_encoding('ISO-8859-1').encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
    end
    
    # Remove non-breaking spaces and other problematic characters
    csv_content = csv_content.gsub(/\xA0/, ' ')  # Replace non-breaking space with regular space
    csv_content = csv_content.gsub(/[^\x00-\x7F]/, '')  # Remove non-ASCII characters if needed
    
    CSV.parse(csv_content, headers: true, header_converters: :symbol) do |csv_file|
      csv_file.each_slice(1000) do |raws|
        dynamic_price_entries = load_dynamic_price_entries(raws,scale)
        if (DynamicPrice.check_file(dynamic_price_entries))
          DynamicPrice.update_and_insert(dynamic_price_entries)
        end
      end
    end
  rescue Encoding::UndefinedConversionError => e
    Rails.logger.error "Encoding error in CSV file #{filename}: #{e.message}"
    raise "CSV file contains invalid characters. Please save the file as UTF-8 encoded CSV and try again."
  rescue => e
    Rails.logger.error "Error processing CSV file #{filename}: #{e.message}"
    raise e
  end
end
```

### 2. Enhanced Data Processing

#### Before:
```ruby
def self.load_dynamic_price_entries(parsed_file,scale)
  parsed_file.collect do |line|
    entry = {
      country_id: line[:country_id].to_i,
      scale: scale || line[:scale].to_f,
      country_code: line[:country_code].to_s.upcase.gsub(/\n/,'')
    }
    # ...
  end
end
```

#### After:
```ruby
def self.load_dynamic_price_entries(parsed_file,scale)
  parsed_file.collect do |line|
    # Clean and sanitize string values to handle encoding issues
    country_code = line[:country_code].to_s.upcase
                                      .gsub(/\n/, '')
                                      .gsub(/\xA0/, ' ')  # Replace non-breaking space
                                      .strip
    
    entry = {
      country_id: line[:country_id].to_i,
      scale: scale || line[:scale].to_f,
      country_code: country_code
    }
    # ...
  end
end
```

## Key Improvements

### 1. **Multi-Stage Encoding Handling**
- First attempts UTF-8 encoding
- Falls back to ISO-8859-1 if UTF-8 fails
- Replaces invalid characters instead of crashing

### 2. **Character Sanitization**
- Replaces non-breaking spaces (`\xA0`) with regular spaces
- Removes non-ASCII characters that could cause issues
- Strips whitespace from processed values

### 3. **Comprehensive Error Handling**
- Catches `Encoding::UndefinedConversionError` specifically
- Provides user-friendly error messages
- Logs detailed error information for debugging

### 4. **Robust CSV Processing**
- Uses `CSV.parse()` instead of `CSV()` for better control
- Processes content in memory after cleaning
- Maintains existing batch processing logic

## Testing the Fix

### 1. **Test with Problematic CSV**
Create a CSV file with non-breaking spaces:
```csv
design_id,country_code,country_id,scale
12345,US 1,840,1.2
67890,UK 1,826,1.1
```
(Where ` ` represents a non-breaking space character)

### 2. **Expected Behavior**
- File should process successfully
- Non-breaking spaces should be converted to regular spaces
- No encoding errors should occur

### 3. **Error Scenarios**
- If file contains completely invalid encoding, user gets clear error message
- Processing continues for valid rows even if some rows have issues

## Prevention Strategies

### 1. **User Guidelines**
Provide users with CSV formatting guidelines:
- Save files as UTF-8 encoded CSV
- Avoid copying data from Excel (which can introduce non-breaking spaces)
- Use plain text editors for CSV creation

### 2. **File Validation**
Consider adding pre-upload validation:
```ruby
def validate_csv_encoding(file_content)
  return true if file_content.valid_encoding?
  
  # Try common encodings
  ['ISO-8859-1', 'Windows-1252'].each do |encoding|
    begin
      file_content.force_encoding(encoding).encode('UTF-8')
      return true
    rescue
      next
    end
  end
  
  false
end
```

### 3. **Enhanced Upload Interface**
- Add encoding detection and conversion options
- Provide CSV template downloads
- Show preview of parsed data before processing

## Monitoring and Maintenance

### 1. **Log Analysis**
Monitor for encoding-related errors:
```bash
grep "Encoding error in CSV file" log/production.log
```

### 2. **Performance Impact**
The additional processing adds minimal overhead:
- String operations are fast
- Only affects CSV upload process
- No impact on regular application performance

### 3. **Future Enhancements**
- Add support for more character encodings
- Implement automatic encoding detection
- Provide CSV validation API endpoint

## Rollback Plan

If issues arise, revert to original code:
```ruby
# Simple rollback - remove encoding handling
CSV(open(full_path), headers:true, :header_converters => :symbol) do |csv_file|
  # Original processing logic
end
```

However, this will bring back the original encoding error, so consider:
1. Fixing specific encoding issues case-by-case
2. Implementing more targeted character replacement
3. Adding user education about proper CSV formatting
