# Dynamic pricing
DYNAMIC_PRICE_ENABLED = ENV["DYNAMIC_PRICE_ENABLED"].to_i==1
MINIMUM_SCALING_FACTOR = ENV['MINIMUM_SCALING_FACTOR'].to_f
INTERNATIONAL_MARGIN = 0.0
SWITCH_DP_TO_INTERNATIONAL = SystemConstant.get('DYNAMIC_PRICE_INT').to_i == 1
#   for specify normal return vs scaled return
RETURN_NORMAL = 1
RETURN_SCALED = 2
ENABLE_CUSTOMS_MESSAGE = (ENV['ENABLE_CUSTOMS_MESSAGE'].to_i == 1)
CUSTOMS_MESSAGE_SHOW_RATIO = ENV['CUSTOMS_MESSAGE_SHOW_RATIO'].to_i
JABBER_REVIEW_ENABLED = (ENV['JABBER_REVIEW_ENABLED'].to_i == 1)
ENABLE_PIPELINE_SORT = (ENV['ENABLE_PIPELINE_SORT'].to_i == 1)
if ENV['HTTP2_ENABLE'] == 'true'
  ASSETS_PROTOCOL = 'https:'
  IMAGE_PROTOCOL = :https
else
  ASSETS_PROTOCOL = ''
  IMAGE_PROTOCOL = ''
end
IN_STATE_CODE = SystemConstant.get('STATE_CODE')
QUICK_COD_DISABLED = (ENV['QUICK_COD_DISABLED'].to_i == 1)
MIRRAW_DOMAIN = ENV.fetch('MIRRAW_DOMAIN')
ENABLE_MAP_LANDMARK = (ENV['ENABLE_MAP_LANDMARK'] == 'true')
MAX_CHAR_LIMIT_FOR_MAP = SystemConstant.get('MAX_CHAR_LIMIT_FOR_MAP').to_i
COD_FILTER_ENABLED = (ENV['COD_FILTER_ENABLED'] != 'false')
# pay with amazon
PAY_WITH_AMAZON_DISABLED =(ENV["PAY_WITH_AMAZON_DISABLED"].to_i == 1)

#pay-with-paytm switch
PAY_WITH_PAYTM_DISABLED = (ENV["PAY_WITH_PAYTM_DISABLED"].to_i == 1)
PAYTM_STATUS_CHECK_ERROR_REPORT = SystemConstant.get('PAYTM_STATUS_CHECK_ERROR_REPORT').to_s == 'true'

FEDEX_DECLARATION = promise{ CombinePDF.load("doc/export_declaration.pdf") }
FEDEX_FORM        = promise{ CombinePDF.load("doc/export_form.pdf") }

COD_AUTO_MARK_SANE = (ENV['COD_AUTO_MARK_SANE'].to_i == 1)
SANE_INT_COD_ORDERS = (ENV['SANE_INT_COD_ORDERS'].to_i == 1)
COD_AUTO_MARK_SANE_MAX_AMNT = SystemConstant.get('COD_AUTO_MARK_SANE_MAX_AMNT').to_i
#mirraw api url
API_URL='https://api.mirraw.com'
# Pricing
MIN_PRICE_PER_PRODUCT = 50
MAX_PRICE_PER_PRODUCT = 500_000
TRANSACTION_RATE = SystemConstant.get('TRANSACTION_RATE').to_i || 35
NEW_TRANSACTION_RATE = 45

# Grading
LOWEST_GRADE = 0
HIGHEST_GRADE = 10000
# DOM_GRADE_VARIABLE = SystemConstant.get('DOM_GRADE_VARIABLE')
# INT_GRADE_VARIABLE = SystemConstant.get('INT_GRADE_VARIABLE')
# DOM_CATEGORY_GRADE_VARIABLE = SystemConstant.get('DOM_CATEGORY_GRADE_VARIABLE')
GRADING_HASH={
'DOM_GRADE_VARIABLE' => 'recommended',
'INT_GRADE_VARIABLE' => 'popular',
'DOM_CATEGORY_GRADE_VARIABLE' => 'category',
'DOM_GRADE_VARIABLE_MOBILE' => 'recommended_mobile',
'INT_GRADE_VARIABLE_MOBILE' => 'popular_mobile'
}.freeze
CATEGORY_SET = SystemConstant.get('CATEGORY_SET')
HS_CODE = promise{ FuzzyMapper.new(SystemConstant.get('HS_CODE'))}
IEC_NUMBER = promise{SystemConstant.get('IEC_NUMBER')}

# Shipping
SHIPPING_CHARGE = 0
DOMESTIC_SHIPPING_CHARGES = (SystemConstant.get('DOMESTIC_SHIPPING_CHARGES').try(:sort_by) {|total,shipping_charge|total.to_i}).try(:to_h) || {'1000'=> 33}
SHIPPING_TIME = SystemConstant.get('DOMESTIC_SHIPPING_TIME').to_i || 8
INTERNATIONAL_SHIPPING_TIME = SystemConstant.get('INTERNATIONAL_SHIPPING_TIME').to_i || 12
DESIGNER_SHIPPING_TIME = 5
MIN_TOTAL_PER_STORE = 0

ARAMEX_TRACKING_URL = "http://www.aramex.com/express/track_results_multiple.aspx?ShipmentNumber="
SHIPPER_LIST_INTERNATIONAL = Shipper.where(international: true).pluck(:name).uniq

COD = "Cash On Delivery"
BANK_DEPOSIT = "Bank Deposit"
WALLET = 'Mirraw Wallet'
BANK_TRANSFER = "Online Bank Transfer"
PAYMENT_GATEWAY = "Credit/Debit Card/Net Banking"
INTERNATIONAL_PAYMENT_GATEWAY_NAME_ONLY = "International Credit Card"
GHARPAY = "Cash Before Delivery"
GHARPAY_MIN_ORDER = 500
MIN_INTERNATIONAL_ORDER = 5000
PAYPAL = "PayPal"
PAYU_MONEY = 'PayUmoney'
PAYTM = 'Paytm'
PAY_WITH_AMAZON = 'Pay with Amazon'
ACCESSIBLE_EMAIL_ID = SystemConstant.get('EMAIL_ID_ACCESS', :to_h)
#COD_OTP_DISABLE SWITCH
COD_OTP_DISABLED = (ENV['COD_OTP_DISABLED'].to_i == 1)
INT_OTP_DISABLED = (ENV['INT_OTP_DISABLED'].to_i == 1)

MAILCHIMP_API_KEY = '************************************'
MAILCHIMP_MIRRAW_SUB_ID = 'ef5ebcb9ed'
MAILCHIMP_DESIGNER_SUB_ID = '8398406bb7'

MIRRAW_CONTACT_INFO = "+91-**********"
MIRRAW_OFFICE_TIME = "8am - 8pm"
MIRRAW_GST_NUMBER = '27AAHCM4763C1ZH'
STATE_CODE = '27'
MIRRAW_SAC_CODE = '999799'
MIRRAW_AD_CODE  = '**************'
STRIPE_AD_CODE  = '**************'

LAZY_LOAD_IMAGE = ENV['LAZY_LOAD_IMAGE'] == 'true'

COD_CHARGES = 50

ODA_SERVICE = false

MAX_SMS_EVENT = 1

#per page data
PER_PAGE = 10000
SHIPPER_STATE = 'Maharashtra'
# Ignore Quantity
#
# STOP STOP STOP! Before you start turning on and off this variable.
# Did you check all the orders are in either fraud or archived state ?
# If not you need to make sure. If you don't know why. May be you should
# not be changing this variables in the first place.
#
IGNORE_QUANTITY = false
RESPECT_QUANTITY = !IGNORE_QUANTITY
DESTROY_CART = false


#Designer Report
SLA_VIOLATION_BAD_COUNT_COMPARSION_DATE = 6
SLA_VIOLATION_BAD_COUNT_PERCENT = 5
AVG_DISPATCH_TIME_BAD_COUNT = 6
MIRRAW_DESIGNER_SLA = 3
MIRRAW_INTERNATIONAL_SLA = 8

FREESIZE = 'Free Size'

DETECT_CURRENCY = true
#CCavenue
MERCHANT_ID = "M_sha21501_21501"

#SMS API
SMS_API = "http://smsapi.24x7sms.com/api_1.0/SendSMS.aspx?EmailID=<EMAIL>&Password=Mirraw@123&MobileNo={phone}&SenderID=MIRRAW&ServiceName=TEMPLATE_BASED&Message={template}"

#ZIPDIAL API
# ZIPDIAL_COD_API = "https://www.zipdial.com/z2v/startTransaction.action?customerToken=19b2d4b2d93556599d10839bf3f6c229b94cf54d&clientTransactionId={order_number}&callerid={customer_phone}&duration={verification_time}&countryCode=91&z2vToken={z2v_token}"
DIAL2VERIFY_COD_API = "http://engine.dial2verify.in/Integ/API.dvf?mobile={phone}&passkey={z2v_token}&notify={notify_host_url}&e-notify=<EMAIL>&out=JSON&cn=IN"
#TARGETING MANTRA API
TARGETING_MANTRA_API = "//api.targetingmantra.com/RecordEvent?mid=141017"

#CCavenue MCPG
CCAVENUE_MCPG_MERCHANT_ID = "310"

#CCavenue
CCAVENUE_DEV_API_ACCESS_CODE = "AVZD03BK22BK85DZKB"
CCAVENUE_DEV_API_ENC_KEY = "13594B9FA0F623A16CF9B3847B0ECDAA"

CACHE_EXPIRES_IN =  7.days
RESPONSIVE_ACTIONS = {
  'orders' => ['new', 'retry', 'create', 'show'],
  'fashion_updates' => ['new', 'show', 'edit', 'index', 'create', 'update', 'category_page'],
  'designs' => ['show'],
  'accounts/registrations' => ['new', 'create'],
  'accounts/sessions' => ['new', 'create'],
  'store' => ['collection', 'catalog2', 'landing', 'tags1', 'search1', 'online'],
  'carts' => ['show'],
  'pages' => ['landing','help_center','eid','sell','faq','track','stitching_information'],
  'stitching_measurement' => ['stitching_form','sample_stitching_forms'],
  'reviews'=>['site_review'],
  'coupons'=>['all'],
  'surveys' => ['new'],
  'dynamic_landing_pages' => ['show'],
  'horoscopes' => ['new', 'show', 'index'],
  'wishlists' => ['index'],
  'gift_card_orders' => ['new','show'],
  'designers' => ['index']

}

# Mobile Url Patterns - URLs that we has to be rendered on mobile site
# Regex being used is :
# /\A(\/accounts\/sign_in)|(\/accounts\/cancel)|(\/accounts\/sign_up)|(\/omniauth\/(facebook|google_oauth2))|(\/omniauth\/(facebook|google_oauth2)\/callback)|(\/accounts\/password\/edit)|(\/accounts\/password\/new)|(\/d\/)|(\/designers\/.+\/designs\/)|(\/chocolate\/)|(\/festival\/)|(\/gosf-offers\/)|(\/islamic-clothing\/)|(\/islamic-clothing\/abaya\/)|(\/kids\/)|(\/men\/clothing\/)|(\/men)|(\/rakhi-gifts\/)|(\/rakhigifts\/)|(\/salwar-suits\/)|(\/salwar\/)|(\/sarees\/)|(\/store\/)|(\/women\/)/
MOBILE_PATTERN = /\A(#{REDIRECT_PATTERNS['mobile'].join(')|(')})/
DESKTOP_PATTERN = /\A(#{REDIRECT_PATTERNS['desktop'].join(')|(')})/

LOCALYTICS_MESSAGES =promise{ YAML.load_file("#{Rails.root.to_s}/db/data/localytics_notification_messages.yml")}

APP_SOURCE = %w(DESKTOP MOBILE APP)

ALLOWED_APP_VERSIONS = SystemConstant.find_by_name('ALLOWED_APP_VERSIONS_ANDROID').value.split(',')
ALLOWED_IOS_APP_VERSIONS = SystemConstant.get('ALLOWED_APP_VERSIONS_IOS').split(',')

FRESHDESK_API_KEY = ENV['FRESHDESK_API_KEY']
S3_BUCKET =  AWS::S3.new.buckets[ENV['S3_BUCKET']]

RETURN_ACCESS = PublicSystemConstant.get('RETURN_ACCESS', :to_h)

MARK_FOR_DISPTACH_ACCESS = SystemConstant.get('MARK_FOR_DISPTACH_ACCESS').split(',')
OOS_CHARGES_PERCENTAGE = SystemConstant.get('OOS_CHARGES_PERCENTAGE').to_f
QC_FAILED_CHARGES_PERCENTAGE= SystemConstant.get('QC_FAILED_CHARGES_PERCENTAGE').to_f
DEPARTMENT_HEAD_EMAILS= SystemConstant.get('DEPARTMENT_HEAD_EMAILS', :to_h)
# for password encryption/decryption
ALPHANUM = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
ENCODING_KEY = "X0vamj983C1pxwyARK5nuiDblLsZFUke47gozH6hVYPWcIfqSdrGBtN2JMTEOQ"
DEPARTMENT_TICKET_EMAILS = SystemConstant.get('DEPARTMENT_TICKET_EMAILS', :to_h)
ESCALATIONS_EMAILS = SystemConstant.get('ESCALATIONS_EMAILS', :to_h)
SKYNET_COUNTRIES =promise{ YAML.load_file("#{Rails.root}/db/data/skynet_countries.yml")}
ATLANTIC_COUNTRIES = promise{ YAML.load_file("#{Rails.root}/db/data/atlantic_destination.yml")}

PINCODE_FORMATS = promise{YAML.load_file('config/pincode_format.yml')}

AUTOCOMPLETE_ZIPCODE = promise{YAML.load_file "config/autocomplete_zipcode.yml"}
AUTOCOMPLETE_ZIPCODE_ACTIVATED = SystemConstant.get('AUTOCOMPLETE_ZIPCODE_ACTIVATED') == 'true'

HOW_TO_IMPROVE_QUALITY_DESIGNER = "Just as important as price is product quality. Our on-site reviews around product use also are factors in determining the likeliness of the product among the users. If a product has user experience quirks that cause it to be a hassle to use, you can expect customers won’t buy it. With good user experience, ratings and reviews you can expect better sales."

DESIGNER_MESSAGES = {
     "fake_reviews" =>   "This is to inform all designers that we have noticed some designers
                         giving fake reviews on our website. All such activities are being
                         tracked and strict actions may be taken.Please do not review your
                         own designs. Posting fake reviews and ratings may result in
                         permanent ban.",
     "fake_reviewers" => "Dear Designer, we have noticed many fake reviews on your designs.
                         Kindly refrain from posting such fake reviews. If you are not part of such activities please
                         contact our <NAME_EMAIL>",
     "gst" => "Dear Designer, Please update your GST related details in your profile as soon as possible."
                    }

DISPATCH_COD_BLOCK_MINUTES = SystemConstant.get('DISPATCH_COD_BLOCK_MINUTES').to_i
CANCEL_COD_VISIBLE_HOUR = SystemConstant.get('CANCEL_COD_VISIBLE_HOUR').to_i

COLOR_CODES = promise{YAML.load_file("#{Rails.root.to_s}/db/data/color_codes.yml")}

SHIPPER_EMAILS = SystemConstant.get('SHIPPER_EMAILS')
REVISED_SIZE_CHART =promise{ YAML.load_file("#{Rails.root.to_s}/db/data/revised_size_chart.yml")}
SIZE_CHART_TABLE =promise{ YAML.load_file("#{Rails.root.to_s}/db/data/size_chart.yml")}
VENDOR_PROMOTION_PLAN = promise{ YAML.load_file("#{Rails.root.to_s}/db/data/vendor_promotion_plan.yml")}
HELP_CENTER_TABLE =promise{ YAML.load_file("#{Rails.root.to_s}/db/data/help_center.yml")}
CORRECT_MEASUREMENTS = promise{ YAML.load_file("#{Rails.root.to_s}/db/data/correct_measurements.yml")}
MEASUREMENTS =promise{ YAML.load_file("#{Rails.root.to_s}/db/data/measurements.yml")}
ORDERING_OF_FAQ = (SystemConstant.get('ORDERING_OF_FAQ').presence ||  JSON.parse("{\"Order\":[0,\"order_faq\"],\"Cancellation & Returns\":[1,\"return_faq\"]}"))
ORDERING_OF_DESIGNER_FAQ = (SystemConstant.get('ORDERING_OF_DESIGNER_FAQ').presence || JSON.parse("{\"Account Settings\":[0,\"account_set\"],\"Manage Inventory\":[2,\"inventory_set\"],\"Performance\":[1,\"performance_set\"],\"Manage Orders\":[3,\"orders_set\"],\"Payments\":[4,\"payments_set\"]}"))
# Prices related to stitching
B_REGULAR = INFO_STRIP_PRICES[:atv_blouse_std] || 465
B_CUSTOM = INFO_STRIP_PRICES[:atv_blouse_custom] || 620
SS_STANDARD = INFO_STRIP_PRICES[:atv_salwar_suit_std] ||  899
SS_CUSTOM = INFO_STRIP_PRICES[:atv_salwar_suit_custom] || 1085
L_STANDARD = INFO_STRIP_PRICES[:atv_lehenga_std] || 899
L_CUSTOM = INFO_STRIP_PRICES[:atv_lehenga_custom] || 1085
FNP = INFO_STRIP_PRICES[:atv_fnp] || 100
NEAR_BY_CITY= SystemConstant.get('NEAR_BY_CITY').downcase.split(',')
#Minimum cart value
INTERNATIONAL_MINIMUM_CART_VALUE = SystemConstant.get('INTERNATIONAL_MIN_CART_VAL').to_i
EXPERT_QC=ENV['EXPERT_QC_EMAILS'].to_s.split(',')
PAYOUT_INVOICE_ID =  SystemConstant.get('PAYOUT_INVOICE_ID').to_i
COMMISSION_SERVICE_TAX = SystemConstant.get('COMMISSION_SERVICE_TAX').to_f
SWACHH_BHARAT_TAX = SystemConstant.get('SWACHH_BHARAT_TAX').to_f
KRISHI_TAX = SystemConstant.get('KRISHI_TAX').to_f
USE_FORM_VALIDATIONS = (SystemConstant.get('USE_FORM_VALIDATIONS').to_s == 'true')

IGST = 18
SGST = 9
CGST = 9
GST_HOLD_EXCLUDED_DESIGNER_IDS = SystemConstant.get('GST_HOLD_EXCLUDED_DESIGNER_IDS', :to_a).map(&:to_i)

COMMERCIAL_FOR_AUTOMATION = SystemConstant.get('COMMERCIAL_FOR_AUTOMATION')
INVOICE_TITLE_CHANGE = SystemConstant.get('INVOICE_TITLE_CHANGE').to_s.downcase.split(',')

DESIGNER_SLUGS = SystemConstant.get('DESIGNER_SLUGS')

REFERRAL_TYPES = REFERRAL_TYPE.split(',')

DESIGNABLE_CATEGORY_MAPPING = SystemConstant.get('DESIGNABLE_CATEGORY_MAPPING', :to_h)

SEO_TOP_CONTENT_ENABLE = SystemConstant.get('SEO_TOP_CONTENT_ENABLE').to_s.split(',')

SearchKeywordBoosting = (SystemConstant.get('SearchKeywordBoosting').presence || JSON.parse('{"keywords": 1.5,"imp_keywords": 3,"max_sell_count_scale": 1.4,"min_sell_count_scale": 1.0}')).symbolize_keys

DISABLE_SOLR = ENV["DISABLE_SOLR"].to_s == 'true'

PARENT_CATEGORIES = SystemConstant.get('PARENT_CATEGORIES').to_s.split(',')

ACTIVATE_VIZURY_TRACKING = (ENV['ACTIVATE_VIZURY_TRACKING'].to_i == 1)

MENU_SUPER_TAG = SystemConstant.get('MENU_SUPER_TAG')

DYNAMIC_LANDING_TEMPLATES = promise{ YAML.load_file("#{Rails.root.to_s}/db/data/landing_page_templates.yml")}

ENABLE_DESIGNER_DESIGNABLE_EDIT = (ENV['ENABLE_DESIGNER_DESIGNABLE_EDIT'].to_i == 1)

AWS_ACCESS_KEYS = {
                    access_key_id: ENV['AWS_ACCESS_KEY_ID'],
                    secret_access_key: ENV['AWS_SECRET_ACCESS_KEY'],
                    bucket: ENV['S3_BUCKET']
                  }

DELAY_DELIVERY = SystemConstant.get('DELAY_DELIVERY')

RAKHI_PRE_ORDER = SystemConstant.get('RAKHI_PRE_ORDER').to_s.split(',')

OPTIONS_FOR_SELL_FORM = SystemConstant.get('OPTIONS_FOR_SELL_FORM').to_s.split('--')

ENABLE_GST = (ENV['ENABLE_GST'] == 'true')
API_DEVICE_ID = SystemConstant.get('WEB_DEVICE_ID')

HSN_RATES = SystemConstant.get('HSN_RATES')

DESIGN_SPEC_SUB_GROUP = SystemConstant.get('DESIGN_SPEC_SUB_GROUP')

ENABLE_SIGNUP_COUPON_SUBSCRIPTION = (SystemConstant.get('ENABLE_SIGNUP_COUPON_SUBSCRIPTION') == 'true')


#['inr', 'rs'].include?(@symbol.downcase) ? [:grade, :desc, 'default'] : [:international_grade, :desc, 'default']
SORT_OPTS = {
  'trending-designs' => [:trending_designs, :desc, 'trending-designs'.freeze].freeze,
  'trending' => [:clicks_impression, :desc, 'trending'.freeze].freeze,
  'top_rated' => [:graded_rating, :desc, 'top_rated'.freeze].freeze,
  'l2h' => [:discount_price, :asc, 'l2h'.freeze].freeze,
  'h2l' => [:discount_price, :desc, 'h2l'.freeze].freeze,
  'new' => [:created_at, :desc, 'new'.freeze].freeze,
  'discount' => [:discount_percent, :desc, 'discount'.freeze].freeze,
  'bstslr' => [:sell_count, :desc, 'bstslr'.freeze].freeze,
  'default' => [:international_grade, :desc, 'default'.freeze].freeze,
  'recommended' => [:dom_grade, :desc,'recommended'.freeze].freeze,
  'popular' => [:int_grade, :desc, 'popular'.freeze].freeze,
  'eid' => [:category_grade, :desc, 'eid'.freeze].freeze,
  'store-dom' => [:store_grade, :desc, 'domestic-store'].freeze,
  'store-int'=> [:store_international_grade, :desc, 'int-store'].freeze
}.freeze

SCHEDULE_TIMER = (SystemConstant.get('ENABLE_SCHEDUL_TIMER') == 'true')
HOME_DYNAMIC_TEMPLATE = PublicSystemConstant.get('HOME_DYNAMIC_TEMPLATE', :to_h)
VALID_EMAIL_REGEX = /\A[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*\z/.freeze

BESTSELLER_CREATE_LIMIT = ENV['BESTSELLER_CREATE_LIMIT'].to_i

BESTSELLER_ODR_LIMIT = ENV['BESTSELLER_ODR_LIMIT'].to_i

BESTSELLER_EXTRA_ODR_LIMIT = ENV['BESTSELLER_EXTRA_ODR_LIMIT'].to_i

WEBP_CONFIGURATION = SystemConstant.get('WEBP_CONFIGURATION', :to_h).collect do |model_name, stime|
                          stime = model_name == 'switch' ? (stime == "true") : (stime.to_s.to_time rescue '')
                          [model_name.freeze, stime.freeze ]
                        end.to_h.freeze rescue {}.freeze

SHIPPING_ITEM_VALUE = (value = SystemConstant.get('SHIPPING_ITEM_VALUE', :to_i)) == 0 ? 200 : value

CRITEO_ACCOUNT_ID = JSON.parse(ENV['CRITEO_ACCOUNT_ID'] || '{}')

PETTICOAT_FORM_DATA = SystemConstant.get('PETTICOAT_FORM_DATA', :to_h)

SHAPEWEAR_FORM_DATA = SystemConstant.get('SHAPEWEAR_FORM_DATA', :to_h)
COLOUR_BASED_SEO = SystemConstant.get('COLOUR_BASED_SEO').split(',')
FACETED_URL_KINDS = SystemConstant.get('FACETED_URL_KINDS', :to_h)
TICKET_ISSUES = SystemConstant.get('TICKET_ISSUES', :to_h)
TICKET_ISSUES_FOR_HIGER_TAT = SystemConstant.get('TICKET_ISSUES_FOR_HIGER_TAT', :to_h)
FCM_NOTIFICATION = SystemConstant.get('FCM_NOTIFICATION').to_s == 'true'

CLEVERTAP_APP_ID_LIST = YAML.load_file("#{Rails.root.to_s}/db/data/clevertap_app_ids.yml")

REMARKETING_CONVERSION_ID = ENV['REMARKETING_CONVERSION_ID']

LOYALTY_VALUES = SystemConstant.get('LOYALTY_VALUES', :to_h).collect{|key,val| [Range.new(*(key.split('..').collect(&:to_i))), val]}.to_h

TAILOR_STYLIST_LOCKOUT_DAYS = SystemConstant.get('TAILOR_STYLIST_LOCKOUT_DAYS', :to_h)
RAKHI_SALE = ENV['RAKHI_SALE'].to_i == 1

LIVE_CHAT_ENABLE = SystemConstant.get('LIVE_CHAT_ENABLE', :to_h)

ADWORD_CONVERSION_TRACKING_IDS = JSON.parse(ENV['ADWORD_CONVERSION_TRACKING_IDS'].presence || '{}') rescue {}

CUSTOM_DUTY_COUNTRY = SystemConstant.get('CUSTOM_DUTY_COUNTRY')

PRODUCT_DESIGNABLE_TYPES = {"Anarkali with Chudidar" => "anarkali","Anarkali with Pant" => "anarkali_pant"," Kameez with Salwar" => "kameez_salwar","Kameez with Pant" => "kameez_pant","Blouse" => "blouse","Lehenga Choli"=> "lehenga_choli", 'Kurti' => 'kurti', 'Islamic' => 'islamic', 'Kameez with Chudidar' => 'kameez_chudidar', 'Anarkali With Bottom (As Per Image)' => 'anarkali_with_bottom_as_per_image', 'Kameez With Bottom (As Per Image)' => 'kameez_with_bottom_as_per_image', 'Pre-Stitched Saree' => 'pre_stitched_saree'}
FEEDBACK_REVIEW_ACCESSIBLE_EMAIL_ID = promise{SystemConstant.get('FEEDBACK_REVIEW_ACCESSIBLE_EMAIL_ID')}

LINE_ITEM_SYNC_DESIGN = SystemConstant.get('LINE_ITEM_SYNC_DESIGN') == '1'
VENDOR_SUSPENSION_METRIC = SystemConstant.get('VENDOR_SUSPENSION').presence || {}
DESIGNER_VACATION_CONSTRAINTS = SystemConstant.get('DESIGNER_VACATION_CONSTRAINTS').presence || {}
LONG_DIV = ENV['LONG_DIV'].to_s.split(',')
SHARED_PARTIAL_CACHING = ENV['SHARED_PARTIAL_CACHING'] != "false"

ENABLE_COD_CHARGE = SystemConstant.get('ENABLE_COD_CHARGE')
ENABLE_COD_COUNTRIES = SystemConstant.get('ENABLE_COD_COUNTRIES').presence || ['india']
MIN_PRODUCT_THRESHOLD_FOR_CART_RECOMMEND = SystemConstant.get('MIN_PRODUCT_THRESHOLD_FOR_CART_RECOMMEND').to_i
ENABLE_ALMOST_GONE = ENV['ENABLE_ALMOST_GONE'] == 'true'

NEW_THEME_ROLLOUT = JSON.parse(ENV['NEW_THEME_ROLLOUT'] || '{}')

THEME_CSS_FILE_MAPPING = YAML.load_file("#{Rails.root}/config/theme_css_file_mapping.yml")

TAT_FOR_TAILOR = SystemConstant.get('TAT_FOR_TAILOR').presence || {}

STYLIST_TAILOR_MAPPING = SystemConstant.get('STYLIST_TAILOR_MAPPING').presence || {}

DELIST_CONSTRAINTS = SystemConstant.get('DELIST_CONSTRAINTS').presence || {}
ANARKALI_CATEGORY_FOR_STANDARD_ADDON = promise{Category.where(name: 'anarkali-salwar-kameez').collect(&:self_and_descendants).flatten.collect(&:id)}
LONG_DIV_RUN_DATE = Date.parse(SystemConstant.get('LONG_DIV_RUN_DATE')) rescue 10.year.ago
LONG_DIV_CATEGORY_IDS = SystemConstant.get('LONG_DIV_CATEGORY_IDS').to_a.map(&:to_i)

UNVEILING_EFFECT = ENV['UNVEILING_EFFECT'] == 'true'
STATE_WISE_COURIER_CHECK = SystemConstant.get('STATE_WISE_COURIER_CHECK').presence || {}

SOR_READY_TO_SHIP = SystemConstant.get('SOR_READY_TO_SHIP') == 'true'

REFERRAL_EXPIRY_PERIOD = SystemConstant.get('REFERRAL_EXPIRY_PERIOD')
RTS_ALLOWED_COUNTRIES = SystemConstant.get('RTS_ALLOWED_COUNTRIES')
AUTO_RACK_ASSIGN = SystemConstant.get('AUTO_RACK_ASSIGN').to_i == 1
STITCHING_BAG_THRESHOLD_PERCENT = SystemConstant.get('STITCHING_BAG_THRESHOLD_PERCENT').to_i
INHOUSE_VENDOR_IDS = SystemConstant.get('INHOUSE_VENDOR_IDS', :to_a)
VENDOR_ISSUE_LIST = SystemConstant.get('VENDOR_ISSUE_LIST')
DISABLE_ADMIN_FUCTIONALITY = SystemConstant.get('DISABLE_ADMIN_FUCTIONALITY', :to_h)
SHIPMENT_BLOCK_TICKET_ISSUES = SystemConstant.get('SHIPMENT_BLOCK_TICKET_ISSUES', :to_a).map(&:downcase)
ALLOWED_VENDOR_FOR_PREPARATION = SystemConstant.get('ALLOWED_VENDOR_FOR_PREPARATION', :to_a).map(&:to_i)
TAILORING_MATERIALS = SystemConstant.get('TAILORING_MATERIALS', :to_a)
DESIGNABLE_TAILORING_MATERIAL = promise{ SystemConstant.get('DESIGNABLE_TAILORING_MATERIAL', :to_h)}
MEASUREMENT_HOLD_REASONS = SystemConstant.get('MEASUREMENT_HOLD_REASONS', :to_a)
ORDER_MESSAGES = promise{ YAML.load_file("#{Rails.root.to_s}/db/data/order_sms.yml")}
DESIGNER_ADDON = (ENV['DESIGNER_ADDON'].to_i == 1) || Rails.env.development?
HIDDEN_FACETS_FILTERS = SystemConstant.get('HIDDEN_FACETS_FILTERS') || {}
BLOUSE_AVAILABILITY_CHECK_VENDORS = SystemConstant.get('BLOUSE_AVAILABILITY_CHECK_VENDORS', :to_a).map(&:to_i)
SOLR_USE_SCALED_PRICE = SystemConstant.get('SOLR_USE_SCALED_PRICE', :to_i) > 0
TRANSFER_MODEL_DISCOUNT = 20

ERROR_404_POPULAR_LINKS = {
  "Saree" => "/store/sarees",
  "Salwar Kameez" => "/salwar-suits/salwar-kameez",
  "Lehenga" => "/store/lehengas",
  "Hijab" => "/islamic-clothing/hijab",
  "Burka" => "/islamic-clothing/burka",
  "Kaftan " => "/islamic-clothing/kaftans",
  "Kurti" => "/women/clothing/kurtas-and-kurtis",
  "Jewellery" => "/store/jewellery",
  "Kids Wear" => "/kids",
  "Mens Clothing" => "/men/clothing",
  "Home Decor " => "/home-decor"
}.freeze

CUSTOM_STITCHING_BUST_SIZE_CONSTRAINTS = SystemConstant.get('CUSTOM_STITCHING_BUST_SIZE_CONSTRAINTS', :to_h)
XPRESS_BEES_PROCESS_CODES = promise{ YAML.load_file("#{Rails.root.to_s}/config/xpress_bees_pincode_process_codes.yml")}

FEEDBACK_SAMPLE_FORM_LINE_ITEMS = SystemConstant.get('FEEDBACK_SAMPLE_FORM_LINE_ITEMS')
VENDOR_APP_SELECTIVE_PROPERTIES = SystemConstant.get('VENDOR_APP_SELECTIVE_PROPERTIES') || {}
COMPANY_LOCATIONS = SystemConstant.get('USER_COURIER_COMPANY_ADDRESS', :to_h ).deep_symbolize_keys
WALLET_CONFIG = SystemConstant.get('WALLET_CONFIG')
QUALITY_RATING_QUESTION_ID = SystemConstant.get('QUALITY_RATING_QUESTION_ID', :to_i).nonzero? || SurveyQuestion.where(qualify_as: 'quality', question_type: 'rating').first.try(:id) || 21
ENABLE_PLUS_SIZE = SystemConstant.get('ENABLE_PLUS_SIZE', :to_h)
PLUS_SIZE = SystemConstant.get('PLUS_SIZE', :to_h)

LATE_PICKED_UP_DOMESTIC_ORDERS = SystemConstant.get('LATE_PICKED_UP_DOMESTIC_ORDERS') || {
  'offset_days' => 3,
  'cc_emails' => []
}

IP_BLOCK_LIST = SystemConstant.get('IP_BLOCK_LIST') || []

CIN = SystemConstant.get('CIN') || ''

CRITICAL_DAYS = SystemConstant.get('DES_ORD_CRIT_DAYS').to_i

TAILOR_DASHBOARD_METRIC = SystemConstant.get('TAILOR_DASHBOARD_METRIC') || {
  'delay_ratio': {
    'threshold_time_in_days': 1
  },
  'average_time': {
    'offset_in_days' => 5,
    'period_in_days' => 30
  }
}

DASHBOARD_NAVIGATIONS = promise{ YAML.load_file("#{Rails.root.to_s}/db/data/admin_dashboard_navigations.yml")}
NO_REPLY_EMAIL = 'Mirraw <<EMAIL>>'
LUXE_EMAIL = 'Muzai <<EMAIL>>'
MIRRAW_TEAM_EMAIL = 'Mirraw.com <<EMAIL>>'
RETURN_REASON_DETAILS = SystemConstant.get('RETURN_REASON_DETAILS', :to_h) || {}
ADMIN_RETURN_REASON = SystemConstant.get('ADMIN_RETURN_REASON', :to_h) || {}
MIRRAW_CONTACT_US = SystemConstant.get('MIRRAW_CONTACT_US', :to_h) || {}
PAYPAL_PRESENTMENT_CURRENCIES = SystemConstant.get('PAYPAL_PRESENTMENT_CURRENCIES',:to_a) || []
DESIGNER_LSR = SystemConstant.get('DESIGNER_LSR').to_i || 30
RACK_AUDIT_CONFIG = SystemConstant.get('RACK_AUDIT_CONFIG', :to_h) || {}
METRO_CITIES = SystemConstant.get('METRO_CITIES') || %w(mumbai chennai delhi kolkata hyderabad bangalore)
CITY_BASED_SHIP_TIME = SystemConstant.get('CITY_BASED_SHIP_TIME', :to_h).symbolize_keys || {metro: 2, non_metro: 5}
PLUS_SIZE_CATEGORY_PARAM = SystemConstant.get('PLUS_SIZE_CATEGORY_PARAM', :to_h) || {
  'Saree' => ['plus-size-blouses', '42'],
  'Kurti' => ['plus-size-kurtis', '3XL'],
  'SalwarKameez' => ['plus-size-salwar', '42'],
  'Islamic' => ['plus-size-kaftans', 'XXL'],
  'Other' => ['plus-size-leggings', '40']
}
SHARE_AND_EARN_REWARD = SystemConstant.get('SHARE_AND_EARN_REWARD').to_i
DEFAULT_BEST_SHIPPER_COUNTRIES = SystemConstant.get('DEFAULT_BEST_SHIPPER_COUNTRIES', :to_h)
BANNED_COUNTRIES = SystemConstant.get('BANNED_COUNTRIES', :to_h) || {}
CHECK_COD_DISABLED = (ENV['CHECK_COD_DISABLED'].to_i == 1)
ESSENTIAL_DESIGNERS = SystemConstant.get('ESSENTIAL_DESIGNERS', :to_h) || {}
PINCODE_BASED_CATEGORY_DELIVERY = SystemConstant.get('PINCODE_BASED_CATEGORY_DELIVERY', :to_h) || {}
ALLOWED_DOMESTIC_SOR = (SystemConstant.get('ALLOWED_DOMESTIC_SOR') == 'true')
INSCAN_DO_MONTHS  = (SystemConstant.get('INSCAN_DO_MONTHS')).to_i || 2
VIDEO_LISTING_ROLE_ACCESS = (SystemConstant.get('VIDEO_LISTING_ROLE_ACCESS', :to_a))
LUXE_WAREHOUSE_ADDRESS_ID = 3
AUTOMATED_COD_REFUND = SystemConstant.get('AUTOMATED_COD_REFUND', :to_h) || {}
ENABLE_NEW_RACK_LOGIC = SystemConstant.get('ENABLE_NEW_RACK_LOGIC', :to_h).presence || {'enable' => false}
LANE_FUNCTIONALITY = SystemConstant.get('LANE_FUNCTIONALITY').to_i == 1
EXCLUDE_FREE_SHIPPING_CATEGORIES = SystemConstant.get('EXCLUDE_FREE_SHIPPING_CATEGORIES').map(&:to_i) || []
COUNTRY_SPECIFIC_SHIPPER_LOGIC = SystemConstant.get('COUNTRY_SPECIFIC_SHIPPER_LOGIC', :to_h)
CLASSIQUES_DESIGNER = Designer.find(Rails.env.production? ? 12727 : 148)
REDIRECT_ROUTE = RedirectRule.pluck(:route, :match).flatten | Menu.pluck(:link).to_a | MenuColumn.pluck(:link).to_a | MenuItem.pluck(:link).to_a
LOCKDOWN_COD_DISABLE_PINCODES = SystemConstant.get('LOCKDOWN_MAYHEM')['cod_disable_pincodes'].presence || []
LOCKDOWN_NON_SERVICABLE_PINCODES = SystemConstant.get('LOCKDOWN_MAYHEM')['non_servicable_pincodes'].presence || []
ENABLE_PAYPAL_CREDIT = SystemConstant.get('ENABLE_PAYPAL_CREDIT').presence || false
# NOTIFY_DEVS = (SystemConstant.get('NOTIFY_DEVS', :to_a))
ENABLE_WIZGO = SystemConstant.get('ENABLE_WIZGO').presence || false
DOORSTEP = SystemConstant.get('DOORSTEP')
SET_MARKET_RATE_PRIORITY = SystemConstant.get('SET_MARKET_RATE_PRIORITY', :to_a)
CHANGE_TERMS_OF_SERVICE_DHL = SystemConstant.get('CHANGE_TERMS_OF_SERVICE_DHL', :to_s)
ENABLE_COD_WITH_RANGE_AND_COUNTRIES = SystemConstant.get('ENABLE_COD_WITH_RANGE_AND_COUNTRIES',:to_h).presence
CART_ADDON = SystemConstant.get('CART_ADDON',:to_h).presence
GOOGLE_FEEDS_MENS_CATEGORIES = SystemConstant.get('GOOGLE_FEEDS_MENS_CATEGORIES', :to_a)
MENS_CATEGORY_IDS = Category.where("lower(name) IN (?)", SystemConstant.get("GOOGLE_FEEDS_MENS_CATEGORIES")).map{|category| category.self_and_descendants.pluck(:id)}.flatten
FOOTWEAR_CATEGORY_PARENT_IDS = SystemConstant.get('FOOTWEAR_CATEGORY_PARENT_IDS').map(&:to_i)
FOOTWEAR_CATEGORY_IDS = SystemConstant.get('FOOTWEAR_CATEGORY_IDS').map(&:to_i)
UNBXD_V2_2023 = SystemConstant.get('UNBXD_V2_2023') == "true"
ADMIN_PANEL_ACCESS = SystemConstant.get('ADMIN_PANEL_ACCESS', :to_h)
RESTRICTED_REASONS_FOR_RETURN_ORDER = SystemConstant.get('RESTRICTED_REASONS_FOR_RETURN_ORDER') || []
SHIPPERS_PRIORITY = JSON.parse(SystemConstant.get('SHIPPERS_PRIORITY'))
CUSTOM_TAGS = SystemConstant.get('CUSTOM_TAGS') || []
PLT_SIGNATURE = SystemConstant.find_by_name('PLT_SIGNATURE').value
GIFT_ORDER = SystemConstant.get('GIFT_ORDER', :to_h) || {}
CURRENCY_CONVERT_TO_USD = SystemConstant.get('CURRENCY_CONVERT_TO_USD', :to_h)|| {}
RACK_BLOCK_UNBLOCK = SystemConstant.get('RACK_BLOCK_UNBLOCK', :to_h) || {}
MAX_UPLOADABLE_CATEGORIES = SystemConstant.get("MAX_UPLOADABLE_CATEGORIES", :to_h) || {}
CUSTOM_TAGS = SystemConstant.get('CUSTOM_TAGS') || []
SELLER_CAMPAIGN_ATTRIBUTES = SystemConstant.get('SELLER_CAMPAIGN_ATTRIBUTES', :to_h) || {}
EXCLUDED_DESIGN_SPEC_KEYS = SystemConstant.get('EXCLUDED_DESIGN_SPEC_KEYS', :to_a) || []
BULK_DESIGN_EDITABLE_FIELDS = SystemConstant.get('BULK_DESIGN_EDITABLE_FIELDS', :to_a) || []
ORDER_PROCESSING_DAYS = SystemConstant.get('ORDER_PROCESSING_DAYS').to_i
FRESHDESK_AUTOMATION = SystemConstant.get('FRESHDESK_AUTOMATION', :to_h) || {}
KYC_DOCUMENT_TYPES = SystemConstant.get('KYC_DOCUMENT_TYPES', :to_a) || []
SOUTH_AFRICA_SHIPPER = SystemConstant.get('SOUTH_AFRICA_SHIPPER')
INHOUSE_VENDORS_FOR_STATE_CHANGE = SystemConstant.get('INHOUSE_VENDORS_FOR_STATE_CHANGE') || []
SHIPCONSOLE_HSN_CODE = SystemConstant.get('SHIPCONSOLE_HSN_CODE')