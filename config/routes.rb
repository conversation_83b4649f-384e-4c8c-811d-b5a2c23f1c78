require 'sidekiq/web'

Rails.application.routes.draw do

    resources :designer_owners, only: [] do
      member do
        get :dashboard
        post :dashboard
      end
      collection do
        get :manager_dashboard
        post :manager_dashboard
      end
    end

    namespace :delayed_files do
        post :status
    end

    get 'designer_order_panels/sla_canceled_orders'

    # Temporary Fix
    get "/islamic-clothing/kaftans/party-kaftans" => redirect("/islamic-clothing/kaftans/party-wear-kaftans")
    get "/islamic-clothing/abaya/party-abaya" => redirect("/islamic-clothing/abaya/party-wear-abaya")
    get "/islamic-clothing/tunics/party-tunics" => redirect("/islamic-clothing/tunics/party-wear-tunics")
    get "/islamic-clothing/burka/party-burka" => redirect("/islamic-clothing/burka/party-wear-burka")
    get "/men/clothing/nehru-jacket/party-nehru-jacket" => redirect("/men/clothing/nehru-jacket/party-wear-nehru-jacket")
    get "/men/clothing/kurta-pajama/party-kurta-pajama" => redirect("/men/clothing/kurta-pajama/party-wear-kurta-pajama")
    get "/kids/girls/clothing/lehenga-choli/party-lehenga-choli" => redirect("/kids/girls/clothing/lehenga-choli/party-wear-lehenga-choli")
    get "/islamic-clothing/farasha/party-farasha" => redirect("/islamic-clothing/farasha/party-wear-farasha")
    get "/women/jewellery/earrings/party-earrings" => redirect("/women/jewellery/earrings/party-wear-earrings")  
    get "/men/accessories/cufflinks/party-cufflinks" => redirect("/men/accessories/cufflinks/party-wear-cufflinks")
    get "/men/clothing/kurtas/party-kurtas" => redirect("/men/clothing/kurtas/party-wear-kurtas")
    get "/women/clothing/blouses/party-blouse" => redirect("/women/clothing/blouses/party-wear-blouse")
    get "/kids/girls/clothing/sarees/party-sarees" => redirect("/kids/girls/clothing/sarees/party-wear-sarees")
    get "/women/jewellery/pendants/party-pendants", to: redirect(path: "/women/jewellery/pendants/party-wear-pendants")
    get "/women/clothing/gowns/party-gowns" => redirect("/women/clothing/gowns/party-wear-gowns")
    get "/women/clothing/kurtas-and-kurtis/party-kurtis" => redirect("/women/clothing/kurtas-and-kurtis/party-wear-kurtis")
    get "/kids/girls/clothing/kaftans/party-kaftans" => redirect("/kids/girls/clothing/kaftans/party-wear-kaftans")

    get "/kids/boys/clothing/nehru-jacket/party-nehru-jacket" => redirect("/kids/boys/clothing/nehru-jacket/party-wear-nehru-jacket")
    get "/kids/boys/clothing/dhoti-kurta/party-dhoti-kurta" => redirect("/kids/boys/clothing/dhoti-kurta/party-wear-dhoti-kurta")
    get "/kids/boys/clothing/sherwani/party-sherwani" => redirect("/kids/boys/clothing/sherwani/party-wear-sherwani")
    get "/kids/boys/clothing/indo-western-dress/party-indo-western-dress" => redirect("/kids/boys/clothing/indo-western-dress/party-wear-indo-western-dress")
    get "/kids/boys/clothing/kurta-pyjama/party-kurta-pyjama", to: redirect(path: "/kids/boys/clothing/kurta-pyjama/party-wear-kurta-pyjama")

    get "/kids/girls/clothing/frocks/party-frocks" => redirect("/kids/girls/clothing/frocks/party-wear-frocks")
    get "/kids/girls/clothing/salwar-suits/party-salwar-suits" => redirect("/kids/girls/clothing/salwar-suits/party-wear-salwar-suits")
    get "/kids/girls/clothing/gowns/party-gowns" => redirect("/kids/girls/clothing/gowns/party-wear-gowns")
    # page_redirect = lambda{|url,request,params={}| url + ( params[:colour].nil? ? "" : "/colour-#{params[:colour]}" ) + ( request.params[:page].nil? ? "" : "?page=#{request.params[:page]}" ) }
    RedirectionRouter.load
    get "header_infos/index"
    # get "/store/salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/salwar-kameez",request,params) }
    # get "/salwar-suits/salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "salwar-kameez", :as => "salwar_search"

    # get "/store/anarkali-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/anarkali-salwar-kameez",request,params) }
    # get "/salwar-suits/anarkali-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "anarkali-salwar-kameez", :as => "anarkali_salwar_search"

    # get "/store/cotton-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/cotton-salwar-kameez",request,params) }
    # get "/salwar-suits/cotton-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "cotton-salwar-kameez", :as => "cotton_salwar_search"

    # get "/store/party-wear-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/party-wear-salwar-kameez",request,params) }
    # get "/salwar-suits/party-wear-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "party-wear-salwar-kameez", :as => "party_wear_salwar_search"

    # get "/store/pakistani-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/pakistani-salwar-kameez",request,params) }
    # get "/salwar-suits/pakistani-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "pakistani-salwar-kameez", :as => "pakistani_salwar_search"

    # get "/store/ready-to-ship-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/ready-to-ship-salwar-kameez",request,params) }
    # get "/salwar-suits/ready-to-ship-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "ready-to-ship-salwar-kameez", :as => "ready_to_ship_salwar_kameez_search"

    # get "/store/tie-pins(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/accessories/tie-pins",request,params) }
    # get "/men/accessories/tie-pins(/colour-:colour)" =>"store#catalog2", :kind => "tie-pins", :as => "tie_pins_search"

    # get "/store/dresses(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/dresses",request,params) }
    # get "/women/clothing/dresses(/colour-:colour)" =>"store#catalog2", :kind => "dresses", :as => "dresses_search"

    # get "/store/cufflinks(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/accessories/cufflinks",request,params) }
    # get "/men/accessories/cufflinks(/colour-:colour)" =>"store#catalog2", :kind => "cufflinks", :as => "cufflinks_search"

    # get "/store/leggings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/leggings",request,params) }
    # get "/women/clothing/leggings(/colour-:colour)" =>"store#catalog2", :kind => "leggings", :as => "leggings_search"

    # get "/store/maxi-dresses(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/dresses/maxi-dresses",request,params) }
    # get "/women/clothing/dresses/maxi-dresses(/colour-:colour)" =>"store#catalog2", :kind => "maxi-dresses", :as => "maxi_dresses_search"

    # get "/store/home-furnishing(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/home-furnishing",request,params) }
    # get "/home-decor/home-furnishing(/colour-:colour)" =>"store#catalog2", :kind => "home-furnishing", :as => "home_furnishing_search"

    # get "/store/home-decor(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor",request,params) }
    # get "/home-decor(/colour-:colour)" =>"store#catalog2", :kind => "home-decor", :as => "home_decor_search"

    # get "/store/wall-decals(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/wall-decals",request,params) }
    # get "/home-decor/wall-decals(/colour-:colour)" =>"store#catalog2", :kind => "wall-decals", :as => "wall_decals_search"

    # get "/store/artificial-flowers(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/artificial-flowers",request,params) }
    # get "/home-decor/artificial-flowers(/colour-:colour)" =>"store#catalog2", :kind => "artificial-flowers", :as => "artificial_flowers_search"

    # get "/store/ethnic-bottoms(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/ethnic-bottoms",request,params) }
    # get "/women/clothing/ethnic-bottoms(/colour-:colour)" =>"store#catalog2", :kind => "ethnic-bottoms", :as => "ethnic_bottoms_search"

    # get "/store/wedding-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/wedding-salwar-kameez",request,params) }
    # get "/salwar-suits/wedding-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "wedding-salwar-kameez", :as => "wedding_salwar_search"

    # get "/store/semi-stitched-salwar-suits(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/semi-stitched-salwar-suits",request,params) }
    # get "/salwar-suits/semi-stitched-salwar-suits(/colour-:colour)" =>"store#catalog2", :kind => "semi-stitched-salwar-suits", :as => "semi_stitched_salwar_search"

    # get "/store/bollywood-salwar-kameez-online(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/bollywood-salwar-kameez-online",request,params) }
    # get "/salwar-suits/bollywood-salwar-kameez-online(/colour-:colour)" =>"store#catalog2", :kind => "bollywood-salwar-kameez-online", :as => "bollywood_salwar_search"

    # get "/store/salwar-combo(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/salwar-combo",request,params) }
    # get "/salwar-suits/salwar-combo(/colour-:colour)" =>"store#catalog2", :kind => "salwar-combo", :as => "salwar_salwar_search"

    # get "/store/pongal-dhoti-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/pongal-dhoti-saree",request,params) }
    # get "/sarees/pongal-dhoti-saree(/colour-:colour)" =>"store#catalog2", :kind => "pongal-dhoti-saree", :as => "pongal_dhoti_saree_search"

    # get "/store/crop-tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/tops/crop-tops",request,params) }
    # get "/women/clothing/tops/crop-tops(/colour-:colour)" =>"store#catalog2", :kind => "crop-tops", :as => "crop_tops_saree_search"

    # get "/store/sunglasses(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/accessories/sunglasses",request,params) }
    # get "/women/accessories/sunglasses(/colour-:colour)" =>"store#catalog2", :kind => "sunglasses", :as => "sunglasses_search"

    # get "/store/kids-skirts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/skirts",request,params) }
    # get "/kids/clothing/skirts(/colour-:colour)" =>"store#catalog2", :kind => "kids-skirts", :as => "kids_skirts_search"

    # get "/store/kids-frocks(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/frocks",request,params) }
    # get "/kids/clothing/frocks(/colour-:colour)" =>"store#catalog2", :kind => "kids-frocks", :as => "kids_frocks_search"

    # get "/store/kids-salwar-suits(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/salwar-suits",request,params) }
    # get "/kids/clothing/salwar-suits(/colour-:colour)" =>"store#catalog2", :kind => "kids-salwar-suits", :as => "kids_salwar_suits_search"

    # get "/store/ethnic-jackets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/ethnic-jackets",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/ethnic-jackets(/colour-:colour)" =>"store#catalog2", :kind => "ethnic-jackets", :as => "ethnic_jackets_search"

    # get "/store/cushion-covers(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/home-furnishing/cushion-covers",request,params) }
    # get "/home-decor/home-furnishing/cushion-covers(/colour-:colour)" =>"store#catalog2", :kind => "cushion-covers", :as => "cushion_covers_search"

    # get "/store/patialas-pants(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/patialas-pants",request,params) }
    # get "/salwar-suits/patialas-pants(/colour-:colour)" =>"store#catalog2", :kind => "patialas-pants", :as => "patialas_pants_search"

    # get "/store/men-jewellery(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/jewellery",request,params) }
    # get "/men/jewellery(/colour-:colour)" =>"store#catalog2", :kind => "men-jewellery", :as => "men_jewellery_search"

    # get "/store/men-earrings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/jewellery/earrings",request,params) }
    # get "/men/jewellery/earrings(/colour-:colour)" =>"store#catalog2", :kind => "men-earrings", :as => "men_earrings_search"

    # get "/store/men-studs(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/jewellery/earrings/studs",request,params) }
    # get "/men/jewellery/earrings/studs(/colour-:colour)" =>"store#catalog2", :kind => "men-studs", :as => "men_studs_search"

    # get "/store/men-bracelets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/jewellery/bracelets",request,params) }
    # get "/men/jewellery/bracelets(/colour-:colour)" =>"store#catalog2", :kind => "men-bracelets", :as => "men_bracelets_search"

    # get "/store/men-pendants(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/jewellery/pendants",request,params) }
    # get "/men/jewellery/pendants(/colour-:colour)" =>"store#catalog2", :kind => "men-pendants", :as => "men_pendants_search"

    # get "/store/men-rings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/jewellery/rings",request,params) }
    # get "/men/jewellery/rings(/colour-:colour)" =>"store#catalog2", :kind => "men-rings", :as => "men_rings_search"

    # get "/store/tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/tops",request,params) }
    # get "/women/clothing/tops(/colour-:colour)" =>"store#catalog2", :kind => "tops", :as => "tops_search"

    # get "/store/kaftans(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/kaftans",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/kaftans(/colour-:colour)" =>"store#catalog2", :kind => "kaftans", :as => "kaftans_search"

    # get "/store/wedding-dresses(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/wedding-dresses",request,params) }
    # get "/women/clothing/wedding-dresses(/colour-:colour)" =>"store#catalog2", :kind => "wedding-dresses", :as => "wedding_dresses_search"

    # get "/store/kids-lehenga-choli(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/lehenga-choli",request,params) }
    # get "/kids/clothing/lehenga-choli(/colour-:colour)" =>"store#catalog2", :kind => "kids-lehenga-choli", :as => "kids_lehenga_choli_search"

    # get "/kids/clothing/kids-lehenga-choli(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/lehenga-choli",request,params) }
    # get "/kids/clothing/lehenga-choli(/colour-:colour)" =>"store#catalog2", :kind => "kids-lehenga-choli", :as => "kids_lehenga_choli_search"

    # get "/store/kids-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/sarees",request,params) }
    # get "/kids/clothing/sarees(/colour-:colour)" =>"store#catalog2", :kind => "kids-sarees", :as => "kids_sarees_search"

    # get "/store/kids-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/kurtis",request,params) }
    # get "/kids/clothing/kurtis(/colour-:colour)" =>"store#catalog2", :kind => "kids-kurtis", :as => "kids_kurtis_search"

    # get "/kids/clothing/kids-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/sarees",request,params) }
    # get "/kids/clothing/sarees(/colour-:colour)" =>"store#catalog2", :kind => "kids-sarees", :as => "kids_sarees_search"

    # get "/kids/sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/sarees",request,params) }
    # get "/kids/clothing/sarees(/colour-:colour)" =>"store#catalog2", :kind => "kids-sarees", :as => "kids_sarees_search"

    # get "/store/kids(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids",request,params) }
    # get "/kids(/colour-:colour)" =>"store#catalog2", :kind => "kids", :as => "kids_search"

    # get "/store/western-wear(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/western-wear",request,params) }
    # get "/women/clothing/western-wear(/colour-:colour)" =>"store#catalog2", :kind => "western-wear", :as => "western_wear_search"

    # get "/store/party-tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/tops/party-tops",request,params) }
    # get "/women/clothing/tops/party-tops(/colour-:colour)" =>"store#catalog2", :kind => "party-tops", :as => "party_tops_search"

    # get "/store/kids-tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing/tops",request,params) }
    # get "/kids/clothing/tops(/colour-:colour)" =>"store#catalog2", :kind => "kids-tops", :as => "kids_tops_search"

    # get "/store/chiffon-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/chiffon-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/chiffon-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "chiffon-kurtis", :as => "chiffon_kurtis_search"

    # get "/store/leggings-combo(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/leggings-combo",request,params) }
    # get "/women/clothing/leggings-combo(/colour-:colour)" =>"store#catalog2", :kind => "leggings-combo", :as => "leggings_combo_search"

    # get "/women/jewellery/toe-rings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/rings/toe-rings",request,params) }
    # get "/women/jewellery/rings/toe-rings(/colour-:colour)" =>"store#catalog2", :kind => "toe-rings", :as => "toe_rings_search"

    # get "/store/ear-cuffs(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/earrings/ear-cuffs",request,params) }
    # get "/women/jewellery/earrings/ear-cuffs(/colour-:colour)" =>"store#catalog2", :kind => "ear-cuffs", :as => "ear_cuffs_search"

    # get "/store/below-300(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/below-300",request,params) }
    # get "/sarees/below-300(/colour-:colour)" =>"store#catalog2", :kind => "below-300", :as => "below_300_search"

    # get "/store/below-1500(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/below-1500",request,params) }
    # get "/sarees/below-1500(/colour-:colour)" =>"store#catalog2", :kind => "below-1500", :as => "below_1500_search"

    # get "/store/below-500(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/below-500",request,params) }
    # get "/sarees/below-500(/colour-:colour)" =>"store#catalog2", :kind => "below-500", :as => "below_500_search"

    # get "/store/below-400(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/below-400",request,params) }
    # get "/sarees/below-400(/colour-:colour)" =>"store#catalog2", :kind => "below-400", :as => "below_400_search"

    # get "/store/earrings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/earrings",request,params) }
    # get "/women/jewellery/earrings(/colour-:colour)" =>"store#catalog2", :kind => "earrings", :as => "earrings_search"

    # get "/store/salwars-and-churidars(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/salwars-and-churidars",request,params) }
    # get "/salwar-suits/salwars-and-churidars(/colour-:colour)" =>"store#catalog2", :kind => "salwars-and-churidars", :as => "salwar_churidar_salwar_search"

    # get "/store/punjabi-suits(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/punjabi-suits",request,params) }
    # get "/salwar-suits/punjabi-suits(/colour-:colour)" =>"store#catalog2", :kind => "punjabi-suits", :as => "punjabi_suits_search"

    # get "/store/chikankari-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/chikankari-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/chikankari-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "chikankari-kurtis", :as => "chikankari_kurtis_search"

    # get "/store/patiala-salwar(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/patiala-salwar",request,params) }
    # get "/salwar-suits/patiala-salwar(/colour-:colour)" =>"store#catalog2", :kind => "patiala-salwar", :as => "patiala_salwar_search"

    # get "/store/readymade-suits(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/readymade-suits",request,params) }
    # get "/salwar-suits/readymade-suits(/colour-:colour)" =>"store#catalog2", :kind => "readymade-suits", :as => "readymade_salwar_search"

    # get "/store/jhumkas(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/earrings/jhumkas",request,params) }
    # get "/women/jewellery/earrings/jhumkas(/colour-:colour)" =>"store#catalog2", :kind => "jhumkas", :as => "jhumkas_search"

    # get "/store/kurtas-and-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "kurtas-and-kurtis", :as => "kurtas_and_kurtis_search"

    # get "/store/studs(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/earrings/studs",request,params) }
    # get "/women/jewellery/earrings/studs(/colour-:colour)" =>"store#catalog2", :kind => "studs", :as => "studs_search"

    # get "/store/hoops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/earrings/hoops",request,params) }
    # get "/women/jewellery/earrings/hoops(/colour-:colour)" =>"store#catalog2", :kind => "hoops", :as => "hoops_search"

    # get "/store/gifts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gifts",request,params) }
    # get "/gifts(/colour-:colour)" =>"store#catalog2", kind: "gifts", as: "gifts_search"

    # get "/store/georgette-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/georgette-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/georgette-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "georgette-kurtis", :as => "georgette_kurtis_search"

    # get "/store/cotton-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/cotton-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/cotton-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "cotton-kurtis", :as => "cotton_kurtis_search"

    # get "/store/party-wear-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/party-wear-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/party-wear-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "party-wear-kurtis", :as => "party_wear_kurtis_search"

    # get "/store/pakistani-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/pakistani-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/pakistani-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "pakistani-kurtis", :as => "pakistani_kurtis_search"

    # get "/store/short-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/short-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/short-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "short-kurtis", :as => "short_kurtis_search"

    # get "/store/trousers(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/trousers",request,params) }
    # get "/women/clothing/trousers(/colour-:colour)" =>"store#catalog2", :kind => "trousers", :as => "trousers_search"

    # get "/store/clothing(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/clothing",request,params) }
    # get "/kids/clothing(/colour-:colour)" =>"store#catalog2", :kind => "clothing", :as => "clothing_search"

    # get "/store/ethnic-suits(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/ethnic-suits",request,params) }
    # get "/salwar-suits/ethnic-suits(/colour-:colour)" =>"store#catalog2", :kind => "ethnic-suits", :as => "ethnic_suits_search"

    # get "/store/ethnic-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/ethnic-sarees",request,params) }
    # get "/sarees/ethnic-sarees(/colour-:colour)" =>"store#catalog2", :kind => "ethnic-sarees", :as => "ethnic_sarees_search"

    # get "/store/ethnic-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/ethnic-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/ethnic-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "ethnic-kurtis", :as => "ethnic_kurtis_search"

    # get "/store/palazzo-pants(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/trousers/palazzo-pants",request,params) }
    # get "/women/clothing/trousers/palazzo-pants(/colour-:colour)" =>"store#catalog2", :kind => "palazzo-pants", :as => "palazzo_pants_search"

    # get "/store/party-jewellery(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/party-jewellery",request,params) }
    # get "/women/jewellery/party-jewellery(/colour-:colour)" =>"store#catalog2", :kind => "party-jewellery", :as => "party_jewellery_search"

    # get "/store/designer-jewellery(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/designer-jewellery",request,params) }
    # get "/women/jewellery/designer-jewellery(/colour-:colour)" =>"store#catalog2", :kind => "designer-jewellery", :as => "designer_jewellery_search"

    # get "/store/uppada-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/uppada-sarees",request,params) }
    # get "/sarees/uppada-sarees(/colour-:colour)" =>"store#catalog2", :kind => "uppada-sarees", :as => "uppada_sarees_search"

    # get "/store/danglers-drops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/earrings/danglers-drops",request,params) }
    # get "/women/jewellery/earrings/danglers-drops(/colour-:colour)" =>"store#catalog2", :kind => "danglers-drops", :as => "danglers_drops_search"

    # get "/store/hair-pins(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/hair-accessories",request,params) }

    get "/pages/careers" =>redirect("http://www.applicanttrackingsystem.co/Careers/Mirraw%20Online%20Services%20Pvt%20Ltd")

    # get "/store/hair-accessories(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/hair-accessories",request,params) }
    # get "/women/jewellery/hair-accessories(/colour-:colour)" =>"store#catalog2", :kind => "hair-accessories", :as => "hair_accessories_search"

    # get "/store/haath-phool-hath-panja(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/haath-phool-hath-panja",request,params) }
    # get "/women/jewellery/haath-phool-hath-panja(/colour-:colour)" =>"store#catalog2", :kind => "haath-phool-hath-panja", :as => "haath_phool_hath_panja_search"

    # get "/store/half-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/half-sarees",request,params) }
    # get "/sarees/half-sarees(/colour-:colour)" =>"store#catalog2", :kind => "half-sarees", :as => "half_sarees_search"

    # get "/store/bengali-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/bengali-sarees",request,params) }
    # get "/sarees/bengali-sarees(/colour-:colour)" =>"store#catalog2", :kind => "bengali-sarees", :as => "bengali_sarees_search"

    # get "/store/handloom-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/handloom-sarees",request,params) }
    # get "/sarees/handloom-sarees(/colour-:colour)" =>"store#catalog2", :kind => "handloom-sarees", :as => "handloom_sarees_search"

    # get "/store/patola-saris(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/patola-saris",request,params) }
    # get "/sarees/patola-saris(/colour-:colour)" =>"store#catalog2", :kind => "patola-saris", :as => "patola_saris_search"

    # get "/store/pre-stitched-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/pre-stitched-sarees",request,params) }
    # get "/sarees/pre-stitched-sarees(/colour-:colour)" =>"store#catalog2", :kind => "pre-stitched-sarees", :as => "pre_stitched_sarees_search"

    # get "/store/musical-instruments(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/products/musical-instruments",request,params) }
    # get "/products/musical-instruments(/colour-:colour)" =>"store#catalog2", :kind => "musical-instruments", :as => "musical_instruments_search"

    # get "/store/laces(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/accessories/laces",request,params) }
    # get "/women/accessories/laces(/colour-:colour)" =>"store#catalog2", :kind => "laces", :as => "laces_search"

    # get "/store/bags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags",request,params) }
    # get "/women/bags(/colour-:colour)" =>"store#catalog2", :kind => "bags", :as => "bags_search"

    # get "/store/handbags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/handbags",request,params) }
    # get "/women/bags/handbags(/colour-:colour)" =>"store#catalog2", :kind => "handbags", :as => "handbags_search"

    # get "/store/clutches(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/clutches",request,params) }
    # get "/women/bags/clutches(/colour-:colour)" =>"store#catalog2", :kind => "clutches", :as => "clutches_search"

    # get "/store/wallets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/wallets",request,params) }
    # get "/women/bags/wallets(/colour-:colour)" =>"store#catalog2", :kind => "wallets", :as => "wallets_search"

    # get "/store/tote-bags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/tote-bags",request,params) }
    # get "/women/bags/tote-bags(/colour-:colour)" =>"store#catalog2", :kind => "tote-bags", :as => "tote_bags_search"

    # get "/store/backpacks(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/backpacks",request,params) }
    # get "/women/bags/backpacks(/colour-:colour)" =>"store#catalog2", :kind => "backpacks", :as => "backpacks_search"

    # get "/store/sling-bags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/sling-bags",request,params) }
    # get "/women/bags/sling-bags(/colour-:colour)" =>"store#catalog2", :kind => "sling-bags", :as => "sling_bags_search"

    # get "/store/potli-bags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/potli-bags",request,params) }
    # get "/women/bags/potli-bags(/colour-:colour)" =>"store#catalog2", :kind => "potli-bags", :as => "potli_bags_search"

    # get "/store/jute-bags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/jute-bags",request,params) }
    # get "/women/bags/jute-bags(/colour-:colour)" =>"store#catalog2", :kind => "jute-bags", :as => "jute_bags_search"

    # get "/store/bangle-bags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/bangle-bags",request,params) }
    # get "/women/bags/bangle-bags(/colour-:colour)" =>"store#catalog2", :kind => "bangle-bags", :as => "bangle_bags_search"

    # get "/store/designer-bags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/designer-bags",request,params) }
    # get "/women/bags/designer-bags(/colour-:colour)" =>"store#catalog2", :kind => "designer-bags", :as => "designer_bags_search"

    # get "/store/leather-bags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/leather-bags",request,params) }
    # get "/women/bags/leather-bags(/colour-:colour)" =>"store#catalog2", :kind => "leather-bags", :as => "leather_bags_search"

    # get "/store/jhola-bags(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/bags/jhola-bags",request,params) }
    # get "/women/bags/jhola-bags(/colour-:colour)" =>"store#catalog2", :kind => "jhola-bags", :as => "jhola_bags_search"

    # get "/store/religious-items(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gifts/religious-items",request,params) }
    # get "/gifts/religious-items(/colour-:colour)" =>"store#catalog2", :kind => "religious-items", :as => "religious_items_search"

    # get "/store/long-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/long-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/long-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "long-kurtis", :as => "long_kurtis_search"

    # get "/store/kota-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/kota-saree",request,params) }
    # get "/store/kota-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/kota-saree",request,params) }
    # get "/sarees/kota-saree(/colour-:colour)" =>"store#catalog2", :kind => "kota-saree", :as => "kota_sarees_search"

    # get "/salwar-suits(/colour-:colour)" =>"store#catalog2", :kind => ""

    # get "/kids/sarees(/colour-:colour)" =>"store#catalog2", :kind => "kids-sarees", :as => "kids_sarees_search"

    # get "/store/south-indian-jewellery(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/south-indian-jewellery",request,params) }
    # get "/women/jewellery/south-indian-jewellery(/colour-:colour)" =>"store#catalog2", :kind => "south-indian-jewellery", :as => "south_india_jewellery_search"

    # get "/store/terracotta-jewelry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/terracotta-jewelry",request,params) }
    # get "/women/jewellery/terracotta-jewelry(/colour-:colour)" =>"store#catalog2", :kind => "terracotta-jewelry", :as => "terracotta_jewelry_search"

    # get "/store/eid-sarees-dresses(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/eid-sarees-dresses",request,params) }
    # get "/sarees/eid-sarees-dresses(/colour-:colour)" =>"store#catalog2", :kind => "eid-sarees-dresses", :as => "eid_sarees_dresses_search"

    # get "/store/eid-special-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar/eid-special-salwar-kameez",request,params) }
    # get "/salwar/eid-special-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "eid-special-salwar-kameez", :as => "eid_special_salwar_kameez_search"

    # get "/store/tamanna-bhatia-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/tamanna-bhatia-sarees",request,params) }
    # get "/sarees/tamanna-bhatia-sarees(/colour-:colour)" =>"store#catalog2", :kind => "tamanna-bhatia-sarees", :as => "tamanna_bhatia_sarees_search"

    # get "/store/chikankari-saris(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/chikankari-saris",request,params) }
    # get "/sarees/chikankari-saris(/colour-:colour)" =>"store#catalog2", :kind => "chikankari-saris", :as => "chikankari_sarees_search"

    # get "/store/diwali-discount-offers-2013(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/store/diwali-discount-offers",request,params) }

    # get "/store/christmas-gifts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/festival/christmas-gifts",request,params) }
    # get "/festival/christmas-gifts(/colour-:colour)" =>"store#catalog2", :kind => "christmas-gifts", :as => "christmas_gifts_search"

    # get "/store/diwali-gifts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/festival/diwali-gifts",request,params) }
    # get "/festival/diwali-gifts(/colour-:colour)" =>"store#catalog2", :kind => "diwali-gifts", :as => "diwali_gifts_search"

    # get "/store/ramadan-gifts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/festival/ramadan-gifts",request,params) }
    # get "/festival/ramadan-gifts(/colour-:colour)" =>"store#catalog2", :kind => "ramadan-gifts", :as => "ramadan_gifts_search"

    # get "/store/holi-offers(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/festival/holi-offers",request,params) }
    # get "/festival/holi-offers(/colour-:colour)" =>"store#catalog2", :kind => "holi-offers", :as => "holi_offers_search"

    # get "/store/gudi-padwa-offers(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/festival/gudi-padwa-offers",request,params) }
    # get "/festival/gudi-padwa-offers(/colour-:colour)" =>"store#catalog2", :kind => "gudi-padwa-offers", :as => "gudi_padwa_offers_search"

    # get "/store/eid-gift(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/festival/eid-gift",request,params) }
    # get "/festival/eid-gift(/colour-:colour)" =>"store#catalog2", :kind => "eid-gift", :as => "eid_gift_search"

    # get "/store/abaya(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/islamic-clothing/abaya",request,params) }
    # get "/islamic-clothing/abaya(/colour-:colour)" =>"store#catalog2", :kind => "abaya", :as => "abaya_search"

    # get "/store/lycra-abaya(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/islamic-clothing/abaya/lycra",request,params) }
    # get "/islamic-clothing/abaya/lycra(/colour-:colour)" =>"store#catalog2", :kind => "lycra-abaya", :as => "lycra_abaya_search"

    # get "/store/crystal-abaya(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/islamic-clothing/abaya/crystal",request,params) }
    # get "/islamic-clothing/abaya/crystal(/colour-:colour)" =>"store#catalog2", :kind => "crystal-abaya", :as => "crystal_abaya_search"

    # get "/store/readymade-abaya(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/islamic-clothing/abaya/readymade",request,params) }
    # get "/islamic-clothing/abaya/readymade(/colour-:colour)" =>"store#catalog2", :kind => "readymade-abaya", :as => "readymade_abaya_search"

    # get "/store/paithani-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/paithani-saree",request,params) }
    # get "/sarees/paithani-saree(/colour-:colour)" =>"store#catalog2", :kind => "paithani-saree", :as => "paithani_saree_search"

    # get "/store/linen-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/linen-saree",request,params) }
    # get "/sarees/linen-saree(/colour-:colour)" =>"store#catalog2", :kind => "linen-saree", :as => "linen_saree_search"

    # get "/store/phulkari-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/phulkari-saree",request,params) }
    # get "/sarees/phulkari-saree(/colour-:colour)" =>"store#catalog2", :kind => "phulkari-saree", :as => "phulkari_saree_search"

    # get "/store/designer-embroidered-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/designer-embroidered-sarees",request,params) }
    # get "/sarees/designer-embroidered-sarees(/colour-:colour)" =>"store#catalog2", :kind => "designer-embroidered-sarees", :as => "embroidered_saree_search"

    # get "/store/tunics(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/store/women-tunics",request,params) }
    # get "/store/women-tunics(/colour-:colour)" =>"store#catalog2", :kind => "tunics", :as => "women_tunics_search"

    # get "/store/rakhi(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rakhi-gifts/rakhi-online",request,params) }
    # get "/store/rakhi-hampers(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/store/rakhi-gift-hampers",request,params) }

    # get "/store/rakhi-online(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rakhi-gifts/rakhi-online",request,params) }
    # get "/rakhi-gifts/rakhi-online(/colour-:colour)" =>"store#catalog2", :kind => "rakhi-online", :as => "rakhi_online_search"

    # get "/store/kids-rakhi(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rakhi-gifts/kids-rakhi",request,params) }
    # get "/rakhi-gifts/kids-rakhi(/colour-:colour)" =>"store#catalog2", :kind => "kids-rakhi", :as => "kids_rakhi_search"

    # get "/store/rakhi-with-chocalates(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/chocolate/rakhi-with-chocolates",request,params) }
    # get "/store/rakhi-with-chocolates(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/chocolate/rakhi-with-chocolates",request,params) }
    # get "/chocolate/rakhi-with-chocolates(/colour-:colour)" =>"store#catalog2", :kind => "rakhi-with-chocolates", :as => "rakhi_chocolates_search"

    # get "/store/rakhi-gifts-for-sister(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rakhigifts/rakhi-gifts-for-sister",request,params) }
    # get "/rakhigifts/rakhi-gifts-for-sister(/colour-:colour)" =>"store#catalog2", :kind => "rakhi-gifts-for-sister", :as => "rakhi_sister_search"

    # get "/store/rakhi-gifts-for-brother(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rakhigifts/rakhi-gifts-for-brother",request,params) }
    # get "/rakhigifts/rakhi-gifts-for-brother(/colour-:colour)" =>"store#catalog2", :kind => "rakhi-gifts-for-brother", :as => "rakhi_brother_search"

    # get "/store/diwali-sarees-collection-2013(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/store/diwali-sarees-collection",request,params) }
    # get "/store/dry-fruits(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/store/diwali-dry-fruits",request,params) }
    # get "/store/puja-thalis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/store/diwali-puja-thalis",request,params) }

    # get "/store/great-online-shopping-festival-2014(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gosf-offers/great-online-shopping-festival-2014",request,params) }
    # get "/gosf-offers/great-online-shopping-festival-2014(/colour-:colour)" =>"store#catalog2", :kind => "great-online-shopping-festival-2014", :as => "gosf_search"

    # get "/store/curated-jewelry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/curated-jewelry",request,params) }
    # get "/women/jewellery/curated-jewelry(/colour-:colour)" =>"store#catalog2", :kind => "curated-jewelry", :as => "curated_jewelry_search"

    # get "/store/collar-neck-designs(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/collar-neck-designs",request,params) }
    # get "/salwar-suits/collar-neck-designs(/colour-:colour)" =>"store#catalog2", :kind => "collar-neck-designs", :as => "collar_neck_designs_search"

    # get "/store/indian-dresses(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/indian-dresses",request,params) }
    # get "/women/clothing/indian-dresses(/colour-:colour)" =>"store#catalog2", :kind => "indian-dresses", :as => "indian_dresses_search"

    # get "/store/evening-wear-dresses(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/evening-wear-dresses",request,params) }
    # get "/women/clothing/evening-wear-dresses(/colour-:colour)" =>"store#catalog2", :kind => "evening-wear-dresses", :as => "evening_wear_dresses_search"

    # get "/store/party-wear-gowns(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/party-wear-gowns",request,params) }
    # get "/women/clothing/party-wear-gowns(/colour-:colour)" =>"store#catalog2", :kind => "party-wear-gowns", :as => "party_wear_gowns_search"

    # get "/store/men-apparel(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing",request,params) }
    # get "/store/men-clothing(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing",request,params) }
    # get "/men/clothing(/colour-:colour)" =>"store#catalog2", :kind => "men-apparel", :as => "men_apparel_search"

    # get "/store/kurta-pajama(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/kurta-pajama",request,params) }
    # get "/men/clothing/kurta-pajama(/colour-:colour)" =>"store#catalog2", :kind => "kurta-pajama", :as => "men_kurta_pajama_search"

    # get "/store/sherwani(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/sherwani",request,params) }
    # get "/men/clothing/sherwani(/colour-:colour)" =>"store#catalog2", :kind => "sherwani", :as => "men_sherwani_search"

    # get "/store/indo-western-dresses(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/indo-western-dresses",request,params) }
    # get "/men/clothing/indo-western-dresses(/colour-:colour)" =>"store#catalog2", :kind => "indo-western-dresses", :as => "men_indo_western_dresses_search"

    # get "/store/ethnic-wear(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/ethnic-wear",request,params) }
    # get "/men/clothing/ethnic-wear(/colour-:colour)" =>"store#catalog2", :kind => "ethnic-wear", :as => "men_ethnic_wear_search"

    # get "/store/nehru-jacket(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/nehru-jacket",request,params) }
    # get "/men/clothing/nehru-jacket(/colour-:colour)" =>"store#catalog2", :kind => "nehru-jacket", :as => "nehru_jacket_search"

    # get "/store/turbans(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/turbans",request,params) }
    # get "/men/clothing/turbans(/colour-:colour)" =>"store#catalog2", :kind => "turbans", :as => "turbans_search"

    # get "/collections/gisf(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/store/sarees",request,params) }
    # get "/store/sarees(/colour-:colour)" =>"store#catalog2", :kind => "sarees", :as => "sarees_search"

    # get "/store/eid-jewellery(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/festival/eid-jewellery",request,params) }
    # get "/festival/eid-jewellery(/colour-:colour)" =>"store#catalog2", :kind => "eid-jewellery", :as => "eid_jewellery_search"

    # get "/store/toe-rings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/rings/toe-rings",request,params) }
    # get "/women/jewellery/rings/toe-rings(/colour-:colour)" =>"store#catalog2", :kind => "toe-rings", :as => "toe_rings_search"

    # get "/store/rings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/rings",request,params) }
    # get "/women/jewellery/rings(/colour-:colour)" =>"store#catalog2", :kind => "rings", :as => "rings_search"

    # get "/store/engagement-rings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/rings/engagement-rings",request,params) }
    # get "/women/jewellery/rings/engagement-rings(/colour-:colour)" =>"store#catalog2", :kind => "engagement-rings", :as => "engagement_rings_search"

    # get "/store/wedding-rings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/rings/wedding-rings",request,params) }
    # get "/women/jewellery/rings/wedding-rings(/colour-:colour)" =>"store#catalog2", :kind => "wedding-rings", :as => "wedding_rings_search"

    # get "/store/nose-ring(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/rings/nose-ring",request,params) }
    # get "/women/jewellery/rings/nose-ring(/colour-:colour)" =>"store#catalog2", :kind => "nose-ring", :as => "nose_ring_search"

    # get "/store/pakistani-jewellery(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/pakistani-jewellery",request,params) }
    # get "/women/jewellery/pakistani-jewellery(/colour-:colour)" =>"store#catalog2", :kind => "pakistani-jewellery", :as => "pakistani_jewellery_search"

    # get "/store/punjabi-jewellery(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/punjabi-jewellery",request,params) }
    # get "/women/jewellery/punjabi-jewellery(/colour-:colour)" =>"store#catalog2", :kind => "punjabi-jewellery", :as => "punjabi_jewellery_search"

    # get "/store/scarves(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/accessories/scarves",request,params) }
    # get "/women/accessories/scarves(/colour-:colour)" =>"store#catalog2", :kind => "scarves", :as => "scarves_search"

    # get "/store/harem-pants(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/harem-pants",request,params) }
    # get "/women/clothing/harem-pants(/colour-:colour)" =>"store#catalog2", :kind => "harem-pants", :as => "harem_pants_search"

    # get "/store/gadwal-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/gadwal-sarees",request,params) }
    # get "/sarees/gadwal-sarees(/colour-:colour)" =>"store#catalog2", :kind => "gadwal-sarees", :as => "gadwal_sarees_search"

    # get "/store/durga-puja(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/durga-puja",request,params) }
    # get "/sarees/durga-puja(/colour-:colour)" =>"store#catalog2", :kind => "durga-puja", :as => "durga_puja_search"

    # get "/store/accessories(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/accessories",request,params) }
    # get "/men/accessories(/colour-:colour)" =>"store#catalog2", :kind => "accessories", :as => "accessories_search"

    # get "/store/jamdani-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/jamdani-sarees",request,params) }
    # get "/sarees/jamdani-sarees(/colour-:colour)" =>"store#catalog2", :kind => "jamdani-sarees", :as => "jamdani_sarees_search"

    # get "/store/footwear(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/footwear",request,params) }
    # get "/women/footwear(/colour-:colour)" =>"store#catalog2", :kind => "footwear", :as => "footwear_search"

    # get "/store/bindis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/accessories/makeup/bindis",request,params) }
    # get "/women/accessories/makeup/bindis(/colour-:colour)" =>"store#catalog2", :kind => "bindis", :as => "bindis_search"

    # get "/store/rajasthani-sherwani(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/sherwani/rajasthani-sherwani",request,params) }
    # get "/men/clothing/sherwani/rajasthani-sherwani(/colour-:colour)" =>"store#catalog2", :kind => "rajasthani-sherwani", :as => "rajasthani_sherwani_search"

    # get "/store/pakistani-sherwani(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/sherwani/pakistani-sherwani",request,params) }
    # get "/men/clothing/sherwani/pakistani-sherwani(/colour-:colour)" =>"store#catalog2", :kind => "pakistani-sherwani", :as => "pakistani_sherwani_search"

    # get "/store/wedding-sherwani(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/sherwani/wedding-sherwani",request,params) }
    # get "/men/clothing/sherwani/wedding-sherwani(/colour-:colour)" =>"store#catalog2", :kind => "wedding-sherwani", :as => "wedding_sherwani_search"

    # get "/store/jodhpuri-sherwani(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/sherwani/jodhpuri-sherwani",request,params) }
    # get "/men/clothing/sherwani/jodhpuri-sherwani(/colour-:colour)" =>"store#catalog2", :kind => "jodhpuri-sherwani", :as => "jodhpuri_sherwani_search"

    # get "/store/pathani-sherwani(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/sherwani/pathani-sherwani",request,params) }
    # get "/men/clothing/sherwani/pathani-sherwani(/colour-:colour)" =>"store#catalog2", :kind => "pathani-sherwani", :as => "pathani_sherwani_search"

    # get "/store/achkan(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/sherwani/achkan",request,params) }
    # get "/men/clothing/sherwani/achkan(/colour-:colour)" =>"store#catalog2", :kind => "achkan", :as => "achkan_search"

    # get "/store/south-indian-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/south-indian-sarees",request,params) }
    # get "/sarees/south-indian-sarees(/colour-:colour)" =>"store#catalog2", :kind => "south-indian-sarees", :as => "south_indian_sarees_search"

    # get "/store/ileana-dcruz-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/ileana-dcruz-sarees",request,params) }
    # get "/sarees/ileana-dcruz-sarees(/colour-:colour)" =>"store#catalog2", :kind => "ileana-dcruz-sarees", :as => "ileana_dcruz_sarees_search"

    # get "/store/jacqueline-fernandez-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/jacqueline-fernandez-sarees",request,params) }
    # get "/sarees/jacqueline-fernandez-sarees(/colour-:colour)" =>"store#catalog2", :kind => "jacqueline-fernandez-sarees", :as => "jacqueline_fernandez_sarees_search"

    # get "/store/kajal-agarwal-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/kajal-agarwal-sarees",request,params) }
    # get "/sarees/kajal-agarwal-sarees(/colour-:colour)" =>"store#catalog2", :kind => "kajal-agarwal-sarees", :as => "kajal_agarwal_sarees_search"

    # get "/store/asin-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/asin-sarees",request,params) }
    # get "/sarees/asin-sarees(/colour-:colour)" =>"store#catalog2", :kind => "asin-sarees", :as => "asin_sarees_search"

    # get "/store/ayesha-takia-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/ayesha-takia-saree",request,params) }
    # get "/sarees/ayesha-takia-saree(/colour-:colour)" =>"store#catalog2", :kind => "ayesha-takia-saree", :as => "ayesha_takia_saree_search"

    # get "/store/ayesha-takia-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/ayesha-takia-salwar-kameez",request,params) }
    # get "/salwar-suits/ayesha-takia-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "ayesha-takia-salwar-kameez", :as => "ayesha_takia_salwar_kameez_search"

    # get "/store/chitrangada-singh-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/chitrangada-singh-saree",request,params) }
    # get "/sarees/chitrangada-singh-saree(/colour-:colour)" =>"store#catalog2", :kind => "chitrangada-singh-saree", :as => "chitrangada_singh_saree_search"

    # get "/store/diana-penty-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/diana-penty-sarees",request,params) }
    # get "/sarees/diana-penty-sarees(/colour-:colour)" =>"store#catalog2", :kind => "diana-penty-sarees", :as => "diana_penty_sarees_search"

    # get "/store/evelyn-sharma-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/evelyn-sharma-sarees",request,params) }
    # get "/sarees/evelyn-sharma-sarees(/colour-:colour)" =>"store#catalog2", :kind => "evelyn-sharma-sarees", :as => "evelyn_sharma_sarees_search"

    # get "/store/evelyn-sharma-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/evelyn-sharma-salwar-kameez",request,params) }
    # get "/salwar-suits/evelyn-sharma-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "evelyn-sharma-salwar-kameez", :as => "evelyn_sharma_salwar_kameez_search"

    # get "/store/esha-gupta-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/esha-gupta-sarees",request,params) }
    # get "/sarees/esha-gupta-sarees(/colour-:colour)" =>"store#catalog2", :kind => "esha-gupta-sarees", :as => "esha_gupta_sarees_search"

    # get "/store/huma-qureshi-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/huma-qureshi-sarees",request,params) }
    # get "/sarees/huma-qureshi-sarees(/colour-:colour)" =>"store#catalog2", :kind => "huma-qureshi-sarees", :as => "huma_qureshi_sarees_search"

    # get "/store/eid-abayas(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/islamic-clothing/abaya/eid-abayas",request,params) }
    # get "/islamic-clothing/abaya/eid-abayas(/colour-:colour)" =>"store#catalog2", :kind => "eid-abayas", :as => "eid_abayas_search"

    # get "/store/kriti-sanon-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/kriti-sanon-sarees",request,params) }
    # get "/sarees/kriti-sanon-sarees(/colour-:colour)" =>"store#catalog2", :kind => "kriti-sanon-sarees", :as => "kriti_sanon_sarees_search"

    # get "/store/collar-necklace(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/necklaces/collar-necklace",request,params) }
    # get "/women/jewellery/necklaces/collar-necklace(/colour-:colour)" =>"store#catalog2", :kind => "collar-necklace", :as => "collar_necklace_search"

    # get "/store/patiala-combo(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/patiala-combo",request,params) }
    # get "/salwar-suits/patiala-combo(/colour-:colour)" =>"store#catalog2", :kind => "patiala-combo", :as => "patiala_combo_search"

    # get "/store/nightwear(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/nightwear",request,params) }
    # get "/women/clothing/nightwear(/colour-:colour)" =>"store#catalog2", :kind => "nightwear", :as => "nightwear_search"

    # get "/store/neha-sharma-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/neha-sharma-sarees",request,params) }
    # get "/sarees/neha-sharma-sarees(/colour-:colour)" =>"store#catalog2", :kind => "neha-sharma-sarees", :as => "neha_sharma_sarees_search"

    # get "/store/nargis-fakhri-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/nargis-fakhri-saree",request,params) }
    # get "/sarees/nargis-fakhri-saree(/colour-:colour)" =>"store#catalog2", :kind => "nargis-fakhri-saree", :as => "nargis_fakhri_saree_search"

    # get "/store/parineeti-chopra-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/parineeti-chopra-sarees",request,params) }
    # get "/sarees/parineeti-chopra-sarees(/colour-:colour)" =>"store#catalog2", :kind => "parineeti-chopra-sarees", :as => "parineeti_chopra_sarees_search"

    # get "/store/boys-shirts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/boys/clothing/boys-shirts",request,params) }
    # get "/kids/boys/clothing/boys-shirts(/colour-:colour)" =>"store#catalog2", :kind => "boys-shirts", :as => "boys_shirts_search"

    # get "/store/boys-shorts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/boys/clothing/boys-shorts",request,params) }
    # get "/kids/boys/clothing/boys-shorts(/colour-:colour)" =>"store#catalog2", :kind => "boys-shorts", :as => "boys_shorts_search"

    # get "/store/girls-jackets-coats(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kids/girls/clothing/girls-jackets-coats",request,params) }
    # get "/kids/girls/clothing/girls-jackets-coats(/colour-:colour)" =>"store#catalog2", :kind => "girls-jackets-coats", :as => "girls_jackets_coats_search"

    # get "/store/indowestern(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/indowestern",request,params) }
    # get "/women/clothing/indowestern(/colour-:colour)" =>"store#catalog2", :kind => "indowestern", :as => "indowestern_search"

    # get "/store/soha-ali-khan-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/soha-ali-khan-saree",request,params) }
    # get "/sarees/soha-ali-khan-saree(/colour-:colour)" =>"store#catalog2", :kind => "soha-ali-khan-saree", :as => "soha_ali_khan_saree_search"

    # get "/store/sonal-chauhan-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/sonal-chauhan-sarees",request,params) }
    # get "/sarees/sonal-chauhan-sarees(/colour-:colour)" =>"store#catalog2", :kind => "sonal-chauhan-sarees", :as => "sonal_chauhan_sarees_search"

    # get "/store/kajol-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/kajol-sarees",request,params) }
    # get "/sarees/kajol-sarees(/colour-:colour)" =>"store#catalog2", :kind => "kajol-sarees", :as => "kajol_sarees_search"

    # get "/store/sushmita-sen-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/sushmita-sen-sarees",request,params) }
    # get "/sarees/sushmita-sen-sarees(/colour-:colour)" =>"store#catalog2", :kind => "sushmita-sen-sarees", :as => "sushmita_sen_sarees_search"

    # get "/store/sunny-leone-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/sunny-leone-sarees",request,params) }
    # get "/sarees/sunny-leone-sarees(/colour-:colour)" =>"store#catalog2", :kind => "sunny-leone-sarees", :as => "sunny_leone_sarees_search"

    # get "/store/sunny-leone-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/sunny-leone-salwar-kameez",request,params) }
    # get "/salwar-suits/sunny-leone-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "sunny-leone-salwar-kameez", :as => "sunny_leone_salwar_kameez_search"

    # get "/store/yami-gautam-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/yami-gautam-sarees",request,params) }
    # get "/sarees/yami-gautam-sarees(/colour-:colour)" =>"store#catalog2", :kind => "yami-gautam-sarees", :as => "yami_gautam_sarees_search"

    # get "/store/zarine-khan-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/zarine-khan-sarees",request,params) }
    # get "/sarees/zarine-khan-sarees(/colour-:colour)" =>"store#catalog2", :kind => "zarine-khan-sarees", :as => "zarine_khan_sarees_search"

    # get "/store/salwar-kameez-below-300(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/salwar-kameez-below-300",request,params) }
    # get "/salwar-suits/salwar-kameez-below-300(/colour-:colour)" =>"store#catalog2", :kind => "salwar-kameez-below-300", :as => "salwar_kameez_below_300_search"

    # get "/store/salwar-kameez-below-1000(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/salwar-kameez-below-1000",request,params) }
    # get "/salwar-suits/salwar-kameez-below-1000(/colour-:colour)" =>"store#catalog2", :kind => "salwar-kameez-below-1000", :as => "salwar_kameez_below_1000_search"

    # get "/store/salwar-kameez-below-500(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/salwar-kameez-below-500",request,params) }
    # get "/salwar-suits/salwar-kameez-below-500(/colour-:colour)" =>"store#catalog2", :kind => "salwar-kameez-below-500", :as => "salwar_kameez_below_500_search"

    # get "/store/salwar-kameez-below-2000(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/salwar-kameez-below-2000",request,params) }
    # get "/salwar-suits/salwar-kameez-below-2000(/colour-:colour)" =>"store#catalog2", :kind => "salwar-kameez-below-2000", :as => "salwar_kameez_below_2000_search"

    # get "/store/kurtis-below-250(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/below-250",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/below-250(/colour-:colour)" =>"store#catalog2", :kind => "kurtis-below-250", :as => "kurtis_below_250_search"

    # get "/store/kurtis-below-300(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/below-300",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/below-300(/colour-:colour)" =>"store#catalog2", :kind => "kurtis-below-300", :as => "kurtis_below_300_search"

    # get "/store/kurtis-below-500(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/below-500",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/below-500(/colour-:colour)" =>"store#catalog2", :kind => "kurtis-below-500", :as => "kurtis_below_500_search"

    # get "/store/kurtis-below-400(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/below-400",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/below-400(/colour-:colour)" =>"store#catalog2", :kind => "kurtis-below-400", :as => "kurtis_below_400_search"

    # get "/store/fancy-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/fancy-sarees",request,params) }
    # get "/sarees/fancy-sarees(/colour-:colour)" =>"store#catalog2", :kind => "fancy-sarees", :as => "fancy_sarees_search"

    # get "/store/women-accessories(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/accessories",request,params) }
    # get "/women/accessories(/colour-:colour)" =>"store#catalog2", :kind => "women-accessories", :as => "women_accessories_search"

    # get "/store/combo-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/combo-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/combo-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "combo-kurtis", :as => "combo_kurtis_search"

    # get "/store/rubber-hair-bands(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/accessories/rubber-hair-bands",request,params) }
    # get "/women/accessories/rubber-hair-bands(/colour-:colour)" =>"store#catalog2", :kind => "rubber-hair-bands", :as => "rubber_hair_bands_search"

    # get "/store/men-jeans(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/jeans",request,params) }
    # get "/men/clothing/jeans(/colour-:colour)" =>"store#catalog2", :kind => "men-jeans", :as => "men_jeans_search"

    # get "/store/men-tshirts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/tshirts",request,params) }
    # get "/men/clothing/tshirts(/colour-:colour)" =>"store#catalog2", :kind => "men-tshirts", :as => "men_tshirts_search"

    # get "/store/jewellery-below-100(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/jewellery/below-100",request,params) }
    # get "/jewellery/below-100(/colour-:colour)" =>"store#catalog2", :kind => "jewellery-below-100", :as => "jewellery_below_100_search"

    # get "/store/jewellery-below-200(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/jewellery/below-200",request,params) }
    # get "/jewellery/below-200(/colour-:colour)" =>"store#catalog2", :kind => "jewellery-below-200", :as => "jewellery_below_200_search"

    # get "/store/jewellery-below-300(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/jewellery/below-300",request,params) }
    # get "/jewellery/below-300(/colour-:colour)" =>"store#catalog2", :kind => "jewellery-below-300", :as => "jewellery_below_300_search"

    # get "/store/jewellery-below-500(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/jewellery/below-500",request,params) }
    # get "/jewellery/below-500(/colour-:colour)" =>"store#catalog2", :kind => "jewellery-below-500", :as => "jewellery_below_500_search"

    # get "/store/nail-art(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/accessories/makeup/nail-art",request,params) }
    # get "/women/accessories/makeup/nail-art(/colour-:colour)" =>"store#catalog2", :kind => "nail-art", :as => "nail_art_search"

    # get "/store/multicolor-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/multicolor-sarees",request,params) }
    # get "/sarees/multicolor-sarees(/colour-:colour)" =>"store#catalog2", :kind => "multicolor-sarees", :as => "multicolor_sarees_search"

    # get "/store/multicolor-salwar-kameez(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/salwar-suits/multicolor-salwar-kameez",request,params) }
    # get "/salwar-suits/multicolor-salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "multicolor-salwar-kameez", :as => "multicolor_salwar_kameez_search"

    # get "/store/multicolor-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/multicolor-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/multicolor-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "multicolor-kurtis", :as => "multicolor_kurtis_search"

    # get "/store/combo-jeans(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/jeans/combo-jeans",request,params) }
    # get "/men/clothing/jeans/combo-jeans(/colour-:colour)" =>"store#catalog2", :kind => "combo-jeans", :as => "combo_jeans_search"

    # get "/store/combo-pants(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/trousers/combo-pants",request,params) }
    # get "/men/clothing/trousers/combo-pants(/colour-:colour)" =>"store#catalog2", :kind => "combo-pants", :as => "combo_pants_search"

    # get "/store/skirts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/skirts",request,params) }
    # get "/women/clothing/skirts(/colour-:colour)" =>"store#catalog2", :kind => "skirts", :as => "skirts_search"

    # get "/store/heavy-work-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/heavy-work-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/heavy-work-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "heavy-work-kurtis", :as => "heavy_work_kurtis_search"

    # get "/store/silk-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/silk-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/silk-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "silk-kurtis", :as => "silk_kurtis_search"

    # get "/store/embroidered-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/embroidered-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/embroidered-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "embroidered-kurtis", :as => "embroidered_kurtis_search"

    # get "/store/punjabi-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/punjabi-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/punjabi-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "punjabi-kurtis", :as => "punjabi_kurtis_search"

    # get "/store/vintage-sarees(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/vintage-sarees",request,params) }
    # get "/sarees/vintage-sarees(/colour-:colour)" =>"store#catalog2", :kind => "vintage-sarees", :as => "vintage_sarees_search"

    # get "/store/send-rakhi-to-usa(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rakhi-gifts/send-rakhi-to-usa",request,params) }
    # get "/rakhi-gifts/send-rakhi-to-usa(/colour-:colour)" =>"store#catalog2", :kind => "send-rakhi-to-usa", :as => "send_rakhi_to_usa_search"

    # get "/store/send-rakhi-to-india(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rakhi-gifts/send-rakhi-to-india",request,params) }
    # get "/rakhi-gifts/send-rakhi-to-india(/colour-:colour)" =>"store#catalog2", :kind => "send-rakhi-to-india", :as => "send_rakhi_to_india_search"

    # get "/store/send-rakhi-to-canada(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rakhi-gifts/send-rakhi-to-canada",request,params) }
    # get "/rakhi-gifts/send-rakhi-to-canada(/colour-:colour)" =>"store#catalog2", :kind => "send-rakhi-to-canada", :as => "send_rakhi_to_canada_search"

    # get "/store/send-rakhi-to-australia(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rakhi-gifts/send-rakhi-to-australia",request,params) }
    # get "/rakhi-gifts/send-rakhi-to-australia(/colour-:colour)" =>"store#catalog2", :kind => "send-rakhi-to-australia", :as => "send_rakhi_to_australia_search"
    
    # get "/store/readymade-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/kurtas-and-kurtis/readymade-kurtis",request,params) }
    # get "/women/clothing/kurtas-and-kurtis/readymade-kurtis(/colour-:colour)" =>"store#catalog2", :kind => "readymade-kurtis", :as => "readymade_kurtis_search"


    # get "/store/maxi-skirt(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/skirts/maxi-skirt",request,params) }
    # get "/women/clothing/skirts/maxi-skirt(/colour-:colour)" =>"store#catalog2", :kind => "maxi-skirt", :as => "maxi_skirt_search"

    # get "/store/long-skirts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/skirts/long-skirts",request,params) }
    # get "/women/clothing/skirts/long-skirts(/colour-:colour)" =>"store#catalog2", :kind => "long-skirts", :as => "long_skirts_search"

    # get "/store/mini-skirt(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/skirts/mini-skirt",request,params) }
    # get "/women/clothing/skirts/mini-skirt(/colour-:colour)" =>"store#catalog2", :kind => "mini-skirt", :as => "mini_skirt_search"

    # get "/store/short-skirts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/skirts/short-skirts",request,params) }
    # get "/women/clothing/skirts/short-skirts(/colour-:colour)" =>"store#catalog2", :kind => "short-skirts", :as => "short_skirts_search"

    # get "/store/cotton-skirts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/skirts/cotton-skirts",request,params) }
    # get "/women/clothing/skirts/cotton-skirts(/colour-:colour)" =>"store#catalog2", :kind => "cotton-skirts", :as => "cotton_skirts_search"

    # get "/store/sleeveless-tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/tops/sleeveless-tops",request,params) }
    # get "/women/clothing/tops/sleeveless-tops(/colour-:colour)" =>"store#catalog2", :kind => "sleeveless-tops", :as => "sleeveless_tops_search"

    # get "/store/cotton-tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/tops/cotton-tops",request,params) }
    # get "/women/clothing/tops/cotton-tops(/colour-:colour)" =>"store#catalog2", :kind => "cotton-tops", :as => "cotton_tops_search"

    # get "/store/chiffon-tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/tops/chiffon-tops",request,params) }
    # get "/women/clothing/tops/chiffon-tops(/colour-:colour)" =>"store#catalog2", :kind => "chiffon-tops", :as => "chiffon_tops_search"

    # get "/store/long-tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/tops/long-tops",request,params) }
    # get "/women/clothing/tops/long-tops(/colour-:colour)" =>"store#catalog2", :kind => "long-tops", :as => "long_tops_search"

    # get "/store/spaghetti-tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/tops/spaghetti-tops",request,params) }
    # get "/women/clothing/tops/spaghetti-tops(/colour-:colour)" =>"store#catalog2", :kind => "spaghetti-tops", :as => "spaghetti_tops_search"

    # get "/store/bellies-shoes(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/footwear/bellies-shoes",request,params) }
    # get "/women/footwear/bellies-shoes(/colour-:colour)" =>"store#catalog2", :kind => "bellies-shoes", :as => "bellies_shoes_search"

    # get "/store/wedges-shoes(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/footwear/wedges-shoes",request,params) }
    # get "/women/footwear/wedges-shoes(/colour-:colour)" =>"store#catalog2", :kind => "wedges-shoes", :as => "wedges_shoes_search"

    # get "/store/peep-toe-shoes(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/footwear/peep-toe-shoes",request,params) }
    # get "/women/footwear/peep-toe-shoes(/colour-:colour)" =>"store#catalog2", :kind => "peep-toe-shoes", :as => "peep_toe_shoes_search"

    # get "/store/loafers-shoes(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/footwear/loafers-shoes",request,params) }
    # get "/men/footwear/loafers-shoes(/colour-:colour)" =>"store#catalog2", :kind => "loafers-shoes", :as => "loafers_shoes_search"

    # get "/store/dress-materials(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/dress-materials",request,params) }
    # get "/women/clothing/dress-materials(/colour-:colour)" =>"store#catalog2", :kind => "dress-materials", :as => "dress_materials_search" 

    # get "/store/gemstone-rings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/jewellery/gemstone-rings",request,params) }
    # get "/gemstones/jewellery/gemstone-rings(/colour-:colour)" =>"store#catalog2", :kind => "gemstone-rings", :as => "gemstone_rings_search"

    # get "/store/gemstone-pendants(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/jewellery/gemstone-pendants",request,params) }
    # get "/gemstones/jewellery/gemstone-pendants(/colour-:colour)" =>"store#catalog2", :kind => "gemstone-pendants", :as => "gemstone_pendants_search"

    # get "/store/gemstone-jewellery(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/jewellery",request,params) }
    # get "/gemstones/jewellery(/colour-:colour)" =>"store#catalog2", :kind => "gemstone-jewellery", :as => "gemstone_jewellery_search"

    # get "/store/gemstone-bracelets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/jewellery/gemstone-bracelets",request,params) }
    # get "/gemstones/jewellery/gemstone-bracelets(/colour-:colour)" =>"store#catalog2", :kind => "gemstone-bracelets", :as => "gemstone_bracelets_search"

    # get "/store/gemstone-earrings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/jewellery/gemstone-earrings",request,params) }
    # get "/gemstones/jewellery/gemstone-earrings(/colour-:colour)" =>"store#catalog2", :kind => "gemstone-earrings", :as => "gemstone_earrings_search"

    # get "/store/loose-gemstones(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/jewellery/loose-gemstones",request,params) }
    # get "/gemstones/jewellery/loose-gemstones(/colour-:colour)" =>"store#catalog2", :kind => "loose-gemstones", :as => "loose_gemstones_search"

    # get "/store/gemstone-necklaces(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/jewellery/gemstone-necklaces",request,params) }
    # get "/gemstones/jewellery/gemstone-necklaces(/colour-:colour)" =>"store#catalog2", :kind => "gemstone-necklaces", :as => "gemstone_necklaces_search"

    # get "/store/plus-size(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/plus-size",request,params) }
    # get "/women/clothing/plus-size(/colour-:colour)" =>"store#catalog2", :kind => "plus-size", :as => "plus_size_search"

    # get "/store/plus-size-blouses(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/plus-size/blouses",request,params) }
    # get "/women/clothing/plus-size/blouses(/colour-:colour)" =>"store#catalog2", :kind => "plus-size-blouses", :as => "plus_size_blouses_search"

    # get "/store/plus-size-salwar(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/plus-size/salwar-kameez",request,params) }
    # get "/women/clothing/plus-size/salwar-kameez(/colour-:colour)" =>"store#catalog2", :kind => "plus-size-salwar", :as => "plus_size_salwar_search"

    # get "/store/plus-size-lehenga(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/plus-size/lehenga",request,params) }
    # get "/women/clothing/plus-size/lehenga(/colour-:colour)" =>"store#catalog2", :kind => "plus-size-lehenga", :as => "plus_size_lehenga_search"

    # get "/store/plus-size-kurtis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/plus-size/kurtis",request,params) }
    # get "/women/clothing/plus-size/kurtis(/colour-:colour)" =>"store#catalog2", :kind => "plus-size-kurtis", :as => "plus_size_kurtis_search"

    # get "/store/plus-size-leggings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/plus-size/leggings",request,params) }
    # get "/women/clothing/plus-size/leggings(/colour-:colour)" =>"store#catalog2", :kind => "plus-size-leggings", :as => "plus_size_leggings_search"

    # get "/store/plus-size-tops(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/plus-size/tops",request,params) }
    # get "/women/clothing/plus-size/tops(/colour-:colour)" =>"store#catalog2", :kind => "plus-size-tops", :as => "plus_size_tops_search"

    # get "/store/plus-size-skirts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/plus-size/skirts",request,params) }
    # get "/women/clothing/plus-size/skirts(/colour-:colour)" =>"store#catalog2", :kind => "plus-size-skirts", :as => "plus_size_skirts_search"

    # get "/store/plus-size-shoes(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/plus-size/shoes",request,params) }
    # get "/women/clothing/plus-size/shoes(/colour-:colour)" =>"store#catalog2", :kind => "plus-size-shoes", :as => "plus_size_shoes_search"

    # get "/store/bolster-cushion(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/home-furnishing/pillow-covers/bolster-cushion",request,params) }
    # get "/home-decor/home-furnishing/pillow-covers/bolster-cushion(/colour-:colour)" =>"store#catalog2", :kind => "bolster-cushion", :as => "bolster_cushion_search"

    # get "/store/wall-hangings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/wall-hangings",request,params) }
    # get "/home-decor/wall-hangings(/colour-:colour)" =>"store#catalog2", :kind => "wall-hangings", :as => "wall_hangings_search"

    # get "/store/door-hangings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/door-hangings",request,params) }
    # get "/home-decor/door-hangings(/colour-:colour)" =>"store#catalog2", :kind => "door-hangings", :as => "door_hangings_search"

    # get "/store/chindi-rugs(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/rugs/chindi-rugs",request,params) }
    # get "/rugs/chindi-rugs(/colour-:colour)" =>"store#catalog2", :kind => "chindi-rugs", :as => "chindi_rugs_search"

    # get "/store/saree-borders(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/fabrics/saree-borders",request,params) }
    # get "/fabrics/saree-borders(/colour-:colour)" =>"store#catalog2", :kind => "saree-borders", :as => "saree_borders_search"

    # get "/store/fabric-by-yard(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/fabrics/fabric-by-yard",request,params) }
    # get "/fabrics/fabric-by-yard(/colour-:colour)" =>"store#catalog2", :kind => "fabric-by-yard", :as => "fabric_by_yard_search"

    # get "/store/diwali-rangoli-designs(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/festival/diwali-rangoli-designs",request,params) }
    # get "/festival/diwali-rangoli-designs(/colour-:colour)" =>"store#catalog2", :kind => "diwali-rangoli-designs", :as => "diwali_rangoli_designs_search"

    # get "/store/capris-3-4-pants(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/capris-3-4-pants",request,params) }
    # get "/women/clothing/capris-3-4-pants(/colour-:colour)" =>"store#catalog2", :kind => "capris-3-4-pants", :as => "capris_3_4_pants_search"

    # get "/store/organic-tea(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/beverages/organic-tea",request,params) }
    # get "/beverages/organic-tea(/colour-:colour)" =>"store#catalog2", :kind => "organic-tea", :as => "organic_tea_search"

    # get "/store/tea" => redirect{ |params,request| page_redirect.call("/beverages/tea",request,params) }
    # get "/beverages/tea" => "store#catalog2", :kind => "tea", :as => "tea_search"

    # get "/store/tapestries(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries",request,params) }
    # get "/tapestries(/colour-:colour)" =>"store#catalog2", :kind => "tapestries", :as => "tapestries_search"

    # get "/store/tapestries-wall-hangings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings",request,params) }
    # get "/tapestries/wall-hangings(/colour-:colour)" =>"store#catalog2", :kind => "tapestries-wall-hangings", :as => "tapestries_wall_hangings_search"

    # get "/store/mandala-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/mandala-tapestry",request,params) }
    # get "/tapestries/wall-hangings/mandala-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "mandala-tapestry", :as => "mandala_tapestry_search"

    # get "/store/queen-size-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/queen-size-tapestry",request,params) }
    # get "/tapestries/wall-hangings/queen-size-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "queen-size-tapestry", :as => "queen_size_tapestry_search"

    # get "/store/twin-size-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/twin-size-tapestry",request,params) }
    # get "/tapestries/wall-hangings/twin-size-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "twin-size-tapestry", :as => "twin_size_tapestry_search"

    # get "/store/elephant-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/elephant-tapestry",request,params) }
    # get "/tapestries/wall-hangings/elephant-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "elephant-tapestry", :as => "elephant_tapestry_search"

    # get "/store/tree-of-life-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/tree-of-life-tapestry",request,params) }
    # get "/tapestries/wall-hangings/tree-of-life-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "tree-of-life-tapestry", :as => "tree_of_life_tapestry_search"

    # get "/store/buddha-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/buddha-tapestry",request,params) }
    # get "/tapestries/wall-hangings/buddha-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "buddha-tapestry", :as => "buddha_tapestry_search"

    # get "/store/sun-moon-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/sun-moon-tapestry",request,params) }
    # get "/tapestries/wall-hangings/sun-moon-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "sun-moon-tapestry", :as => "sun_moon_tapestry_search"

    # get "/store/tie-dye-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/tie-dye-tapestry",request,params) }
    # get "/tapestries/wall-hangings/tie-dye-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "tie-dye-tapestry", :as => "tie_dye_tapestry_search"

    # get "/store/printed-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/printed-tapestry",request,params) }
    # get "/tapestries/wall-hangings/printed-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "printed-tapestry", :as => "printed_tapestry_search"

    # get "/store/zodiac-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/zodiac-tapestry",request,params) }
    # get "/tapestries/wall-hangings/zodiac-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "zodiac-tapestry", :as => "zodiac_tapestry_search"

    # get "/store/indian-hindu-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/indian-hindu-tapestry",request,params) }
    # get "/tapestries/wall-hangings/indian-hindu-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "indian-hindu-tapestry", :as => "indian_hindu_tapestry_search"

    # get "/store/bedding-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/bedding-tapestry",request,params) }
    # get "/tapestries/wall-hangings/bedding-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "bedding-tapestry", :as => "bedding_tapestry_search"

    # get "/store/patchwork-tapestry(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/tapestries/wall-hangings/patchwork-tapestry",request,params) }
    # get "/tapestries/wall-hangings/patchwork-tapestry(/colour-:colour)" =>"store#catalog2", :kind => "patchwork-tapestry", :as => "patchwork_tapestry_search"

    # get "/store/quilts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/quilts",request,params) }
    # get "/home-decor/bedding/quilts(/colour-:colour)" =>"store#catalog2", :kind => "quilts", :as => "quilts_search"

    # get "/store/kantha-quilt(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/quilts/kantha-quilt",request,params) }
    # get "/home-decor/bedding/quilts/kantha-quilt(/colour-:colour)" =>"store#catalog2", :kind => "kantha-quilt", :as => "kantha_quilt_search"

    # get "/store/queen-size-kantha-quilt(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/quilts/queen-size-kantha-quilt",request,params) }
    # get "/home-decor/bedding/quilts/queen-size-kantha-quilt(/colour-:colour)" =>"store#catalog2", :kind => "queen-size-kantha-quilt", :as => "queen_size_kantha_quilt_search"

    # get "/store/vintage-kantha-quilt(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/quilts/vintage-kantha-quilt",request,params) }
    # get "/home-decor/bedding/quilts/vintage-kantha-quilt(/colour-:colour)" =>"store#catalog2", :kind => "vinatge-kantha-quilt", :as => "vintage_kantha_quilt_search"

    # get "/store/king-size-kantha-quilt(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/quilts/king-size-kantha-quilt",request,params) }
    # get "/home-decor/bedding/quilts/king-size-kantha-quilt(/colour-:colour)" =>"store#catalog2", :kind => "king-size-kantha-quilt", :as => "king_size_kantha_quilt_search"

    # get "/store/kantha-blankets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/quilts/kantha-blankets",request,params) }
    # get "/home-decor/bedding/quilts/kantha-blankets(/colour-:colour)" =>"store#catalog2", :kind => "kantha-blankets", :as => "kantha_blankets_search"

    # get "/store/patchwork-kantha-quilt(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/quilts/patchwork-kantha-quilt",request,params) }
    # get "/home-decor/bedding/quilts/patchwork-kantha-quilt(/colour-:colour)" =>"store#catalog2", :kind => "patchwork-kantha-quilt", :as => "patchwork_kantha_quilt_search"

    # get "/store/kantha-throws(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/quilts/kantha-throws",request,params) }
    # get "/home-decor/bedding/quilts/kantha-throws(/colour-:colour)" =>"store#catalog2", :kind => "kantha-throws", :as => "kantha_throws_search"

    # get "/store/bedspreads(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/bedspreads",request,params) }
    # get "/home-decor/bedding/bedspreads(/colour-:colour)" =>"store#catalog2", :kind => "bedspreads", :as => "bedspreads_search"

    # get "/store/other-gemstone(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/jewellery/other-gemstone",request,params) }
    # get "/gemstones/jewellery/other-gemstone(/colour-:colour)" =>"store#catalog2", :kind => "other-gemstone", :as => "other_gemstone_search"

    # get "/store/nauvari-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/nauvari-saree",request,params) }
    # get "/sarees/nauvari-saree(/colour-:colour)" =>"store#catalog2", :kind => "nauvari-saree", :as => "nauvari_saree_search"

    # get "/store/maharashtrian-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/maharashtrian-saree",request,params) }
    # get "/sarees/maharashtrian-saree(/colour-:colour)" =>"store#catalog2", :kind => "maharashtrian-saree", :as => "maharashtrian_saree_search"

    # get "/store/peshwai-saree(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/sarees/peshwai-saree",request,params) }
    # get "/sarees/peshwai-saree(/colour-:colour)" =>"store#catalog2", :kind => "peshwai-saree", :as => "peshwai_saree_search"

    # get "/store/lingerie(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/lingerie",request,params) }
    # get "/women/clothing/lingerie(/colour-:colour)" =>"store#catalog2", :kind => "lingerie", :as => "lingerie_search"

    # get "/store/bras(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/lingerie/bras",request,params) }
    # get "/women/clothing/lingerie/bras(/colour-:colour)" =>"store#catalog2", :kind => "bras", :as => "bras_search"

    # get "/store/panties(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/lingerie/panties",request,params) }
    # get "/women/clothing/lingerie/panties(/colour-:colour)" =>"store#catalog2", :kind => "panties", :as => "panties_search"

    # get "/store/sleepwear(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/lingerie/sleepwear",request,params) }
    # get "/women/clothing/lingerie/sleepwear(/colour-:colour)" =>"store#catalog2", :kind => "sleepwear", :as => "sleepwear_search"

    # get "/store/swimwear(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/swimwear",request,params) }
    # get "/women/clothing/swimwear(/colour-:colour)" =>"store#catalog2", :kind => "swimwear", :as => "swimwear_search"

    # get "/store/workout-gym-wear(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/workout-gym-wear",request,params) }
    # get "/women/clothing/workout-gym-wear(/colour-:colour)" =>"store#catalog2", :kind => "workout-gym-wear", :as => "workout_gym_wear_search"

    # get "/store/organic-foods(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/organic-foods",request,params) }
    # get "/organic-foods(/colour-:colour)" =>"store#catalog2", :kind => "organic-foods", :as => "organic_foods_search"

    # get "/store/indian-staples(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/organic-foods/indian-staples",request,params) }
    # get "/organic-foods/indian-staples(/colour-:colour)" =>"store#catalog2", :kind => "indian-staples", :as => "indian_staples_search"

    # get "/store/masala-spice-mixes(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/organic-foods/masala-spice-mixes",request,params) }
    # get "/organic-foods/masala-spice-mixes(/colour-:colour)" =>"store#catalog2", :kind => "masala-spice-mixes", :as => "masala_spice_mixes_search"

    # get "/store/papad(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/organic-foods/papad",request,params) }
    # get "/organic-foods/papad(/colour-:colour)" =>"store#catalog2", :kind => "papad", :as => "papad_search"

    # get "/store/pickles-dips(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/organic-foods/pickles-dips",request,params) }
    # get "/organic-foods/pickles-dips(/colour-:colour)" =>"store#catalog2", :kind => "pickles-dips", :as => "pickles_dips_search"

    # get "/store/confectionery(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/organic-foods/confectionery",request,params) }
    # get "/organic-foods/confectionery(/colour-:colour)" =>"store#catalog2", :kind => "confectionery", :as => "confectionery_search"

    # get "/store/snacks(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/organic-foods/snacks",request,params) }
    # get "/organic-foods/snacks(/colour-:colour)" =>"store#catalog2", :kind => "snacks", :as => "snacks_search"

    # get "/store/spices(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/organic-foods/spices",request,params) }
    # get "/organic-foods/spices(/colour-:colour)" =>"store#catalog2", :kind => "spices", :as => "spices_search"

    # get "/store/oil(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/organic-foods/oil",request,params) }
    # get "/organic-foods/oil(/colour-:colour)" =>"store#catalog2", :kind => "oil", :as => "oil_search"

    # get "/store/astrology-rings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/astrology-rings",request,params) }
    # get "/gemstones/astrology-rings(/colour-:colour)" =>"store#catalog2", :kind => "astrology-rings", :as => "astrology_rings_search"

    # get "/store/gemstones(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones",request,params) }
    # get "/gemstones(/colour-:colour)" =>"store#catalog2", :kind => "gemstones", :as => "gemstones_search"

    # get "/store/birthstones(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/gemstones/birthstones",request,params) }
    # get "/gemstones/birthstones(/colour-:colour)" =>"store#catalog2", :kind => "birthstones", :as => "birthstones_search"

    # get "/store/essential-oils(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/bath-beauty/essential-oils",request,params) }
    # get "/bath-beauty/essential-oils(/colour-:colour)" =>"store#catalog2", kind: "essential-oils", as: "essential_oils_search"

    # get "/store/gowns-below-1000(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/gowns-below-1000",request,params) }
    # get "/women/clothing/gowns-below-1000(/colour-:colour)" =>"store#catalog2", kind: "gowns-below-1000", as: "gowns_below_1000_search"
    
    # get "/store/combo-earrings(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/earrings/combo-earrings",request,params) }
    # get "/women/jewellery/earrings/combo-earrings(/colour-:colour)" =>"store#catalog2", kind: "combo-earrings", as: "combo_earrings_search"

    # get "/store/pillow-covers(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/home-furnishing/pillow-covers",request,params) }
    # get "/home-decor/home-furnishing/pillow-covers(/colour-:colour)" =>"store#catalog2", :kind => "pillow-covers", :as => "pillow_covers_search"

    # get "/store/bed-sheets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/bed-sheets",request,params) }
    # get "/home-decor/bedding/bed-sheets(/colour-:colour)" =>"store#catalog2", :kind => "bed-sheets", :as => "bed_sheets_search"

    # get "/store/jaipuri-razai(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/jaipuri-razai",request,params) }
    # get "/home-decor/bedding/jaipuri-razai(/colour-:colour)" =>"store#catalog2", :kind => "jaipuri-razai", :as => "jaipuri_razai_search"

    # get "/store/duvet-covers(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/home-decor/bedding/duvet-covers",request,params) }
    # get "/home-decor/bedding/duvet-covers(/colour-:colour)" =>"store#catalog2", :kind => "duvet-covers", :as => "duvet_covers_search"

    # get "/store/table-cloth(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kitchen-dining/table-cloth",request,params) }
    # get "/kitchen-dining/table-cloth(/colour-:colour)" =>"store#catalog2", :kind => "table-cloth", :as => "table_cloth_search"

    # get "/store/table-mats-and-runner(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/kitchen-dining/table-mats-runners",request,params) }
    # get "/kitchen-dining/table-mats-runners(/colour-:colour)" =>"store#catalog2", :kind => "table-mats-and-runner", :as => "table_mats_and_runner_search"

    # get "/store/bra-sets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/lingerie/bras/bra-sets",request,params) }
    # get "/women/clothing/lingerie/bras/bra-sets(/colour-:colour)" =>"store#catalog2", :kind => "bra-sets", :as => "bra_sets_search"

    # get "/store/panty-sets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/lingerie/panties/panty-sets",request,params) }
    # get "/women/clothing/lingerie/panties/panty-sets(/colour-:colour)" =>"store#catalog2", :kind => "panty-sets", :as => "panty_sets_search"

    # get "/store/lingerie-sets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/lingerie/lingerie-sets",request,params) }
    # get "/women/clothing/lingerie/lingerie-sets(/colour-:colour)" =>"store#catalog2", :kind => "lingerie-sets", :as => "lingerie_sets_search"

    # get "/store/bikini-sets(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/clothing/swimwear/bikini-sets",request,params) }
    # get "/women/clothing/swimwear/bikini-sets(/colour-:colour)" =>"store#catalog2", :kind => "bikini-sets", :as => "bikini_sets_search"

    # get "/store/mangalsutra(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/women/jewellery/mangalsutra",request,params) }
    # get "/women/jewellery/mangalsutra(/colour-:colour)" =>"store#catalog2", :kind => "mangalsutra", :as => "mangalsutra_search"

    # Has to be last for /men category
    # get "/store/men(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men",request,params) }
    # get "/men(/colour-:colour)" =>"store#catalog2", :kind => "men", :as => "men_search"

    # get "/store/dhotis(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/dhotis",request,params) }
    # get "/men/clothing/dhotis(/colour-:colour)" =>"store#catalog2", :kind => "dhotis", :as => "dhotis_search"

    # get "/store/combo-t-shirts(/colour-:colour)" =>redirect{ |params,request| page_redirect.call("/men/clothing/tshirts/combo-t-shirts",request,params) }
    # get "/men/clothing/tshirts/combo-t-shirts(/colour-:colour)" =>"store#catalog2", :kind => "combo-t-shirts", :as => "combo_t_shirts_search"
    get "/designers/:designer_id/mirraw_contact_details" => "pages#mirraw_contact_details" , :as => 'mirraw_contact_details'
    get "/designers/:designer_id/create_ticket" => "pages#create_ticket" , :as => 'create_ticket'
    get "/designers/craftsvilla-sarees" => redirect('/404')
    get "/designers/indiarush-fashion" => redirect('/404')
    get '/home-decor/home-furnishing/cushion-covers' => redirect('/home-decor/home-furnishing/pillow-covers')
    get "/home-decor/home-furnishing/pillow-covers" => "store#catalog2", :kind => "pillow-covers", :as => "pillow_covers_search"


    get "/store/oxidised-jewellery" => redirect("/women/jewellery/oxidised-jewellery")
    get "/women/jewellery/oxidised-jewellery(/:facets)" =>"store#catalog2", :kind => "oxidised-jewellery", :as => "oxidised-jewellery_search"


    get "/women/clothing/dresses" => redirect("/women/clothing/western-wear/dresses")
    get "/women/clothing/western-wear/dresses(/:facets)" =>"store#catalog2", :kind => "dresses", :as => "dresses_search"

    get "/insta" => redirect("/?utm_source=Instagram&utm_medium=home&utm_campaign=home")
    get '/designers/get_bulk_upload_ids' => 'designers#get_bulk_upload_ids', :as =>'get_bulk_upload_ids'
    get '/designers/:id/claim_page' => 'designers#claim_page' , :as=>'/claim_page'
    post 'designers/claim_form' => 'designers#claim_form', :as => '/claim_form'

    get "/designers/yebhi4u" => redirect("/designers/yebhi-4u")

    get "/gosf-offer/jewelery" => redirect("http://get.mirraw.com/gosf-jewellery-page")
    get "/gosf-offer/clothing" => redirect("http://get.mirraw.com/gosf-clothing/")

    get "/gosf-offers/ramleela-earrings" => redirect("http://www.mirraw.com/designers/mokanc/designs/ramleela-earrings-danglers-drop--4")
    get "/gosf-offers/pearl-earring" => redirect("http://www.mirraw.com/designers/adiva/designs/ethnic-pearl-polki-earring-by-adiva-ansatocoo46-tds-1-hoop")
    get "/gosf-offers/ethnic-earrings"=> redirect("http://www.mirraw.com/designers/adiva/designs/beautiful-ethnic-earrings-with-pearl-stones-and-pearls-by-adiva-ha99-tds-3-danglers-drop")
    get "/gosf-offers/replica-earrings" => redirect("http://www.mirraw.com/designers/mokanc/designs/replica-earrings-hoop")
    get "/gosf-offers/hoop-earrings" => redirect("http://www.mirraw.com/designers/adiva/designs/hoop-earrings--3")
    get "/gosf-offers/pearl-necklace" => redirect("http://www.mirraw.com/designers/reeti-fashion/designs/pearl-necklace-set-with-earrings-necklace-set")
    get "/gosf-offers/ear-hook-Jewelery" => redirect("http://www.mirraw.com/designers/fever-21/designs/ear-hook-jewelry")
    get "/gosf-offers/Chiffon-saree" => redirect("http://www.mirraw.com/designers/designersareez/designs/maroon-chiffon-saree-with-unstitched-blouse-bvr935-chiffon-saree")
    get "/gosf-offers/pendant-necklace" => redirect("http://www.mirraw.com/designers/fever-21/designs/multicolor-pendant-necklace")
    get "/gosf-offers/statement-earring" => redirect("http://www.mirraw.com/designers/fever-21/designs/blue-statement-earring--2")
    post "/stitching_measurement/create" => "stitching_measurement#create", as: "stitching_measurement_create"
    patch "/stitching_measurement/update" => "stitching_measurement#update", as: "stitching_measurement_update"
    get "/stitching_measurement/measurement_info" => "stitching_measurement#measurement_info"
    get "/stitching_measurement/standard_measurement_info" => "stitching_measurement#standard_measurement_info"
    get '/stitching_form' => 'stitching_measurement#stitching_form',as: 'stitching_form'
    post '/stitching_measurements/add_custom_addons' => 'stitching_measurement#add_custom_addons'
    get '/sample_stitching_forms' => 'stitching_measurement#sample_stitching_forms'
    get '/lehenga_stitching_form' => redirect('/sample_stitching_forms?product_designable_type=lehenga_choli&type_of_suit=Lehenga')
    get '/blouse_stitching_form' => redirect('/sample_stitching_forms?product_designable_type=lehenga_choli&type_of_suit=Saree')
    get '/salwarkameez_stitching_form' => redirect('/sample_stitching_forms?product_designable_type=anarkali&type_of_suit=SalwarKameez')
    get '/stitching_measurement/measurement_data' => 'stitching_measurement#measurement_data'
    post "/stitching_measurement/stitching_info_mailer" => "stitching_measurement#stitching_info_mailer", as: "stitching_measurement_mailer"
    post "/stitching_measurement/stitching_form_mailer" => "stitching_measurement#stitching_form_mailer", as: "stitching_measurement_form_mailer"
    get '/stitching_measurement/add_measurement_mapping' => 'stitching_measurement#add_measurement_mapping', as: 'add_measurement_mapping'
    post '/stitching_measurement/update_measurement_info' => 'stitching_measurement#update_measurement_info', as: 'update_measurement_info'
    post "/tailoring_info/create" => "tailoring_info#create", :as => "tailoring_info_create"
    post "/tailoring_info/reassign_to_tailor" => 'tailoring_info#reassign_to_tailor', as: "tailoring_info_reassign"
    post "/tailoring_info/assigned_tailer_remove" => 'tailoring_info#assigned_tailer_remove'
    post "/tailoring_info/rtv_tailoring_item" => 'tailoring_info#rtv_tailoring_item', as: "rtv_tailoring_item"
    patch "/tailoring_info/update" => "tailoring_info#update", :as => "tailoring_info_update"
    get "admin/ipending_list" => "admin#ipending_list", as: "admin_ipending_list"
    get 'admin/upload_freshdesk' => 'admin#upload_freshdesk'
    post 'admin/upload_freshdesk' => 'admin#upload_freshdesk', as: 'fetch_order_for_freshdesk'
    get 'admin/app_rank_details' => 'admin#app_rank_details'
    post 'admin/app_rank_details' => 'admin#app_rank_details', as: 'app_rank_details'
    get 'admin/app_rank_batchwise' => 'admin#app_rank_batchwise', as: 'app_rank_batchwise'
    post 'admin/send_email' => 'admin#send_email', as: 'send_email'
    get 'admin/vendor_performance' => 'admin#vendor_performance', as: 'vendor_performance'
    get 'admin/compare_designer_performance' => 'admin#compare_designer_performance',as: 'compare_designer_performance'
    get 'admin/shipment_bill_no' => 'admin#shipment_bill_no'
    post 'admin/shipment_bill_no' => 'admin#shipment_bill_no',as:'shipment_bill_no'
    post 'admin/cost_estimation' => 'admin#cost_estimation', as: 'cost_estimation'
    post 'admin/generate_irn_commercial_invoice' => 'admin#generate_irn_commercial_invoice', as: 'generate_irn_commercial_invoice'
    post 'admin/update_cost_estimation_report' => 'admin#update_cost_estimation_report', as: 'update_cost_estimation_report'
    get 'admin/messages' => 'messages#index', as: 'admin_messages', layout: 'admin'
    get 'admin/invalid_address_orders' => 'admin#invalid_address', as: 'admin_invalid_address'
    get 'admin/get_tally_report' => 'admin#get_tally_report'
    post 'admin/get_tally_report' => 'admin#get_tally_report', as: 'get_tally_report'
    post 'admin/update_user_phone' => 'admin#update_user_phone', as: 'admin_update_user_phone'
    
    scope :path => '/admin', controller: :admin do
      get  'duplicate-inventory' => :duplicate_inventory, as:'duplicate_inventory'
      post 'schedule_for_dedup' => :schedule_for_dedup, as: 'schedule_for_dedup'
      get  'duplicate_inventory_csv_download' => :duplicate_inventory_csv_download, as: 'download_design_report'
      get 'duplicate-inventory/:method_type' => :designer_duplicate_inventory, as:'designer_duplicate_inventory'
      get 'review_tailoring_feedback' => :review_tailoring_feedback, as:'review_tailoring_feedback'
      post 'review_tailoring_feedback' => :review_tailoring_feedback
    end

    scope :path => '/grading', controller: :grading do
      get 'change-constants' => :change_constant
      get 'upload-file' => :grading_automation
      post 'upload-file' => :grading_automation
      post 'reapply' => :reapply
      post 'discard' => :discard
      post 'live' => :live
      get 'all' => :index
      post 'notes' => :update_notes
      get 'config' => :task_config
      post 'change_config' => :change_config
    end

    resources :fb_pages
    resources :facebook_posts do
      member do
        get   :populate
        get   :reset
        post  :schedule
      end
    end

    root :to => 'store#full_image'
    get '/set_offer_message' => "pages#set_offer_message"

    # get "wholesale/:kind" => "wholesale#index", :as => "wholesale_search"

    get "/designers/:designer_id/designs/:design_id/similar" => "designs#similar", :as => 'designer_design_similar'

    get '/designers/:designer_id/designs/:design_id/remove_variants' => 'designs#remove_variants', :as => 'designer_design_remove_variants'
    post '/designers/:designer_id/designs/:design_id/variants/:variant_id/remove_variants' => 'designs#remove_variants'
    get '/designs/:id/get_variants' => 'designs#get_variants',as: 'get_variants'

    get '/s/:id' => "shortener/shortened_urls#show", as: :shortened

    get '/admin/download_bulk_edit' => 'admin#download_bulk_edit'
    get '/admin/view_for_bulk_edit' => 'admin#view_for_bulk_edit', as: 'view_for_bulk_edit'
    get 'admin/create_issue_line_item' => 'admin#create_issue_line_item', as: 'create_issue_line_item'
    post '/admin/download_bulk_edit' => 'admin#download_bulk_edit', as: 'download_bulk_edit'
    get '/admin/mark_dispatch' => "admin#mark_ready_for_dispatch"
    post '/admin/mark_order_urgent' => 'admin#mark_order_urgent'
    get '/admin/check_for_unique_transaction_id' => 'admin#check_for_unique_transaction_id'
    get '/admin/get_transaction_id_for_duplicate_order' => 'admin#get_transaction_id_for_duplicate_order'
    get '/orders/order_check_page' => 'orders#order_check_page'
    post '/orders/order_check_page' => 'orders#order_check_page'
    post '/orders/send_sms_for_cancellation_of_cod_order'=> 'orders#send_sms_for_cancellation_of_cod_order' , as: 'send_sms_for_cancellation_of_cod_order'

    resources :designers, except: :show do
      resources :designs, :except => :destroy do
        resources :reviews, except: [:show,:destroy]
        collection do
          get :get_option_type, defaults: {format: :js}
        end
      end
      resources :vendor_addons, as: :addons do
        post :bulk_addon, on: :collection
      end
      resources :messages, only: :index
      resources :designer_orders, except: :create
      resources :coupons
      resources :designer_collections
      resources :collections,controller: 'designer_collections', except: [:create, :update]#Just to get this kind of user friendly URL
      member do
        get :ads
        post :change_owner
      end
    end
    resource :user, only: [] do
      resources :wishlists, only: [:new, :create, :index] do
        collection do
          delete :destroy
          get :filter
        end
      end
    end

    get '/affiliate' => 'affiliate#show'
    get '/affiliate/designs' => 'affiliate_designs#index'

    get '/os_ai_vendor_details' => 'designers#designer_detail_for_online_sales', defaults: {format: :json}
    post '/messages/mark_read_msgs' => 'messages#mark_read_msgs'
    get '/admin/upload_designs_size_chart' =>'admin#upload_designs_size_chart'
    post '/admin/upload_designs_size_chart' =>'admin#upload_designs_size_chart',as: 'upload_designs_size_chart'

    get '/designers/:designer_id/report' => 'admin#designer_report', :as => 'admin_designer_report'

    post 'designers/:id/add_master_addons' => 'designers#add_master_addons', as: 'add_master_addons'

    post '/design/set_master' => "designs#change_master_image", :as => "change_master_image"
    get '/designers/:id/manage_boutique' => "designers#manage_boutique", :as => 'manage_boutique'
    get '/designers/:id/oos_bestseller_designs' => "designers#oos_bestseller_designs", :as => 'oos_bestseller_designs'
    get '/designers/:id/product_analytics' => "designers#product_analytics", :as => 'designer_product_analytics'
    get '/designers/:id/feedback' => "designers#feedback", :as => 'designer_feedback'
    get '/designers/:id/all_feedback' => "designers#all_feedback", :as => 'designer_all_feedback'
    get '/designers/:designer_id/designs/:design_id/more-designers' => 'designs#more_designers', as:'designer_design_other_designers'
    post '/designers/:designer_id/designs/:design_id/more-designers' => 'designs#update_design_price_and_discount', as: 'design_price_update'
    get '/designers/:id/download_csv' => "designers#download_csv", :as => 'download_csv'
    post '/designers/:id/upload_csv' => "designers#upload_csv", :as => 'upload_csv'

    get '/designers/:id/bulk_upload' => "designers#bulk_upload"
    post '/designers/:id/bulk_upload' => "designers#bulk_upload", :as => "/designers/bulk_upload"

    get '/designers/:id/additional_discount' => "designers#additional_discount"
    post '/designers/:id/additional_discount' => "designers#additional_discount", :as => "additional_discount"

    put '/designers/:id/discontinue_designs' => "designers#discontinue_designs", :as => 'discontinue_designs'
    get '/designers/:id/manage_pages' => "designers#manage_pages", :as => 'manage_pages'
    get '/designers/:id/facebook_page_unlink' => "designers#facebook_page_unlink", :as => 'facebook_page_unlink'
    post '/designers/:designer_id/post_to_facebook' => "designs#post_to_facebook", :as => 'post_to_facebook'
    post "/designers/:id/fb_publish_create" => "designers#fb_publish_create", :as => "designer_fb_publish_create"
    get "/designers/:id/fb_publish_new" => "designers#fb_publish_new", :as => "designer_fb_publish_new"
    get "/designers/:id/activate_fb_autopost" => "designers#toggle_fb_autopost", as: "designer_activate_fb_autopost", autopost: true
    get "/designers/:id/deactivate_fb_autopost" => "designers#toggle_fb_autopost", as: "designer_deactivate_fb_autopost", autopost: false
    post "/designs/update_design_quantity" => "designs#update_design_quantity", :as => "designs_update_design_quantity"
    post "/designers/:designer_id/designs/:id/remove_all_catalog_item" => "designs#remove_from_all_catalog", :as => "designer_design_remove_all_catalog_item"
    post "/designers/:designer_id/designs/:id/remove_int_catalog_item" => "designs#remove_from_international_catalog", :as => "designer_design_remove_int_catalog_item"
    post "/designers/:designer_id/designs/:id/remove_dom_catalog_item" => "designs#remove_from_domestic_catalog", :as => "designer_design_remove_dom_catalog_item"
    get '/designers/:id/addon_settings' => "designers#addon_settings", :as => "designer_addon_settings"
    get '/designers/:id/prepare_manifest' => "designers#prepare_manifest", as: "designer_prepare_manifest"
    get '/designers/:id/bulk_dispatch' => "designers#bulk_dispatch", as: "designer_bulk_dispatch"
    get '/designers/get_domestic_shipper_names' => 'designers#get_domestic_shipper_names'
    post '/designers/:id/upload_manifest' => "designers#upload_manifest", as: "upload_manifest"
    post '/designers/:id/generate_manifest' => "designers#generate_manifest", as: "designer_generate_manifest"
    post '/designers/:id/set_addons_settings' => "designers#set_addons_settings", :as => "designer_set_addons_settings"
    get 'designers/:id/failed_designs' => 'designers#failed_designs', :as => 'failed_designs'
    post 'designers/:id/delete_batch' => 'designers#delete_batch', as: 'delete_batch'
    get 'designers/:id/failed_images' => 'designers#failed_images'
    post 'designers/:id/failed_images' => 'designers#update_failed_images', :as => 'failed_images'
    post 'designers/:id/uploading_failed_designs' => 'designers#uploading_failed_designs', :as => 'uploading_failed_designs'
    post 'designers/:id/uploading_version_failed_designs' => 'designers#uploading_version_failed_designs', :as => 'uploading_version_failed_designs'
    get '/designs/get_line_items_count' => 'designs#get_line_items_count'
    get 'designs/design_eta' => 'designs#design_eta', :as => 'design_eta'

    get 'designers/edit_invoice/:edit_invoice_link' => 'designers#edit_designer_invoice'
    post 'designers/edit_invoice/:edit_invoice_link' => 'designers#edit_designer_invoice', :as => 'edit_designer_invoice'
    get 'designers/edit_credit_note/:edit_credit_note_link' => 'designers#edit_credit_note'
    post 'designers/edit_credit_note/:edit_credit_note_link' => 'designers#edit_credit_note', :as => 'edit_credit_note'
    get 'designers/:id/invoices' => 'designers#upload_invoices'
    post 'designers/:id/invoices' => 'designers#upload_invoices',:as => 'designer_upload_invoices'

    # Creating coupon for order
    post '/admin/create_coupon_order' => 'admin#create_coupon_order', :as => 'admin_create_coupon_order'
    get 'admin/review_designer_batches' => 'admin#review_designer_batches', :as => 'review_designer_batches'
    post 'admin/review_designer_batches' => 'admin#review_designer_batches'
    post '/designable_create' => 'designs#create_designable', :as => 'create_designable'

    resources :promotions, path: '/admin/promotions', controller: 'promotion_pipe_lines'

    get "/admin/additional_discount" => "vendor_additional_discounts#index", :as => "additional_discount_index"
    get "/admin/additional_discount/:designer_id/edit" => "vendor_additional_discounts#edit"
    post "/admin/additional_discount/:designer_id/edit" => "vendor_additional_discounts#update", :as => "additional_discount_edit"

    get '/admin/manage_pages' => "admin#manage_pages", :as => 'admin_manage_pages'
    post "admin/set_facebook_page" => "admin#set_facebook_page", :as => "admin_set_facebook_page"
    get "admin/fb_publish_new" => "admin#fb_publish_new", :as => "admin_fb_publish_new"
    post "admin/fb_publish_create" => "admin#fb_publish_create", :as => "admin_fb_publish_create"
    get "admin/add_designs_to_collection" => "admin#add_designs_to_collection", :as => "add_designs_to_collection"
    post "admin/add_designs_to_collection_create" => "admin#add_designs_to_collection_create", :as => "add_designs_to_collection_create"
    get "admin/add_designs_to_category" => "admin#add_designs_to_category"
    post "admin/add_designs_to_category" => "admin#add_designs_to_category", :as => "add_designs_to_category"

    get "/admin/add_designs_to_featured_products" => "admin#add_designs_to_featured_products"
    post "/admin/add_designs_to_featured_products" => "admin#add_designs_to_featured_products", :as => "add_designs_to_featured_products"

    get "admin/move_collection_to_category" => "admin#move_collection_to_category", :as => "move_collection_to_category"
    post "admin/move_collection_to_category_create" => "admin#move_collection_to_category_create", :as => "move_collection_to_category_create"

    get "admin/add_designs_to_oos_alert" => "admin#add_designs_to_oos_alert", as: "add_designs_to_oos_alert"
    post "admin/add_designs_to_oos_alert" => "admin#add_designs_to_oos_alert_create", as: "add_designs_to_oos_alert_create"

    get "admin/upload_newsletter_image" => "admin#upload_newsletter_image", as: "upload_newsletter_image"
    post "admin/upload_newsletter_image" => "admin#upload_newsletter_image_create", as: "upload_newsletter_image_create"

    get "admin/bulk_product_list_new" => "admin#bulk_product_list_new", :as => "admin_bulk_product_list_new"
    post "admin/bulk_product_list_create" => "admin#bulk_product_list_create", :as => "admin_bulk_product_list_create"
    get "admin/stats" => "admin#stats", :as => "admin_stats"
    get "admin/cache_clear" => "admin#cache_clear", :as => "cache_clear"
    get "admin/enable_design_video" => "admin#enable_design_video"
    post "admin/enable_design_video" => "admin#enable_design_video", :as => "enable_design_video"
    get "admin/stats_summary" => "admin#stats_summary", :as => "admin_stats_summary"
    get "admin/tag_stats/:tag" => "admin#tags_stats", :as => "admin_tag_stats"
    get "admin/stats_summary1" => "admin#stats_summary1", :as => "admin_stats_summary1"
    get "admin/stats_calculations" => "admin#stats_calculations", :as => "admin_stats_calculations"
    get "admin/cod_summary" => "admin#cod_summary", :as => "admin_cod_summary"
    get "admin/canceled" => "admin#canceled", :as => "admin_canceled"
    get "admin/stitching_done_orders" => "admin#stitching_done_orders"
    get 'admin/addons_for_order' => 'admin#addons_for_order'
    get 'admin/resend_addon_payment_link' => 'admin#resend_addon_payment_link', as: 'resend_addon_payment_link'
    post 'admin/add_addon_from_admin' => 'admin#add_addon_from_admin' ,as: 'add_addon_from_admin'
    get 'admin/mark_additional_payment_complete' => 'admin#mark_additional_payment_complete', as: 'mark_additional_payment_complete'
    get "admin/stitching_priority_list" => "admin#stitching_priority_list"
    post "admin/stitching_priority_list" => "admin#stitching_priority_list", as: "stitching_priority_list"
    get "admin/get_notes_bifurcated" => "admin#get_notes_bifurcated"

    scope path: '/warehouse_orders', controller: :warehouse_orders do
      get  'create_warehouse_order' => :create_warehouse_order
      get 'warehouse_cancel_orders' => :warehouse_cancel_orders
      post 'submit_warehouse_order' => :submit_warehouse_order
      get 'index'   => :index,  as: :warehouse_order_index
      post 'update_quantity' => :update_quantity,as: :update_warehouse_line_item_quantity
      get 'generate_buckets' => :generate_buckets, as: :generate_buckets
      post 'merge_racks' => :merge_racks, as: :merge_racks
      get ':number/order_detail' => :show,   as: :warehouse_order_show
      get 'mark_item_received' => :mark_item_received, as: :warehouse_order_mark_item_received
      get 'mark_warehouse_order_complete' => :mark_warehouse_order_complete
      get 'manage_warehouse_inventory' => :manage_warehouse_inventory
      get 'update_warehouse_inventory' => :update_warehouse_inventory
      get 'warehouse_code_sticker' => :warehouse_code_sticker
      get 'manage_warehouse_orders' => :manage_warehouse_orders
      get 'get_warehouse_rack_locations' => :get_warehouse_rack_locations
      get 'mark_warehouse_order_cancel' => :mark_warehouse_order_cancel
      post 'update_warehouse_size' => :update_warehouse_size, as: :update_warehouse_size
      post ':id/create_rtv' => :create_rtv, as: :create_rtv
      get ':number/warehouse_order_csv_report' => :warehouse_order_csv_report, as: :warehouse_order_csv_report
      post 'update_rtv_quantity' => :update_rtv_quantity, as: :update_rtv_quantity
      post 'assign_to_orders' => :assign_to_orders, as: :assign_to_orders
    end

    get "admin/campaign_stats" => "admin#campaign_stats", :as => "admin_campaign_stats"
    get "admin/optional_pickups" => "admin#optional_pickups", as: 'optional_pickups'
    get "admin/category_stats" => "admin#category_stats", as: "admin_category_stats"
    get "admin/category_subreports/:category_id" => "admin#category_subreports", as: "admin_category_subreports"
    get "admin/vendorwise_inventory/:category_id" => "admin#vendorwise_inventory"

    get "admin/sales_pending" => "admin#sales_pending", :as => "admin_sales_pending"
    get 'admin/view_manifest' => 'admin#view_manifest', as: 'admin_view_manifest'

    get "admin/inventory" => "admin#inventory", :as => "admin_inventory"
    get "admin/international_dispatch_report" => "admin#international_dispatch_report", :as => "admin_international_dispatch_report"
    get "admin/domestic_dispatch_report" => "admin#domestic_dispatch_report", :as => "admin_domestic_dispatch_report"
    get "admin/potential_order_dispatch" => "admin#potential_order_dispatch"

    get "admin/summary" => "admin#summary", :as => "admin_summary"
    get "admin/products_summary" => "admin#products_summary", :as => "admin_products_summary"
    get "admin/products_by_day" => "admin#products_by_day", :as => "admin_products_by_day"
    get "admin/inventory_by_order" => "admin#inventory_by_order", :as => "admin_inventory_by_order"
    get "admin/report_page_shipment_time" => "admin#report_page_shipment_time"
    get "admin/designer_order_list" => "admin#pending_orders", :as => "admin_pending_orders"
    get "admin/pending_orders_report" => "admin#pending_orders_report", :as => "pending_orders_report"
    get "admin/pending_orders" => redirect("/admin/designer_order_list?state=pending")
    get "admin/designer_order_list" => "admin#pending_orders"
    get "admin/order_santiy" => "admin#order_sanity", :as => "admin_order_sanity"
    get "admin/ready_for_dispatch" => "admin#ready_for_dispatch", :as => "admin_ready_for_dispatch"
    get "admin/orders/stitching_items" => "admin#stitching_items"
    get "admin/stitching_pending_orders" => "admin#stitching_pending_orders", as: 'stitching_pending_orders'

    get "admin/designer_reports" => "admin#designer_reports", :as => "admin_designer_reports"
    post "admin/create_designer_issue" => "admin#create_designer_issue", :as => 'admin_create_designer_issue'
    post "admin/create_claim_design" => "admin#create_claim_design"
    get "admin/view_claim_requests" => "admin#view_claim_requests", as: 'admin_view_claims'

    get "admin/rtv_pending" => "admin#rtv_pending"
    get "admin/rack_check_page" => "admin#rack_check_page"
    get "admin/bulk_upload_new" => "admin#bulk_upload_new", :as => "admin_bulk_upload_new"
    post "admin/bulk_upload_create" => "admin#bulk_upload_create", :as => "admin_bulk_upload_create"
    get "admin/availability_update_new" => "admin#availability_update_new", :as => "admin_availability_update_new"
    post "admin/availability_update_do" => "admin#availability_update_do", :as => "admin_availability_update_do"

    get "admin/review" => "admin#designs_under_review", :as => "admin_designs_under_review"
    post "admin/review" => "admin#designs_under_review"
    get "admin/oos" => "admin#oos", :as => "admin_oos"
    # get "admin/tailor_payout" => "admin#tailor_payout", as: "admin_tailor_payout"
    # post "admin/tailoring_marked_paid" => 'admin#tailoring_marked_paid'
    get "admin/tailoring_info_page" => 'admin#tailoring_info_page', :as => "admin_tailoring_info"
    get "admin/logistic_info_page" => 'admin#logistic_info_page', :as => 'admin_logistic_info'
    get "stylist/review_measurements" => 'stylist#review_measurements', as: 'review_measurements'
    get "stylist/get_attributes_data" => 'stylist#get_attributes_data'
    get "stylist/reject_measurement" => 'stylist#reject_measurement', as: 'reject_measurement'
    get "stylist/approve_measurement" => 'stylist#approve_measurement', as: 'approve_measurement'
    post 'stylist/hold_measurement' => 'stylist#hold_measurement', as: 'hold_measurement'
    get 'stylist/reassignment' => 'stylist#reassignment_page' #, as: 'stylist_reassignment'
    post 'stylist/reassignment' => 'stylist#reassignment_page', as: 'stylist_reassignment_page'
    get "stylist/index" => 'stylist#index', as: 'stylist_index'
    get 'admin/manage_stylist' => 'stylist#manage_stylist'
    post 'admin/manage_stylist' => 'stylist#manage_stylist', as: 'manage_stylist'
    get 'stylist/daily_track_report' => 'stylist#daily_track_report'

    get "admin/designer_issues" => "admin#designer_issues", :as => "admin_designer_issues"
    get "admin/health" => "admin#health", :as => "admin_health"
    get "admin/designers_summary" => "admin#designers_summary", :as => "admin_designers_summary"

    get "admin/designers" => "admin#designers", :as => "admin_designers"
    post "admin/designers" => "admin#designers"
    post "admin/approve_gst" => "admin#approve_designer_gst", :as => "approve_gst"
    get "admin/designers/:designer_id/edit" => "admin#designers_edit", :as => "admin_edit_designer"
    get "admin/designers/:designer_id" => "admin#designers_show", :as => "admin_designer"
    put "admin/designers/:designer_id" => "designers#designers_update", :as => "admin_update_designers"

    get "admin/report_list" => "admin#report_list", :as => "admin_report_list"
    get "admin/sent_stitching_list" => "admin#sent_stitching_list", :as => "admin_sent_stitching_list"
    get "admin/not_sent_stitching_list" => "admin#not_sent_stitching_list", :as => "admin_not_sent_stitching_list"
    get "admin/enable_skip_qc" => "admin#enable_skip_qc_for_designs"
    post "admin/enable_skip_qc" => "admin#enable_skip_qc_for_designs"
    get 'admin/upload_vendor_payouts' => 'admin#upload_vendor_payouts'
    post 'admin/upload_vendor_payouts' => 'admin#upload_vendor_payouts'

    get "admin/change_qty_of_designs" => "admin#change_qty_of_designs", :as => "change_qty_of_designs"
    put "admin/change_qty_of_designs_update" => "admin#change_qty_of_designs_update", :as => "change_qty_of_designs_update"

    get "admin/designer_invoices" => "admin#designer_invoices"
    post "admin/designer_invoices" => "admin#designer_invoices" #, :as => "admin_designer_invoices"

    get "admin/localytics_push" => "admin#localytics_push", as: "admin_localytics_push"
    post "admin/push_data_to_localytics" => "admin#push_data_to_localytics", as: "admin_push_data_to_localytics"

    get 'admin/payout_management' => 'admin#payout_management'
    post 'admin/payout_management' => 'admin#payout_management', as: 'payout_management'
    post 'admin/payout_management_save_records' => 'admin#payout_management_save_records', as: 'payout_management_save_records'
    post 'admin/payout_management_update_paid_records' => 'admin#payout_management_update_paid_records', as: 'payout_management_update_paid_records'
    post 'admin/adjustment_creation_panel' => 'admin#adjustment_creation_panel', as: 'adjustment_creation_panel'
    get 'admin/sales_register' => 'admin#sales_register', as: 'sales_register'
    
    get 'admin/catalog_update' => 'admin#catalog_update'
    post 'admin/catalog_update' => 'admin#catalog_update', as:  'catalog_update'
    resources :carts

    #Currency Switcher
    get "user/currency/:country_code" => "users#currency", :as => "users_currency"
    get "user/:id/order/:order_number/return_items" => "users#return_items", :as => "user_order_return_items"
    get "user/:id/return_orders" => "users#return_orders", :as => "return_orders"
    get 'user/:id/order/:order_number/cancel_cod_order' => 'users#cancel_cod_order' , as: 'user_cancel_cod_order'
    post 'user/save_review' => 'users#user_review'
    get 'user/:id/my-reviews' => 'users#user_review', as: 'user_reviews'
    get 'user/:id/wallet' => 'users#wallet', as: 'wallet'
    post 'returns/update_return_state' => 'returns#update_return_state', as: 'update_returns_state'
    get 'returns/check_tracking_number' => 'returns#check_tracking_number'
    post '/return/cashgram/callback' => 'returns#cashgram_callback'
    post 'returns/generate_otp' => 'returns#generate_otp'
    post 'user/upload_full_size_photo' => 'users#upload_full_size_photo', as:'upload_full_size_photo'

    get '/returns/:order_no/new_case' => "returns#new_case", :as => "new_return_case"
    get '/returns/designer_order/' => 'returns#designer_order', :as => 'returns_designer_order'
    post '/return_designer_order/add_notes' => 'returns#designer_order_add_notes', :as => 'returns_designer_order_add_notes'
    get '/orders/:order_no/returns' => 'returns#order_returns', :as => 'returns_order_returns'
    get '/return_designer_order/remove_line_item' => 'returns#remove_line_item', :as => 'returns_remove_line_item'
    get '/returns/get_account_no_length' => 'returns#get_account_no_length'
    get 'returns/upload_return_sheet' => 'returns#upload_return_sheet'
    post 'returns/upload_return_sheet' => 'returns#upload_return_sheet', :as => 'upload_returns_sheet'
    post 'returns/send_return_mail' => 'returns#send_return_mail', :as => 'send_return_mail_to_buyer'
    get 'returns/fetch_couriers_from_clickpost' => 'returns#fetch_couriers_from_clickpost'
    get '/returns/:id/user_wallet' => 'returns#user_wallet', as: 'user_wallet'
    put '/returns/:id/transfer_to_wallet' => 'returns#transfer_to_wallet', as: 'transfer_to_wallet'
    post 'returns/extend_coupon' => 'returns#extend_coupon', as: 'extend_coupon'
    post 'returns/paypal_automated_returns' => 'returns#paypal_automated_returns', :as => 'paypal_automated_returns'
    get '/returns/get_line_items_to_return' => 'returns#get_line_items_to_return', as: 'get_line_items_to_return'
    post '/return_designer_order/update_return_qty' => 'returns#update_return_qty'
    get '/return_designer_order/create_return_through_cancel_mail' => 'returns#create_return_through_cancel_mail'

    resources :returns

    get '/offers' => "pages#designer_discounts", as: "designer_discounts"

    get "/buy1get1-offers(/:facets)" => redirect("/buy-m-get-n-free")
    get "/buy-m-get-n-free(/:facets)" => "store#catalog2", kind: 'b1g1'
    get "/flash-deals(/:facets)" => "store#catalog2", kind: 'flash_deals'
    get "/x-price(/:facets)" => "store#catalog2", kind: 'direct_dollar'
    get '/price-match-guarantee' => 'pages#price_match_guarantee_tnc', as: 'price_match_guarantee_tnc'
    get "/offer-terms-and-conditions" => "pages#offer_tnc", as: 'offer_tnc'

    get '/coupons' => "coupons#all", :as => "coupons"
    get '/coupons/:id' => "coupons#show", :as => "coupon"
    get "admin/coupon_dashboard" => "coupons#coupon_dashboard", as: "coupon_dashboard"
    post '/carts/apply_wallet_discount' => 'carts#apply_wallet_discount', as: 'apply_wallet_discount'
    post '/carts/apply_coupon' => "carts#apply_coupon"
    post '/carts/reload' => "carts#reload"
    post '/carts/update_gift_price' => 'carts#update_gift_wrap_price'

    scope :controller => :follows do
      post '/follow/designer' => :follow_designer
      post '/unfollow/designer' => :unfollow_designer
      post '/follow/user' => :follow_user
      post '/unfollow/user' => :unfollow_user
      post '/follow/design' => :follow_design
      post '/unfollow/design' => :unfollow_design
      post '/publish/feed' => :publish_feed
      post '/recommend/design' => :recommend_design
    end

    post "line_items/save_quantity" => "line_items#save_quantity"
    post "line_items/save_note" => "line_items#save_note"
    get 'line_items/add_addon_on_current_cart/:id' => 'line_items#add_addon_on_current_cart', :as => 'add_addon_on_current_cart'
    get "line_items/get_unscaled_price" => 'wms_callback#get_unscaled_price', as: 'get_unscaled_price'
    post 'line_items/received_warehouse_product' => 'line_items#received_warehouse_product'
    post 'wms_callback/create_adjustment' => 'wms_callback#create_adjustment'
    post "line_items/get_rack_finder_details" => "wms_callback#get_rack_finder_details"
    post "carts/save_email" => "carts#save_email"
    post 'carts/generate_otp' => 'carts#generate_otp'
    post 'carts/verify_otp' => 'carts#verify_otp'

    resources :line_items, :only => [:destroy, :create] do
      collection do
        post :add_custom_stitching
      end
    end

    resources :freshdesk_tickets do
      collection do
        post :create_update_webhook
      end
    end

    scope :path => '/invite', :controller => :invitation do
      get '/' => :invite , :as => "invite"
      get 'gmail/contacts' => :fetch_and_show_gmail_contacts, :as => "gmail_contacts"
      post 'gmail/save_contacts' => :save_gmail_contacts, :as => "save_gmail_contacts"
      post 'email/save_contacts' => :save_email_contacts, :as => "save_email_contacts"
    end

    get 'unicommerce/authToken' => 'unicommerce#authtoken'
    get 'unicommerce/orders' => 'unicommerce#orders'
    get 'unicommerce/products' => 'unicommerce#products'
    get 'unicommerce/productsCount' => 'unicommerce#products_count'
    get 'unicommerce/invoiceDetails' => 'unicommerce#invoice_details'
    post 'unicommerce/updateInventory' => 'unicommerce#update_inventory'
    get 'unicommerce/orders/labels' => 'unicommerce#labels'
    post 'unicommerce/orders/labels' => 'unicommerce#create_labels'
    post 'unicommerce/pricing/update' => 'unicommerce#pricing_update'
    get 'unicommerce/courierDetails' => 'unicommerce#courier_details'
    post 'unicommerce/orders/dispatch' => 'unicommerce#dispatch_order'

    get "tags" => "store#tags1", :as => "search_tag"
    post "sell_page" => "pages#sell_page_form", as: "sell_page_form"
    post "seller_login" => "pages#seller_app_login"
    get "seller_logout" => "pages#seller_app_logout"
    get "get_designable" => "designs#get_designable"
    get 'pages/show_designer/:id' => "pages#show_designer"
    get 'pages/terms_and_conditions_vendors', to: 'pages#terms_and_conditions_vendors', :as=> 'tnc'
    get 'customers/reviews', to: 'reviews#site_review', as:'site_reviews'
    get 'customers/under_review', to: 'reviews#design_review', as:'design_review'
    post 'customers/reviews/create_review', to: 'reviews#site_review'
    post 'customers/reviews/moderate_review', to: 'reviews#site_review'
    get '/reviews/reviews_by_filter', to: 'reviews#reviews_by_filter'
    get 'pages/vendor_agreement/:id', to: 'pages#vendor_agreement'
    post 'pages/vendor_agreement/:id', to: 'pages#vendor_agreement', :as=> 'vendor_agreement'
    get 'pages/stitching_information' => 'pages#stitching_information', :as=> 'stitching_information'

    get 'pages/faq' => 'pages#faq'
    get 'pages/helpcenter'=> 'pages#help_center'
    get 'pages/prohibited' => 'pages#prohibited'
    get 'pages/help' => 'pages#designer_help_center',as: 'designers_help_center'
    post 'pages/post_freshdesk_ticket' => 'pages#post_freshdesk_ticket'
    #get "search" => "store#search", :as => "search_search"
    get "search" => "store#search1", :as => "search_search"
    
    post 'orders/update_phone_number' => 'orders#update_phone_number'
    post "order/response" => "orders#res"
    post "order/ccavenue_mpcg_response" => "orders#ccavenue_mpcg_response"
    post "order/paypal_response" => "orders#paypal_res"
    post 'order/paypal_execute' => 'orders#paypal_execute'
    get 'order/paypal_retry' => 'orders#paypal_response_handling'
    post "order/g2a_response" => "orders#g2a_response"
    get "order/stripe_response" => "orders#stripe_response"
    # post "order/cod_verify" => "orders#cod_verify"
    get "order/cod_verify" => "orders#cod_verify", :as => "orders_cod_verify"
    post "order/payu_response" => "orders#payu_response"
    post 'orders/update_shipping_address' => 'orders#update_shipping_address'
    #paytm gateway response
    post "order/paytm_response" => "orders#paytm_response"
    
    get "order/amazon_success" => "orders#amazon_return"
    post "order/amazon_response" => "orders#amazon_response"

    get "orders/tracking_details" => "orders#tracking_details", :as => "tracking_details"
    get "order/gharpay/notification" => "orders#gharpay_push_notification"
    get 'orders/get_order_details' => 'orders#get_order_details'
    get 'orders/get_epst_lpst_details' => 'orders#get_epst_lpst_details', as: 'get_epst_lpst_details'

    get "orders/new2" => "orders#new2"
    get "orders/direct_orders" => "orders#direct_orders", :as => "direct_orders"

    post 'orders/duplicate' => 'orders#duplicate', :as => 'duplicate'
    get  "orders/best_zone_stitching_call" => "orders#best_zone_stitching_call"
    get "orders/get_best_shipper" => "orders#get_best_shipper", :as => "get_best_shipper"
    get "orders/get_volumetric_weight" => "orders#get_volumetric_weight", :as => "get_volumetric_weight"
    post "orders/update_cod_order_state" => "orders#update_cod_order_state", :as => "update_cod_order_state"
    post "orders/update_duplicate_order_state" => "orders#update_duplicate_order_state", :as => "update_duplicate_order_state"
    post "orders/razorpay_submission" => "orders#razorpay_submission"
    get 'orders/unpack_to_order_detail' => 'orders#unpack_to_order_detail', as: 'unpack_to_order_detail'
    resources :orders, param: :number do
      get :order_detail
      member do
        post :send_order_sms
      end
    end
    post "/images" => "images#create", :as => "images"


    get "order/:action/:id", :controller => "orders"
    get "order/retry/:id" => "orders#retry", :as => "order_retry"
    get "order/retry_cod/:id" => "orders#retry_cod", :as => "order_retry_cod"

    scope :path => '/orders', :controller => :orders do
      post "delete_line_item" => :delete_line_item, :as => "delete_line_item_order"
      post "update_line_item" => :update_line_item, :as => "update_line_item_order"
      post "send_order_ack_email" => :send_order_ack_email
      post "send_ship_direct_email" => :send_ship_direct_email
      post "convert_to_gharpay" => :convert_to_gharpay
      post "multiple_local_invoice_links" => :multiple_local_invoice_links, as: "multiple_local_invoice_links"
      post "send_paypal_invoice" => :send_paypal_invoice
      post "send_payu_invoice" => :send_payu_invoice
      post "add_design" => :add_design, :as => "order_add_design"
      post "add_variant" => :add_variant, :as => "order_add_variant"
      post "add_notes" => :add_notes, :as => "order_add_notes"
      post "add_tags" => :add_tags, :as => "order_add_tags"
      post 'customer_order_mail' => :customer_order_mail, :as => 'customer_order_mail'
      # get "invoice/:number" => :invoice
      # post "invoice/:number" => :invoice, :as => "invoice"
      get "check_order_label/:number" => :check_order_label, as: 'check_order_label'
      post "check_order_label" => :check_order_label
      get "label/:number" => :label, :as => "label"
      get "pay_by_paypal/:number" => :pay_by_paypal, :as => "pay_by_paypal"
      get "pay_by_card/:number" => :pay_by_card, :as => "pay_by_card"
      get "check_items/:number" => :check_items, :as => "check_items"
      post 'apply_coupon_on_order' => :apply_coupon_on_order, as: 'apply_coupon_on_order'
    end

    post 'paypal_webhooks/order_dispute'
    post 'paypal_webhooks/order_confirmation'

    get "orders/ups_invoice/:number" => "orders#ups_invoice"
    post "orders/ups_invoice/:number" => "orders#ups_invoice", :as => "ups_invoice"
    get "orders/fedex_invoice/:number" => "orders#fedex_invoice"
    post "orders/fedex_invoice/:number" => "orders#fedex_invoice", :as => "fedex_invoice"
    get "orders/:number/clickpost_shippers" => "orders#clickpost_shippers"
    post "orders/clickpost_shippers" => "orders#clickpost_shippers", :as => "clickpost_shippers"
    get "orders/xindus_invoice/:number" => "orders#xindus_invoice"
    post "orders/xindus_invoice/:number" => "orders#xindus_invoice", :as => "xindus_invoice"
    get "orders/xpressbees_ups_invoice/:number" => "orders#xpressbees_ups_invoice"
    post "orders/xpressbees_ups_invoice/:number" => "orders#xpressbees_ups_invoice", :as => "xpressbees_ups_invoice"
    get "orders/dhl_invoice/:number" => "orders#dhl_invoice"
    post "orders/dhl_invoice/:number" => "orders#dhl_invoice", :as => "dhl_invoice"
    get "orders/delhivery_invoice/:number" => "orders#delhivery_invoice"
    post "orders/delhivery_invoice/:number" => "orders#delhivery_invoice", as: 'delhivery_invoice'
    get "orders/skynet_invoice/:number" => "orders#skynet_invoice"
    post "orders/skynet_invoice/:number" => "orders#skynet_invoice", as: "skynet_invoice"
    get "orders/dhl_ecom_invoice/:number" => "orders#dhl_ecom_invoice"
    post "orders/dhl_ecom_invoice/:number" => "orders#dhl_ecom_invoice", as: "dhl_ecom_invoice"
    get "shipments/create_dhl_ecom_pickups" => "shipments#create_dhl_ecom_pickups"
    post "shipments/create_dhl_ecom_pickups" => "shipments#create_dhl_ecom_pickups", as: "dhl_ecom_pickup"
    post "shipments/get_dhl_ecom_pickup_label",to: "shipments#get_dhl_ecom_pickup_label", as: "get_dhl_ecom_pickup_label"
    post 'shipments/update_reverse_pickup_status' => 'shipments#update_reverse_pickup_status'
    get "orders/atlantic_invoice/:number" => "orders#atlantic_invoice"
    post "orders/atlantic_invoice/:number" => "orders#atlantic_invoice", :as => "atlantic_invoice"
    post 'shipments/calculate_best_shipper' => 'shipments#calculate_best_shipper', as: 'calculate_best_shipper'

    get "orders/aramex_international_shipping/:number" => "orders#aramex_international_invoice"
    post "orders/aramex_international_shipping/:number" => "orders#aramex_international_invoice", :as => "aramex_international_invoice"
    get "order_reports" => "order_reports#index", :as => "order_report"
    get "payouts" => "order_reports#order_payouts", :as => "payouts"
    post "orders/make_designer_payout" => "order_reports#make_payout", :as => 'make_designer_order_payout'
    get 'designer_orders/rack_code_sticker/:id' => 'designer_orders#rack_code_sticker', as: 'rack_code_sticker'
    get 'designer_orders/generate_combined_awb_unpacks' => 'designer_orders#generate_combined_awb_unpacks', as: 'generate_combined_awb_unpacks'
    post "designer_orders/:id/mark_received" => "designer_orders#mark_received", :as => "mark_received_order"
    get "pages/:action", :controller => "pages", :as => "page"
    get "text_me" => "pages#text_me"
    get "Rakhi-2016" => "pages#rakhi"
    get "Gemstones" => "pages#landing", landing: 'gemstones', as: 'gemstones_landing'
    get "orders/combined_sticker/:id" => "orders#combined_sticker" ,as: 'combined_sticker'

    scope :path => '/event_trigger', :controller => :event_trigger do
      post "trigger_event_for_order" => :trigger_event_for_order, :as => "order_event_trigger"
      post "trigger_event_for_designer_order" => :trigger_event_for_designer_order, :as => "designer_order_event_trigger"
      post "trigger_event_for_return_designer_order" => :trigger_event_for_return_designer_order, :as => "return_designer_order_event_trigger"
      post "trigger_event_for_return_order" => :trigger_event_for_return_order, :as => "return_order_event_trigger"
      post 'trigger_event_for_design' => :trigger_event_for_design, :as => 'trigger_event_for_design'
      get 'trigger_event_for_designer' => :trigger_event_for_designer, :as => 'trigger_event_for_designer'
      post 'trigger_event_for_designer_issue' => :trigger_event_for_designer_issue, :as => 'designer_issue_event_trigger'
    end

    resources :vendor_promotions , only: [:index]do   
      collection do
        get  :invoice
        post  :update_state
        post  :create_promotion
      end
    end

    scope :path => '/designer_orders', :controller => :designer_orders do
      post "add_tracking_num" => :add_tracking_num, :as => "designer_order_tracking"
      post "mark_pickup_done" => :mark_pickup_done, :as => "designer_order_mark_pickup_done"
      get 'check_duplicate_tracking_num' => :check_duplicate_tracking_num
      post 'mark_warehouse_order_dispatched' => :mark_warehouse_order_dispatched, as: :designer_order_mark_warehouse_order_dispatched
      post "add_notes" => :add_notes, :as => "designer_order_add_notes"
      post "add_secondary_tracking" => :add_secondary_tracking_details, :as=>"designer_order_add_secondary_tracking"
      post "add_tags" => :add_tags, :as => "designer_order_add_tags"
      post 'gst_status_update' => :gst_status_update, as: 'gst_status_update'
      get "get_encrypted_token" => :get_encrypted_token, as: 'get_encrypted_token'
    end

    post '/designer_orders/:id/change_rack' => "designer_orders#change_rack"
    get "/designer_orders/:id/rtv_invoice" => "designer_orders#new_rtv_invoice", as: "designers_rtv_invoice"
    post "/designer_orders/:id/rtv_invoice" => "designer_orders#create_rtv_invoice", as: "designers_create_rtv_invoice"
    get '/designers/:designer_id/designer_orders/warehouse_order/:id' => 'designer_orders#show_warehouse_order', as: 'show_warehouse_order'
    get '/designers/:designer_id/warehouse_orders' => 'designer_orders#warehouse_orders', as: 'designer_warehouse_orders'
    post '/designers/:designer_id/dispatch_orders_in_bulk' => 'designer_orders#dispatch_orders_in_bulk', as: 'designer_order_dispatch_orders_in_bulk'
    post "/designer_orders/arrange_pickup" => "designer_orders#arrange_pickup"

    post "/designer_orders/create_adjustment" => "designer_orders#create_adjustment", as: "create_adjustment"
    get "designers/:id/pickup" => "designers#pickup", :as => 'designer_pickup'
    get "designers/:id/view_arrange_pickup" => "designers#view_arrange_pickup", as: 'designer_view_arrange_pickup'
    get "designers/:designer_id/designer_orders/show_mailer/:id" => "designer_orders#show_mailer"
    post "designers/:id/set_vacation_mode" => "designers#set_vacation_mode", :as => "set_vacation_mode"
    post 'designers/:id/set_inactive_mode' => 'designers#set_inactive_mode', as: 'set_inactive_mode'
    post "designers/:id/set_facebook_page" => "designers#set_facebook_page", :as => "set_facebook_page"
    get "designers/:designer_id/payments" => "designer_orders#payments", :as => "designers_payments"
    get "designers/:designer_id/export_orders" => "designer_orders#export_orders", :as => "designers_export_orders"
    get 'designers/:id/review_report', to: 'designers#review_report', as: 'review_report'
    get 'designers/:id/review_report/:design_id', to: 'designers#design_review_report', as: 'design_review_report'
    post 'designers/:id/review_report/:design_id', to: 'designers#design_review_report'
    get 'designers/:id/ranking', to: 'designers#rank', as: 'vendor_rank'
    get '/designer_orders/upload_qc_issue' => "designer_orders#upload_qc_issue"
    post '/designer_orders/upload_qc_issue' => "designer_orders#upload_qc_issue"
    post 'line_items/add_vendor_notes' => 'line_items#add_vendor_notes', as: 'add_vendor_notes'
    post 'line_items/add_stylist_notes' => 'line_items#add_stylist_notes', as: 'add_stylist_notes'       
    get 'line_items/line_item_label' => 'line_items#line_item_label', as: 'line_item_label'
    {'stitching_measurement_label' => 'stitching', 'unpacking_label' => 'unpacking'}.each do |link_name, label_type|
      get "line_items/:item_id/#{link_name}" => redirect{|params, request| "/line_items/line_item_label?item_id=#{params[:item_id]}&format=#{params[:format]}&label_type=#{label_type}"}, as: link_name
    end    

    get "designers/:id/payout_invoices" => "designers#payout_invoices", :as => "payout_invoices"
    get 'designers/:id/account_health' => 'designers#account_health',as: 'designers_account_health'
    get 'designers/:id/order_quality_report' => 'designers#order_quality_report',as: 'designers_order_quality_report'
    get 'designers/:id/historical_data(/:metric_definition_id)' => 'designers#historical_data',as: 'designers_historical_data'
    post 'designers/bulk_update_commission_rate' => 'designers#bulk_update_commission_rate'
    get "designers/:id/all_campaign" => "designers#all_campaign", :as => "all_campaign"
    post "seller_campaigns/:seller_campaign_id/participate" => "designer_campaign_participations#create", as: "participate_in_campaign"
    delete "designer_campaign_participations/:id/cancel" => "designer_campaign_participations#cancel", as: "cancel_participation"
    
    if  defined? RailsAdmin
      mount RailsAdmin::Engine => '/admin', :as => 'rails_admin'
      get '/designers/:id/boutique(/:facets)' => 'store#catalog2',skip_designer_show: true, as: 'designer_boutique'
    end
    get "/designers/:id(/:facets)" => "store#catalog2", :as => "search_designer"
    post "design/add_to_reqlist" => "designs#add_to_reqlist", :as => "design_add_to_reqlist"

    resources :contacts, only: [:new, :create]
    devise_for :accounts, :controllers => { :registrations => "accounts/registrations", :omniauth_callbacks => "accounts/omniauth_callbacks", :sessions => "accounts/sessions", confirmations: 'accounts/confirmations', passwords: 'accounts/passwords'}
    devise_scope :account do
      get "accounts/sign_out" => "devise/sessions#destroy", as: "logout"
      get '/sellers/sign_in' => 'accounts/sessions#designer_new', :account => { :accountable_type => 'designer' }
      post '/accounts/forgot_password' => 'accounts/sessions#generate_new_password_email', :as => 'generate_new_password_email'
      post '/accounts/exist' => 'accounts/registrations#exist'
    end

    scope :path => '/user', :controller => :users do
      get ':id/following' => :following, :as => "users_following"
      get ':id/followers' => :followers, :as => "users_followers"
      get ':id/friends' => :friends, :as => "users_friends"
      get ':id/designers' => :designers, :as => "users_designers"
      get ':id/orders' => :orders, :as => 'user_orders'
      get ':id/addresses' => :addresses, :as => 'user_addresses'
      get ':id/measurements' => :measurements, as: 'user_measurements'
      get ':id/invite' => :invite, :as => 'user_invite'
      get 'signup' => :signup, :as => "users_signup"
      get 'me' => :me
      get ':id' => :show, :as => 'user'
      #post 'forgot_password' => 'users#generate_new_password_email', :as => 'generate_new_password_email'
    end

    scope :path => '/user/me' do
      resources :addresses
    end

    # devise_for :accounts, :controllers => { :registrations => "accounts/registrations", :omniauth_callbacks => "accounts/omniauth_callbacks"  }

    get '/play/tot' => 'plays#tot', :as => "plays_tot"
    get '/play/quiz' => 'plays#quiz', :as => "plays_quiz"

    # get '/feed' => 'timeline_events#feed', :as => 'timeline_events_feed'
    # get 'timeline_event/:id' => 'TimelineEvents#show', :as => 'timeline_event'

    get 'best-sellers' => 'store#best_sellers'
    
    get "store/catalog/" => redirect("/"), :as => "store_catalog"
    #get "store/catalog/" => redirect("/"), :as => "store_catalog2"

    get "store/bangles(/:facets)" =>redirect{ |params,request| page_redirect.call("/store/bangles-and-bracelets",request,params) }
    get "store/bracelets(/:facets)" =>redirect{ |params,request| page_redirect.call("/store/bangles-and-bracelets",request,params) }
    get "store/Bangles(/:facets)" =>redirect{ |params,request| page_redirect.call("/store/bangles-and-bracelets",request,params) }
    get "store/Bracelets(/:facets)" =>redirect{ |params,request| page_redirect.call("/store/bangles-and-bracelets",request,params) }

    #get "store/:kind" => "store#catalog", :as => "store_search"
    get '/store/product_widgets/:type' =>'store#product_widgets', :as => 'store_product_widget'
    get "store/:kind(/:facets)" =>"store#catalog2", :as => "store_search"

    # get "collections/:collection" => "store#collection", :as => "store_collection"
    get "collections/:collection(/:facets)" => "store#catalog2", :as => "store_collection"
    get "catalogues/:catalogue(/:facets)" => "store#catalog2", :as => "store_catalogue"
    get "online/:online" => "store#online", :as => "store_online"
    get "lp/:landing" => "store#landing", :as => "store_landing"
    get "onlineshop/:landing" => "pages#landing", as: "shop_landing"
    # New simplified URL pattern for designs created after cutoff date
    get '/designs/:design_id' => 'designs#show', :as => 'simplified_design'

    # Find design, given design id - redirect to appropriate URL pattern based on creation date
    get '/d/:id' => redirect{|params, q|
      if (d = Design.find_by_id(params[:id]))
        if d.uses_new_url_structure?
          "/designs/#{d.cached_slug}"
        else
          "/designers/#{d.designer.cached_slug}/designs/#{d.cached_slug}"
        end
      else
        '/404'
      end
    }, :constraints => {:id => /\d+/}

    # post '/wholesales' => 'designs#wholesale_create', :as => 'wholesales'

    # Package Management
    get '/packages' => 'package_management#home', :as => 'packages'
    get '/package/mark_received/:designer_order_id' => 'package_management#mark_package_received', :as => 'packages_mark_received'
    get '/packages/detail' => 'package_management#detail', :as => 'packages_detail'
    get '/packages/items_received' => 'package_management#items_received', :as => 'packages_items_received'
    get '/packages/mark_warehouse_received' => 'package_management#mark_warehouse_received'
    get '/packages/items_not_marked_received' => 'package_management#items_not_marked_received', :as => 'packages_items_not_marked_received'
    get '/packages/report' => 'package_management#report', :as => 'packages_report'
    get '/packages/pending_quality_check' => 'package_management#pending_quality_check', :as => 'packages_pending_quality_check'
    get '/packages/not_unpacked_orders' => 'package_management#not_unpacked_orders', :as => 'packages_not_unpacked_orders'
    get '/packages/dispatched_from_vendors' => 'package_management#dispatched_from_vendors', :as => 'packages_dispatched_from_vendors'
    get '/packages/dispatched_today' => 'package_management#dispatched_today', :as => 'packages_dispatched_today'
    get '/packages/mark_item_received' => 'package_management#mark_item_received', :as => 'package_management_mark_item_received'
    get '/packages/mark_qc_done' => 'package_management#mark_qc_done', :as => 'package_management_mark_qc_done'
    get '/packages/build_rating' => 'package_management#build_rating'
    get '/packages/mark_stitching_sent' => 'package_management#mark_stitching_sent', :as => 'package_management_mark_stitching_sent'
    get '/packages/mark_stitching_done' => 'package_management#mark_stitching_done', :as => 'package_management_mark_stitching_done'
    get '/packages/mark_stitching_required' => 'package_management#mark_stitching_required', :as => 'package_management_mark_stitching_required'
    get '/packages/remove_stitching_required' => 'package_management#remove_stitching_required'
    get '/packages/in_scan' => 'package_management#index_in_scan'
    post'/packages/in_scan' => 'package_management#index_in_scan', :as => 'package_management_index_in_scan'
    get '/packages/mark_measurement_confirmed' => 'package_management#mark_measurement_confirmed', :as => 'package_management_mark_measurement_confirmed'
    get '/packages/mark_fabric_measurement_done' => 'package_management#mark_fabric_measurement_done', :as => 'package_management_mark_fabric_measurement_done'
    get '/packages/out_scan' => 'package_management#out_scan', :as => 'package_management_out_scan'
    post '/packages/out_scan' => 'package_management#out_scan', :as => 'package_management_out_scan_create'
    get '/packages/out_scan_report' => 'package_management#out_scan_report'
    post '/packages/out_scan_report' => 'package_management#out_scan_report', :as => 'package_management_out_scan_report'
    post '/packages/unpack_line_item' => 'package_management#unpack_line_item', :as => 'package_management_unpack_line_item'
    post '/packages/mark_qc_issue_list' => 'package_management#mark_qc_issue_list'
    post '/packages/assign_rtv_rack' => 'package_management#assign_rtv_rack'
    get '/packages/create_out_scan_batch' => 'package_management#create_out_scan_batch', :as => 'package_management_create_out_scan_batch'
    get '/packages/download_outscan_batchwise_report' => 'package_management#download_outscan_batchwise_report', :as => 'download_outscan_batchwise_report'
    get '/packages/tailoring_inscan' => 'package_management#tailoring_inscan'
    post '/packages/tailoring_inscan' => 'package_management#tailoring_inscan', as: 'tailoring_inscan'
    get '/packages/rtv_outscan' => 'package_management#rtv_outscan'
    post '/packages/rtv_outscan' => 'package_management#rtv_outscan', as: 'rtv_outscan'
    get '/packages/rtv_outscan_report' => 'package_management#rtv_outscan_report', as: 'rtv_outscan_report'
    get '/packages/handover_to_stitching' => 'package_management#handover_to_stitching'
    post '/packages/handover_to_stitching' => 'package_management#handover_to_stitching', as: 'handover_to_stitching'
    get '/packages/stylist_handover_receive' => redirect("/packages/handover_to_stitching?receiving=yes")
    get '/packages/post_tailoring_inscan_scans' => 'package_management#post_tailoring_inscan_scans'
    post '/packages/post_tailoring_inscan_scans' => 'package_management#post_tailoring_inscan_scans', as: 'post_tailoring_inscan_scans'
    post '/packages/inward_bag_generation' => 'package_management#inward_bag_generation', as: 'inward_bag_generation'    
    post '/packages/bulk_shipment_bag_generation' => 'package_management#bulk_shipment_bag_generation', as: 'bulk_shipment_bag_generation'
    post '/packages/update_bag_details' => 'package_management#update_bag_details', as: 'update_bag_details'
    post '/packages/update_bag_or_package' => 'package_management#update_bag_or_package', as: 'update_bag_or_package'
    get '/packages/stitching_searching_list' => 'package_management#stitching_searching_list', as: 'stitching_searching_list'
    get '/packages/stitching_done_panel' => 'package_management#stitching_done_panel'
    get '/packages/quality_repairable_products' => 'package_management#quality_repairable_products'    
    get '/packages/rto_inscan_panel' => 'package_management#rto_inscan_panel'
    post '/packages/rto_inward_bag_generation' => 'package_management#rto_inward_bag_generation', as: 'rto_inward_bag_generation'
    get '/packages/rto_panel' => 'package_management#rto_panel', as: 'rto_panel'
    post '/packages/mark_rto_sor_line_item_received' => 'package_management#mark_rto_sor_line_item_received'
    post '/packages/mark_rto_sor_line_item_qc_done' => 'package_management#mark_rto_sor_line_item_qc_done'
    post '/packages/assign_rack_rto_sor_line_item' => 'package_management#assign_rack_rto_sor_line_item'
    get '/packages/orders_followup' => 'package_management#orders_followup'
    post '/packages/orders_followup' => 'package_management#orders_followup', as: 'orders_followup'
    post '/packages/update_repair_status' => 'package_management#update_repair_status'    
    get '/packages/post_packaging_scan' => 'package_management#post_packaging_scan'
    post '/packages/post_packaging_scan' => 'package_management#post_packaging_scan'
    get '/packages/get_order_buckets' => 'package_management#get_order_buckets'
    get '/packages/get_line_items' => 'package_management#get_line_items'
    get '/packages/pending_panels' => 'package_management#process_pending_panels', as: 'process_pending_panels'
    get '/packages/picking_config' => 'package_management#update_picking_config'
    post '/packages/picking_config' => 'package_management#update_picking_config', as: 'picking_config'
    get '/courier/update_pincodes' => 'couriers#update_pincodes_in_bulk'
    get '/courier/update_containment_pincodes' => 'couriers#update_containment_pincodes'
    get '/courier/update_logistic_data' => 'couriers#update_logistic_data'
    post '/courier/update_pincodes' => 'couriers#update_pincodes_in_bulk', as: 'update_courier_pincodes'
    post '/courier/update_containment_pincodes' => 'couriers#update_containment_pincodes', as: 'update_containment_pincodes'
    post '/courier/update_logistic_data' => 'couriers#update_logistic_data', as: 'update_logistic_data'

    get '/rack_audits/manual_insert' => 'rack_audits#insert_manual_rack_audits'
    post '/rack_audits/manual_insert' => 'rack_audits#insert_manual_rack_audits', as: 'insert_manual_rack_audits'

    get '/country/:country/get_states' => 'orders#get_states', :as => 'orders_get_states'
    get '/country/get_state_dial_code' => 'orders#get_state_dial_code', :as => 'get_state_dial_code'
    get '/country/:country/get_pincode_format' => 'orders#get_pincode_format', :as => 'orders_get_pincode_format'
    post '/orders/update_landmark' => 'orders#update_landmark'
    post '/orders/update_google_map_query_limit_status' => 'orders#update_google_map_query_limit_status'
    get '/shipment/delayed_job/:id' => 'shipments#delayed_job'
    get '/shipments/view_crc' => 'shipments#view_crc'
    post '/shipments/view_crc' => 'shipments#view_crc'
    get '/shipments/approve_crc' => 'shipments#approve_crc', as: 'approve_crc'
    get '/shipment/recent' => 'shipments#recent', :as => 'shipments_recent'
    get '/shipment/:shipment_id/label' => 'shipments#label',   :as => 'shipments_label'
    get '/shipment/:shipment_id'       => 'shipments#index',   :as => 'shipment'
    get '/shipment/:shipment_id/invoice' => 'shipments#invoice',   :as => 'shipments_invoice'
    get '/shipments/create_for_order' => 'shipments#create_for_order'
    post '/shipments/create_for_order' => 'shipments#create_for_order'
    get '/shipments/add_payment_details' => 'shipments#add_payment_details', :as => 'shipments_add_payment_details'
    post '/shipments/update_payment_details' => 'shipments#update_payment_details', :as => 'shipments_update_payment_details'
    post '/shipments/courier_reconciliation' => 'shipments#courier_reconciliation', as: 'courier_reconciliation'
    post '/shipments/:shipment_id/reverse' => 'shipments#reverse_shipment', as: 'shipment_reverse'
    get '/shipments/ndr_update_panel' => 'shipments#ndr_update_panel'
    post '/shipments/ndr_update_panel' => 'shipments#ndr_update_panel', as: 'ndr_update_panel'

    post '/designers/:designer_id/bulk_upload_design' => 'designs#bulk_upload_design', :as => 'designs_bulk_upload_design'
    get '/api/get_design_weight' => 'api#get_design_weight'
    get '/api/get_design_image' => 'api#get_design_image'
    get '/api/get_design_image_list' => 'api#get_design_image_list'
    get '/api/get_item_and_rack_details' => 'api#get_item_and_rack_details'
    get '/api/get_petticoat_details' => 'api#get_petticoat_details'
    get '/api/check_for_stitching_done_status' => 'api#check_stitching_done_status'
    get '/api/check_for_tailoring_receiving_status' => 'api#check_for_tailoring_receiving_status'
    get '/api/check_inscan_package_status' => 'api#check_inscan_package_status'
    get  '/api/list' => 'api#list', :as => 'api_list'
    get  '/api/cod'  => 'api#cod',  :as => 'api_cod'
    get  '/api/cod_cbd'  => 'api#cod_cbd',  :as => 'api_cod_cbd'
    get  '/api/fetch_pdd_from_cp'  => 'api#fetch_pdd_from_cp',  :as => 'fetch_pdd_from_cp'
    get  '/api/fetch_pdd_from_lane'  => 'api#fetch_pdd_from_lane',  :as => 'fetch_pdd_from_lane'
    get  '/api/cod_design'  => 'api#cod_design',  :as => 'api_cod_design'
    get  '/api/pdd_design'  => 'api#pdd_design',  :as => 'api_pdd_design'
    get  '/api/convert_to_cod' => 'api#convert_to_cod', :as => 'api_convert_to_cod'
    get  '/api/pincode_info'  => 'api#pincode_info',  :as => 'api_pincode_info'
    get  '/api/check_free_shipping' => 'api#check_free_shipping', as: 'api_check_free_shipping'
    get '/api/get_cod_charge' => 'api#get_cod_charge', as: 'api_get_cod_charge'
    get '/api/process_app_rank_file' => 'api#process_app_rank_file', as: 'process_app_rank_file'
    post  '/api/international_min_cart_value' => 'api#international_min_cart_value', as: 'api_international_min_cart_value'
    post '/api/insert_new_clusters' => 'api#insert_new_clusters'
    get '/rack_list' => 'rack_list#index'
    get '/rack_lists/rack_open_close' => 'rack_list#rack_open_close', as: 'rack_open_close'
    post '/rack_lists/rack_open_close' => 'rack_list#rack_open_close'
    get '/rack_list/codes' => 'rack_list#codes'
    post '/rack_list/:id/update' => 'rack_list#update'
    get '/rack_api/store_rack_scans' => 'rack_api#store_rack_scans'

    scope :path => '/communications', :controller => :communication_topics do
        get '/' => :index, as: 'communication_index'
        post '/new_message' => :new_message, as: 'new_message'
        post '/update_status' => :update_topic_status,  as: 'update_topic_status'
        post '/new_topic' => :new_topic, as: 'create_new_topic'
    end

    get "dynamic_prices/" => "dynamic_prices#index"
    post "dynamic_prices/:design_id" => "dynamic_prices#create", :as => "show_dynamic_price"
    get "dynamic_prices/:design_id" => "dynamic_prices#show"
    put "dynamic_prices/:design_id" => "dynamic_prices#update", :as => "update_dynamic_price"
    get "/dynamic_prices/:design_id/activate" => "dynamic_prices#activate", :as => 'dynamic_price_activate'
    get "/dynamic_prices/:design_id/deactivate" => "dynamic_prices#deactivate", :as => 'dynamic_price_deactivate'

    post 'dynamic_prices/' => 'dynamic_prices#upload', :as => 'upload_panel'


    get  '/pickups' => 'pickups#report', :as => 'pickups_report'
    get  '/pickups/defer_current_pickup' => 'pickups#defer_current_pickup', :as => 'pickups_defer_current_pickup'
    resources :subscriptions, only: [:create]
    resources :surveys, only: [:create,:new]
    get '/surveys/sample_form', to: 'surveys#new', sample_form: true
    post '/surveys/update_notes', to: 'surveys#update_notes', as: 'surveys_update_notes'
    get '/surveys/get_line_item_image', to: 'surveys#get_line_item_image', as: 'get_line_item_image'
    post '/surveys/new_review', to: 'surveys#new_review', as: 'new_review'
    post '/surveys/save_review_text', to: 'surveys#save_review_text', as: 'save_review_text'
    post '/surveys/survey_answers', to: 'surveys#survey_answers'
    post '/surveys/get_audience_questions', to: 'surveys#get_audience_specific_questions', as: 'get_audience_specific_questions'
    post '/reviews/remove' => 'reviews#remove'

    post '/subscriptions/check_user' => 'subscriptions#check_user'

    scope path: '/mirraw-admin/dashboard', controller: :dashboards do
        get '/' => :index, as: 'dashboard_index'
    end

    resources :dynamic_size_charts do
        collection do
            get :get_category
        end
    end

    resources :filter_groups

    resources :unbxd_recommendations, only: [] do
      collection do
        get :recommended_for_you
        get :recently_viewed
        get :more_like_these
        get :also_viewed
        get :also_bought
        get :cart_recommend
        get :top_sellers
        get :category_top_sellers
        get :brand_top_sellers
        get :pdp_top_sellers
        get :frequently_bought_together
      end
    end

    get 'unbxd_recommendations_in' => 'unbxd#unbxd_recommendations'

    post 'tickets/create' => 'tickets#create'
    get 'tickets/index' => 'tickets#index'
    post 'tickets/ticket_resolve' => 'tickets#ticket_resolve'
    get 'tickets/ticket_working_on' => 'tickets#ticket_working_on'
    post 'tickets/ticket_reopen'=> 'tickets#ticket_reopen'
    post 'tickets/add_ticket_notes' => 'tickets#add_ticket_notes'
    get 'tickets/seller_ticket' => 'tickets#seller_ticket'
    post 'tickets/reassign' => 'tickets#reassign'

    post 'tailoring_info/measurement_label' => 'tailoring_info#measurement_label'
    get  'tailoring_info/measurement_label' => 'tailoring_info#measurement_label', as: 'download_measurement_label'
    get 'tailoring_info/send_for_tailoring' => 'tailoring_info#send_for_tailoring'
    post 'tailoring_info/send_for_tailoring' => 'tailoring_info#send_for_tailoring', as:'send_for_tailoring'
    get 'tailoring_info/received_from_tailoring' => 'tailoring_info#received_from_tailoring'
    post 'tailoring_info/received_from_tailoring' => 'tailoring_info#received_from_tailoring', as:'received_from_tailoring'
    get 'tailoring_info/picked_up_by_tailor' => 'tailoring_info#picked_up_by_tailor'
    post 'tailoring_info/picked_up_by_tailor' => 'tailoring_info#picked_up_by_tailor', as:'picked_up_by_tailor'
    get 'tailoring_info/get_material_names_for_tailoring_info' => 'tailoring_info#get_material_names_for_tailoring_info', as:'get_material_names_for_tailoring_info'
    get 'tailoring_info/index' => 'tailoring_info#index', as:'tailoring_info'
    get 'tailoring_info/received_tailoring_checking' => 'tailoring_info#received_tailoring_checking', as: 'tailor_received_tailoring_checking'
    post 'tailoring_info/tailoring_material_received' => 'tailoring_info#tailoring_material_received', as:'tailoring_material_received'
    get "tailoring_info/get_reassigned_products/:name" => "tailoring_info#get_reassigned_products"
    post 'tailoring_info/add_tailoring_measurement' => 'tailoring_info#add_tailoring_measurement', as:'add_tailoring_measurement'
    get 'tailoring_info/inscan_receive_pending' => 'tailoring_info#inscan_receive_pending', as: 'inscan_receive_pending'
    get 'tailoring_info/fnp_bulk_assign' => 'tailoring_info#fnp_bulk_assign'
    post 'tailoring_info/fnp_bulk_assign' => 'tailoring_info#fnp_bulk_assign', as: 'fnp_bulk_assign'    
    post 'tailoring_info/save_alteration_note' => 'tailoring_info#save_alteration_note', as: 'save_alteration_note'
    get 'tailoring_info/receiving_panel' => 'tailoring_info#tailor_receiving_panel'
    post 'tailoring_info/receiving_panel' => 'tailoring_info#tailor_receiving_panel', as: 'receiving_panel'
    get 'tailoring_info/get_tailoring_issues' => 'tailoring_info#get_tailoring_issues', as: 'get_tailoring_issues'
    post 'tailoring_info/submit_tailoring_issue_response' => 'tailoring_info#submit_tailoring_issue_response', as: 'submit_tailoring_issue_response'
    get 'line_item/get_addon_type_value_form/:item_data' => 'line_items#get_addon_type_value_form', as: 'get_addon_type_value_form'
    post 'line_item/create_stitching_addon' => 'line_items#create_stitching_addon', as: 'create_stitching_addon'
    post 'line_item/mark_no_intervention' => 'line_items#mark_no_intervention', as: 'mark_no_intervention'    
    post 'line_item/remove_warehouse_tag' => 'line_items#remove_warehouse_tag'
    post 'line_item/add_warehouse_tag' => 'line_items#add_warehouse_tag'
    post 'tailoring_info/confirm_replacement_material'=> 'tailoring_info#confirm_replacement_material', as: 'confirm_replacement_material' 
    resources :horoscopes
    resources :header_infos
    #get 'horoscopes', :to => 'horoscopes#index', :as => "horoscopes"
    get 'horoscopes/add_new_horoscope', :to => 'horoscopes#new', :as => "horoscope_new"
    get 'horoscopes/daily/:id', :to => 'horoscopes#show', :as => "inside_horoscope"
    put 'horoscopes/daily/:id', :to => 'horoscopes#update', :as => "update_horoscope"
    get 'horoscopes/daily/:id', :to => 'horoscopes#edit', :as => "edit"

    get "/showcase/online-sales" => redirect("/online-sales")
    get "/showcase/eid" => redirect("/eid")
    get "/eid" => "dynamic_landing_pages#show", id: "eid"
    get "/online-sales" => "dynamic_landing_pages#show", id: "online-sales"

    get "/showcase/navratri" => redirect("/navratri-sale-offers")
    get "/navratri-sale-offers" => "dynamic_landing_pages#show", id: "navratri"

    get "/showcase/diwali-sale" => redirect("/diwali-sale")
    get "/diwali-sale" => "dynamic_landing_pages#show", id: "diwali-sale"

    resources :dynamic_landing_pages, path: 'showcase'

    post '/upload_landing_image', to: 'dynamic_landing_pages#upload_landing_image'
    get '/landing_images', to: 'dynamic_landing_pages#landing_image_index'
    post '/get_landing_template', to: 'dynamic_landing_pages#get_landing_template'

    resources :fashion_updates, :except => [:show], path: 'fashion-updates'
    get 'fashion-updates/:category', to: 'fashion_updates#category_page', as: 'category_fashion_updates'
    get 'fashion-updates/:category/:id', to: 'fashion_updates#show', as: 'blog_fashion_updates'

    scope path: '/sitemap', controller: :sitemaps, format: false do
      get '/' => :index, :as => :sitemaps_index
      get ':title' => :show_category, :as => :sitemaps_category
    end

    get '/design_videos_update', to: 'design_videos#index'
    post '/design_videos_update', to: 'design_videos#update'
    get 'push_engage_subscribers/create', as: :push_engage_subscribers

    if  CKEDITOR_ENABLED
        mount Ckeditor::Engine => '/ckeditor'
    end

    mount Sidekiq::Web => '/sidekiq'

    Sidekiq::Web.use Rack::Auth::Basic do |username, password|
       username == ENV['SIDEKIQ_USERNAME'] && password == ENV['SIDEKIQ_PASSWORD']
    end unless Rails.env.development?

    #authenticated :account, -> account { account.admin? }  do
    #  mount DelayedJobWeb, at: "/delayed_job"
    #end
    # get '/unbxd/add_product/:id' => 'unbxd#add_product'
    # get '/unbxd/mirraw_com_add_design_to_unbxd/:id' => 'unbxd#set_unbxd_flag'
    # Reference http://work.stevegrossi.com/2013/04/06/dynamic-error-pages-with-rails-3-2/
    get '/bx' => 'pages#bx'
    get '/404' => 'errors#error_404'
    get '/block' => 'errors#block_page'

    post 'gift_card_orders/paypal_response' => 'gift_card_orders#paypal_response'
    resources :gift_card_orders, only: [:new, :create, :show], param: :number

    get 'design_widget/basic_widget' => 'design_widget#basic_widget'
    get 'design_widget/catalog_widget' => 'design_widget#catalog_widget'
    get 'tableau_report' => 'tableau_reports#tableau_page'
    get 'tableau_report/:id' => 'tableau_reports#tableau_page'
    namespace :one_time_passwords, path: 'otp' do
        post 'deliver'
        post 'verify'
    end

    scope '/cashback/:order_number' do
        get 'trustpilot', to: 'cashbacks#trustpilot', as: 'cashback_trustpilot'
    end

    namespace :api, defaults: { format: 'json' } do
        get 'item_searches/initiate' => 'item_searches#initiate'
        get 'item_searches/locate' => 'item_searches#locate'
        delete 'item_searches/abandon' => 'item_searches#abandon'
        get 'item_searches/renew' => 'item_searches#renew'
    end
  
    namespace 'mirraw_admin' do
      resources :design ,:only=>[]  do
        collection do
          get 'collection_search' 
          post 'collection_search'
          post 'design_search'
          post 'remove_designs_from_collection'
          get 'upload_design_discount'
          post 'update_design_discount'
          post 'update_variant_price'
          post 'upload_rpv_csv'
          get 'mark_design_premium'
          get 'add_custom_tags_to_design'
          post 'add_custom_tags_to_design_create'
        end
      end
      resources :designers ,:only=> [] do
        collection do
          get 'upload_designer_discount'
          post 'update_designer_discount'
          get 'dynamic_qc_panel'
          post 'update_dynamic_qc_rate'
        end
      end
      resources :gradings,:only=>[] do
        collection do
          get 'manual_grading'
          post 'upload_manual_grading'
        end
      end
      resources :design_gradings,:only=>[:new,:index,:create,:show] do
        member do
          post 'upload_grading_tag'
          get 'export_csv'
        end
        collection do
          get 'fetch_grading_taggable_ids_and_names'
        end
      end
      resources :orders ,:only=>[]  do
        collection do 
          get 'cod_orders_state_change'
          post 'cod_orders_state_update'
        end
      end
      resources :accounts do
        collection do
          get 'negative_commsision_report'
          post 'generate_negative_commission'
        end
      end

      resources :designer_orders do
        collection do
          get 'payload'
          post 'fetch_payload'
        end
      end
      resources :reports do
        collection do
          get 'best_sellers'
        end
      end

      resources :seller_campaigns do
        collection do
          get 'campaign_portal'
        end
      end

    end
    post 'wms_callback/after_qc' => 'wms_callback#after_qc'
    post 'wms_callback/generate_manifest_for_tailoring' => 'wms_callback#generate_manifest_for_tailoring'

    scope path: '/video_listings', controller: :video_listings do
      get 'index' => :index
      get 'show'  => :show
      get  'create_video' => :create
      post 'submit_video' => :submit_video
      get 'update_video' => :update_video
      put 'update' => :update
      delete 'delete/:id', to: 'video_listings#destroy', as: :destroy
    end
    
end

#== Route Map
# Generated on 28 Feb 2012 11:24
#
#                store_catalog GET    /store/catalog(.:format)                                   store#catalog
#                 store_search GET    /store/:kind(.:format)                                     store#search
#             designer_designs GET    /designers/:designer_id/designs(.:format)                  designs#index
#                              POST   /designers/:designer_id/designs(.:format)                  designs#create
#          new_designer_design GET    /designers/:designer_id/designs/new(.:format)              designs#new
#         edit_designer_design GET    /designers/:designer_id/designs/:id/edit(.:format)         designs#edit
#              designer_design GET    /designers/:designer_id/designs/:id(.:format)              designs#show
#                              PUT    /designers/:designer_id/designs/:id(.:format)              designs#update
#     designer_designer_orders GET    /designers/:designer_id/designer_orders(.:format)          designer_orders#index
#                              POST   /designers/:designer_id/designer_orders(.:format)          designer_orders#create
#  new_designer_designer_order GET    /designers/:designer_id/designer_orders/new(.:format)      designer_orders#new
# edit_designer_designer_order GET    /designers/:designer_id/designer_orders/:id/edit(.:format) designer_orders#edit
#      designer_designer_order GET    /designers/:designer_id/designer_orders/:id(.:format)      designer_orders#show
#                              PUT    /designers/:designer_id/designer_orders/:id(.:format)      designer_orders#update
#                              DELETE /designers/:designer_id/designer_orders/:id(.:format)      designer_orders#destroy
#                    designers GET    /designers(.:format)                                       designers#index
#                              POST   /designers(.:format)                                       designers#create
#                 new_designer GET    /designers/new(.:format)                                   designers#new
#                edit_designer GET    /designers/:id/edit(.:format)                              designers#edit
#                     designer GET    /designers/:id(.:format)                                   designers#show
#                              PUT    /designers/:id(.:format)                                   designers#update
#                              DELETE /designers/:id(.:format)                                   designers#destroy
#     line_items_save_quantity POST   /line_items/save_quantity(.:format)                        line_items#save_quantity
#         line_items_save_note POST   /line_items/save_note(.:format)                            line_items#save_note
#             carts_save_email POST   /carts/save_email(.:format)                                carts#save_email
#                   search_tag GET    /tags(.:format)                                            store#tag
#                       orders GET    /orders(.:format)                                          orders#index
#                              POST   /orders(.:format)                                          orders#create
#                    new_order GET    /orders/new(.:format)                                      orders#new
#                   edit_order GET    /orders/:id/edit(.:format)                                 orders#edit
#                        order GET    /orders/:id(.:format)                                      orders#show
#                              PUT    /orders/:id(.:format)                                      orders#update
#                              DELETE /orders/:id(.:format)                                      orders#destroy
#                                     /order/:action/:id(.:format)                               orders#:action
#       delete_line_item_order POST   /orders/delete_line_item(.:format)                         orders#delete_line_item
#       update_line_item_order POST   /orders/update_line_item(.:format)                         orders#update_line_item
#                order_reports GET    /order_reports(.:format)                                   order_reports#index
#                         page GET    /pages/:action(.:format)                                   pages#:action
#          order_event_trigger POST   /event_trigger/trigger_event_for_order(.:format)           event_trigger#trigger_event_for_order
# designer_order_event_trigger POST   /event_trigger/trigger_event_for_designer_order(.:format)  event_trigger#trigger_event_for_designer_order
#      designer_order_tracking POST   /designer_orders/add_tracking_num(.:format)                designer_orders#add_tracking_num
#                     contacts GET    /contacts(.:format)                                        contacts#index
#                              POST   /contacts(.:format)                                        contacts#create
#                  new_contact GET    /contacts/new(.:format)                                    contacts#new
#                 edit_contact GET    /contacts/:id/edit(.:format)                               contacts#edit
#                      contact GET    /contacts/:id(.:format)                                    contacts#show
#                              PUT    /contacts/:id(.:format)                                    contacts#update
#                              DELETE /contacts/:id(.:format)                                    contacts#destroy
#                   line_items POST   /line_items(.:format)                                      line_items#create
#                    line_item DELETE /line_items/:id(.:format)                                  line_items#destroy
#          new_account_session GET    /accounts/sign_in(.:format)                                devise/sessions#new
#              account_session POST   /accounts/sign_in(.:format)                                devise/sessions#create
#      destroy_account_session DELETE /accounts/sign_out(.:format)                               devise/sessions#destroy
#             account_password POST   /accounts/password(.:format)                               devise/passwords#create
#         new_account_password GET    /accounts/password/new(.:format)                           devise/passwords#new
#        edit_account_password GET    /accounts/password/edit(.:format)                          devise/passwords#edit
#                              PUT    /accounts/password(.:format)                               devise/passwords#update
#  cancel_account_registration GET    /accounts/cancel(.:format)                                 accounts/registrations#cancel
#         account_registration POST   /accounts(.:format)                                        accounts/registrations#create
#     new_account_registration GET    /accounts/sign_up(.:format)                                accounts/registrations#new
#    edit_account_registration GET    /accounts/edit(.:format)                                   accounts/registrations#edit
#                              PUT    /accounts(.:format)                                        accounts/registrations#update
#                              DELETE /accounts(.:format)                                        accounts/registrations#destroy
#                  rails_admin        /admin                                                     RailsAdmin::Engine
#
# Routes for RailsAdmin::Engine:
#  history_model          /history/:model_name(.:format)      rails_admin/history#for_model
# history_object          /history/:model_name/:id(.:format)  rails_admin/history#for_object
#      dashboard          /                                   rails_admin/main#dashboard
#          index GET|POST /:model_name(.:format)              rails_admin/main#index
#         export          /:model_name/export(.:format)       rails_admin/main#export
#            new GET      /:model_name/new(.:format)          rails_admin/main#new
#         create POST     /:model_name/new(.:format)          rails_admin/main#create
#    bulk_action POST     /:model_name/bulk_action(.:format)  rails_admin/main#bulk_action
#   bulk_destroy POST     /:model_name/bulk_destroy(.:format) rails_admin/main#bulk_destroy
#           show GET      /:model_name/:id(.:format)          rails_admin/main#show
#           edit GET      /:model_name/:id/edit(.:format)     rails_admin/main#edit
#         update PUT      /:model_name/:id/edit(.:format)     rails_admin/main#update
#         delete GET      /:model_name/:id/delete(.:format)   rails_admin/main#delete
#        destroy DELETE   /:model_name/:id/delete(.:format)   rails_admin/main#destroy
