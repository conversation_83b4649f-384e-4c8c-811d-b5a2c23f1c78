require File.expand_path('../boot', __FILE__)

require 'rails/all'
# require 'sass-rails'
require 'csv'
autoload(:CarrierWave,'carrierwave')
require 'rails_admin' if ENV['DISABLE_RAILS_ADMIN'] != 'true'
CKEDITOR_ENABLED = ENV['DISABLE_CKEDITOR_ADMIN'] != 'true'
require 'ckeditor' if CKEDITOR_ENABLED
# If you have a Gemfile, require the gems listed there, including any gems
# you've limited to :test, :development, or :production.
#Bundler.require(:default, Rails.env) if defined?(Bundler)
Bundler.require(*Rails.groups)
require './lib/normalized_request_middleware'

module Mirraw
  class Application < Rails::Application
    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration should go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded.
    
    # Called load without safe option msg fix
    # SafeYAML::OPTIONS[:suppress_warnings] = true

    # # Custom directories with classes and modules you want to be autoloadable.
    # config.autoload_paths += %W(#{config.root}/lib)
    # config.autoload_paths += Dir["#{config.root}/lib/**/"]
    Koala.config.api_version = "v2.6"
    # Only load the plugins named here, in the order given (default is alphabetical).
    # :all can be used as a placeholder for all plugins not explicitly named.
    # config.plugins = [ :exception_notification, :ssl_requirement, :all ]
    config.active_record.raise_in_transactional_callbacks = true

    # Activate observers that should always be running.
    # config.active_record.observers = :cacher, :garbage_collector, :forum_observer

    # Set Time.zone default to the specified zone and make Active Record auto-convert to this zone.
    # Run "rake -D time" for a list of tasks for finding time zone names. Default is UTC.
    config.time_zone = 'Mumbai'

    # The default locale is :en and all translations from config/locales/*.rb,yml are auto loaded.
    # config.i18n.load_path += Dir[Rails.root.join('my', 'locales', '*.{rb,yml}').to_s]
    # config.i18n.default_locale = :de

    # Configure the default encoding used in templates for Ruby 1.9.
    config.encoding = "utf-8"
    # config.middleware.use Rack::Attack
    config.middleware.use "NormalizedRequestMiddleware"
    
    # Be sure to have the adapter's gem in your Gemfile
    # and follow the adapter's specific installation
    # and deployment instructions.
    config.active_job.queue_adapter = :sidekiq

    # Configure sensitive parameters which will be filtered from the log file.
    config.filter_parameters += [:password]

    # Enable the asset pipeline
    config.assets.enabled = true
    #config.assets.image_optim = {:svgo => false}
    config.assets.image_optim = false

    config.dhl_shipper_constant = {
      "shipperName" => "MR. ANUP NAIR",
      "shipperCompName" => "MIRRAW ONLINE SERVICES PVT. LTD.",
      "shipperAddress1" => "PLOT NO. 7, GROUND FLOOR," ,
      "shipperAddress2" => "NASHIRWAN MANSION, KUMTA STREET," ,
      "shipperAddress3" => "OFF. SHAHID BHAGAT SINGH ROAD, FORT" ,
      "shipperCity" => "Mumbai",
    }
    config.click_post = {
      username: 'mirraw',
      key: '545ddb39-d9d5-4cc6-926f-bf35b6e50f66',
      create_shipment_path: 'https://www.clickpost.in/api/v3/create-order/',
      recommendation_path: 'https://www.clickpost.in/api/v1/recommendation_api/',
      ndr_update_path: 'https://www.clickpost.in/api/v1/ndr/update/',
      tracking_path: 'https://www.clickpost.in/api/v2/tracking/awb-register/',
      pdd_check_path: 'https://www.clickpost.in/api/v2/predicted_sla_api/',
      cancel_order_path: 'https://www.clickpost.in/api/v1/cancel-order',
      bulk_serviceable: 'https://www.clickpost.in/api/v1/bulk_serviceability_api/'
    }

    config.paperclip_defaults ={
      processors: [:thumbnail, :paperclip_optimizer],
      storage: :s3,
      s3_credentials:{
        access_key_id: ENV['AWS_ACCESS_KEY_ID'],
        secret_access_key: ENV['AWS_SECRET_ACCESS_KEY'],
        bucket: ENV['S3_BUCKET']
      },
      path: ":class/:id/:basename_:style.:extension",
      bucket: ENV['S3_BUCKET'],
      default_url: "/processing.jpg",
      url: ":s3_alias_url",
      s3_headers: { 'Cache-Control' => 'max-age=315576000', 'Expires' => 10.years.from_now.httpdate },
      s3_protocol: (ENV['HTTP2_ENABLE'] == 'true' ? :https : ''),
      s3_host_alias:  if ENV.fetch('AKAMAI') == '1'  && ENV.fetch('PREFIX_AKAMAI_ASSETS_URL')
                            if Rails.env.staging?
                              ENV['CLOUDFRONT_URL']
                            else
                              Proc.new {|a| ENV['PREFIX_AKAMAI_ASSETS_URL']+"0.mirraw.com" }
                            end
                        else
                            if Rails.env.production?
                              Proc.new {|a| ENV['PREFIX_MIRRAW_ASSETS_URL']+"0.mirraw.com" }
                            else
                              ENV['CLOUDFRONT_URL']
                            end
                        end
    }

    config.pay_with_amazon = {
        widget_url: "https://static-eu.payments-amazon.com/cba/js/in/sandbox/PaymentWidgets.js",
        merchant_id: "A1DEYASDZHXKA3",
        aws_access_key: "AKIAJEQ2FEGXLDNBFVEQ",
        secret_key: ENV['PAY_WITH_AMAZON_SECRET_KEY'],
        return_url: "http://staging-mirraw.herokuapp.com/order/amazon_success",
        cancel_url: "http://staging-mirraw.herokuapp.com/orders/new"
    }


    config.payu = {:base_url => ENV['PAYU_URL'], :merchant_key => ENV['PAYU_MERCHANT_KEY'], :salt => ENV['PAYU_SALT'], :billing_page_non_seamless_request_url => ENV['PAYU_URL'] + '_payment?type=merchant_txn', :billing_page_non_seamless_transaction_url => ENV['PAYU_URL'] + '_payment_options',
        :payu_money_test_url => 'https://test.payu.in/_payment',
        :payu_money_production_url => 'https://secure.payu.in/_payment', payu_web_service_production_url: 'https://info.payu.in/merchant/postservice.php?form=2', payu_web_service_test_url: 'https://test.payu.in/merchant/postservice.php?form=2'
    }

    config.bluedart = {:tracking_lickey => ENV['BLUEDART_TRACKING_LICKEY'], :loginid => ENV['BLUEDART_LOGINID'], :tracking_url => "http://www.bluedart.com/servlet/RoutingServlet?handler=tnt&action=custawbquery&loginid={loginid}&awb=awb&numbers={awb_number}&lickey={license_key}&verno={version}&scan={scans}"}
    config.dtdc = {:tracking_url => 'http://dtdc.com/tracking/tracking_results.asp'}
    config.mashape = {:speed_post_api => 'https://indianpost.p.mashape.com/index.php', :key => ENV['MASHAPE_KEY']}

    config.gati_tracking_url = "http://www.gati.com/webservices/ECOMDKTTRACK.jsp?p1="
    config.gati_tracking_code = "CE5DE96AED669BBB"

    # config.ship_delight_api_url = "http://crm.shipdelight.com/index.php/alltracking/create_shipment/doUpload"
    config.ship_delight_api_url = "http://erp.shipdelight.com/index.php/alltracking/create_shipment/doUpload"
    config.ship_delight_api_key = "MR123"
    config.ship_delight_vendor_name = "MIRRAW ONLINE SERVICES PVT. LTD"
    config.ship_delight_tracking_url = "http://crm.shipdelight.com/index.php/api/detailed_status/trackAwb?awb="

    config.google_analytics = ENV['GA_TRACKING_ID']
    config.gtm_desktop_id = ENV['GOOGLE_TAG_MANAGER_DESKTOP_ID']
    # BranchIO key
    config.branch_key = ENV.fetch('BRANCH_KEY')
    config.paytm = YAML.load(ERB.new(File.read("#{Rails.root}/config/paytm.yml")).result)[Rails.env].symbolize_keys!
    config.g2a = YAML.load(ERB.new(File.read("#{Rails.root}/config/g2a.yml")).result)[Rails.env].symbolize_keys!
    config.qc_image_key = Base64.strict_decode64(ENV.fetch("QC_IMAGE_KEY"))
    config.aws = {:region => ENV['AWS_REGION'], :bucket => ENV['AWS_BUCKET'], :access_key_id => ENV['AWS_ACCESS_KEY_ID'], :secret_access_key => ENV['AWS_SECRET_ACCESS_KEY']}
    config.exceptions_app = self.routes
    config.razor_key_id = ENV['RAZOR_KEY_ID']
    config.dhl_ecom = YAML.load(ERB.new(File.read("#{Rails.root}/config/dhl_ecom.yml")).result)[Rails.env].symbolize_keys!
    # config.atlantic = {client_id: 'MIR01', password: 'MIR01'}
    config.rapid_delivery = YAML.load(ERB.new(File.read("#{Rails.root}/config/rapid_delivery.yml")).result)[Rails.env].symbolize_keys!
    config.xpress_bees = YAML.load(ERB.new(File.read("#{Rails.root}/config/xpress_bees.yml")).result)[Rails.env].symbolize_keys!
    config.paypal_smart_pay = YAML.load(ERB.new(File.read("#{Rails.root}/config/paypal_smart_checkout.yml")).result)[Rails.env].symbolize_keys!
    config.aramex = YAML.load(ERB.new(File.read("#{Rails.root}/config/aramex.yml")).result)[Rails.env].symbolize_keys!
    config.unbxd = {UNBXD_API_KEY: ENV.fetch('UNBXD_API_KEY'), UNBXD_SITE_KEY: ENV.fetch('UNBXD_SITE_KEY'), UNBXD_SERVICE_URL: ENV.fetch('UNBXD_SERVICE_URL'), UNBXD_SECRET_KEY: ENV.fetch('UNBXD_SECRET_KEY')}
    config.url_secret_key = 'CICADA7781015671'
    config.to_prepare do
      RailsAdmin::MainController.skip_before_filter :set_cart
    end if defined? RailsAdmin
    config.mirraw_api_headers = {"Device-ID" => ENV.fetch('MIRRAW_API_DEVICE_ID'), "app_source" => "web", "Token" => ENV.fetch('MIRRAW_API_TOKEN')}

    config.gokwik = {
      appid: "4ea9db3631364a959c8c5fc694a25b04",
      appsecret: "f82692d526764374a95e98504f64664f",
      create_order_url: "https://api.gokwik.co/v1/order/create",
      update_order_url: "https://api.gokwik.co/v1/order/update"
    }
    
    config.clevertap_evt_app = {
      "android":{
        "X-CleverTap-Account-Id" => ENV["CLEVERTAP_ACCOUNT_ID_ANDROID"],
        "X-CleverTap-Passcode" => ENV["CLEVERTAP_ACCOUNT_PASSCODE_ANDROID"] 
      },
      "ios":{
        "X-CleverTap-Account-Id" => ENV["CLEVERTAP_ACCOUNT_ID_IOS"],
        "X-CleverTap-Passcode" => ENV["CLEVERTAP_ACCOUNT_Passcode_IOS"]
      },
      "web":{
        "X-CleverTap-Account-Id" => ENV["CLEVERTAP_ACCOUNT_ID_WEB"],
        "X-CleverTap-Passcode" => ENV["CLEVERTAP_ACCOUNT_Passcode_WEB"]
      }
    }
    config.autoload_paths << Rails.root.join('lib')
  end
end
