---
:Saree:
- :extra:
    :url: https://www.dropbox.com/scl/fi/601e5ql31f74yi6s437bn/test_saree_pre_stitch_sizes.xlsx?rlkey=j63er3u22goshv52y6w1yl90r&st=h9xkrvtc&dl=1
    :designable: "Saree"
    :product_type: "saree"
    :option_type: "sarees_size"
- :name: :gst_rate
  :col: 42
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 43
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :embellish
  :col: 38
  :p_name: embellish
- :name: :description
  :col: 40
  :operation:
  - :strip
  :p_name: description
- :name: :package_details
  :col: 3
  :present: true
  :p_name: package details
- :name: :price
  :col: 29
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
- :name: :discount_percent
  :col: 30
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 4
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 27
  :operation:
  - :strip
  :p_name: tag_list
- :name: :pattern
  :col: 37
  :p_name: pattern
- :name: :video_link
  :col: 26
  :p_name: video_link
- :name: :is_stitched
  :col: 10
  :set_variable:
    :name: is_stitched
    :value: "->(val){return val.downcase == 'pre_stitched_saree'}"
- :name: :region
  :col: 36
  :p_name: region
- :name: :quantity
  :col: 28
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
- :name: :designer_collection_id
  :col: 41
  :mapping: :collection
  :p_name:  collection
- :name: :fabric_of_saree
  :col: 5
  :present: true
- :name: :work
  :col: 9
  :present: true
  :p_name: work
- :name: :blouse_avail
  :col: 11
  :present: true
  :operation:
  - :if_transform
  - 'yes'
  - With Blouse
- :name: :saree_color
  :col: 6
  :present: true
  :p_name: saree_color
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :saree_color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Saree"
- :delay_images:
  - - :name: :url
      :col: 21
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 22
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 23
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 24
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 25
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
- :delay_designables:
  - - :name: :width
      :col: 8
      :p_name: width_of_saree_in_inches
    - :name: :length
      :col: 7
      :present: true
      :p_name: length_of_saree_in_metres
    - :name: :saree_color
      :col: 6
      :present: true
    - :name: :blouse_available
      :col: 11
      :present: true
      :p_name: blouse_availability
    - :name: :blouse_image
      :col: 15
      :p_name: blouse_as_shown_in_the_image
    - :name: :blouse_size
      :col: 14
      :p_name: size_of_blouse_in_cms
    - :name: :blouse_fabric
      :col: 12
      :p_name: fabric_of_blouse
    - :name: :blouse_color
      :col: 13
      :p_name: blouse_color
    - :name: :blouse_work
      :col: 16
      :p_name: blouse_work
    - :name: :petticoat_available
      :present: true
      :col: 17
      :p_name: petticoat_availability
    - :name: :petticoat_size
      :col: 18
      :p_name: size_of_petticoat_metres
    - :name: :petticoat_color
      :col: 19
    - :name: :petticoat_fabric
      :col: 20
      :p_name: fabric_of_petticoat
    - :name: :kind
      :value:
      - Saree
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :Sarees
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :fabric_of_saree
      :col: 5
      :p_name: fabric_of_saree
      :present: true
  - - :name: :property_value_id
      :mapping: :work
      :col: 9
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :type
      :col: 10
      :p_name: type
      :present: true
  - - :name: :property_value_id
      :mapping: :saree_color
      :col: 6
      :p_name: saree_color
      :present: true
  - - :name: :property_value_id
      :mapping: :fabric_of_blouse
      :col: 12
      :p_name: fabric_of_blouse
  - - :name: :property_value_id
      :mapping: :blouse_color
      :col: 13
  - - :name: :property_value_id
      :mapping: :blouse_work
      :col: 16
      :p_name: blouse_work
  - - :name: :property_value_id
      :mapping: :petticoat_color
      :col: 19
      :p_name: color_of_petticoat
  - - :name: :property_value_id
      :mapping: :fabric_of_petticoat
      :col: 20
  - - :name: :property_value_id
      :mapping: :celebrity
      :col: 39
      :p_name: celebrity
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 31
      :p_name: occasion
  - - :name: :property_value_id
      :mapping: :look
      :col: 32
      :p_name: look
  - - :name: :property_value_id
      :mapping: :saree_border
      :col: 33
      :p_name: saree_border
  - - :name: :property_value_id
      :mapping: :pallu_style
      :col: 34
      :p_name: pallu_style
  - - :name: :property_value_id
      :mapping: stitching
      :col: 35
      :p_name: stitching
- :variants:
  - - :name: :design_code
      :col: 44
      :p_name: sarees_size_XS_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 45
      :operation:
      - :to_i
      :p_name: sarees_size_XS_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 46
      :operation:
      - :to_i
      :p_name: sarees_size_XS_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Extra Small'
      :mapping: :sarees_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 47
      :p_name: sarees_size_S_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 48
      :operation:
      - :to_i
      :p_name: sarees_size_S_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 49
      :operation:
      - :to_i
      :p_name: sarees_size_S_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Small'
      :mapping: :sarees_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 50
      :p_name: sarees_size_M_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 51
      :operation:
      - :to_i
      :p_name: sarees_size_M_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 52
      :operation:
      - :to_i
      :p_name: sarees_size_M_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Medium'
      :mapping: :sarees_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 53
      :p_name: sarees_size_L_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 54
      :operation:
      - :to_i
      :p_name: sarees_size_L_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 55
      :operation:
      - :to_i
      :p_name: sarees_size_L_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Large'
      :mapping: :sarees_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 56
      :p_name: sarees_size_XL_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 57
      :operation:
      - :to_i
      :p_name: sarees_size_XL_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 58
      :operation:
      - :to_i
      :p_name: sarees_size_XL_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Extra Large'
      :mapping: :sarees_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 59
      :p_name: sarees_size_XXL_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 60
      :operation:
      - :to_i
      :p_name: sarees_size_XXL_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 61
      :operation:
      - :to_i
      :p_name: sarees_size_XXL_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Extra Extra Large'
      :mapping: :sarees_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 62
      :p_name: sarees_size_3XL_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 63
      :operation:
      - :to_i
      :p_name: sarees_size_3XL_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 64
      :operation:
      - :to_i
      :p_name: sarees_size_3XL_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3XL'
      :mapping: :sarees_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 65
      :p_name: sarees_size_4XL_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 66
      :operation:
      - :to_i
      :p_name: sarees_size_4XL_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 67
      :operation:
      - :to_i
      :p_name: sarees_size_4XL_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4XL'
      :mapping: :sarees_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 68
      :p_name: sarees_size_5XL_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 69
      :operation:
      - :to_i
      :p_name: sarees_size_5XL_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 70
      :operation:
      - :to_i
      :p_name: sarees_size_5XL_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5XL'
      :mapping: :sarees_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 71
      :p_name: sarees_size_6XL_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 72
      :operation:
      - :to_i
      :p_name: sarees_size_6XL_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 73
      :operation:
      - :to_i
      :p_name: sarees_size_6XL_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6XL'
      :mapping: :sarees_size
      :value_if: :is_stitched
- :combo_variants:
  - - :name: :design_code
      :col: 74
      :p_name: sarees_size_combo_4xs_design_code
      :format: mirraw
    - :name: :quantity
      :col: 75
      :operation:
      - :to_i
      :p_name: sarees_size_combo_4xs_quantity
      :format: mirraw
    - :name: :price
      :col: 76
      :operation:
      - :to_i
      :p_name: sarees_size_combo_4xs_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 4XS'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 77
      :p_name: sarees_size_combo_3xs_design_code
      :format: mirraw
    - :name: :quantity
      :col: 78
      :operation:
      - :to_i
      :p_name: sarees_size_combo_3xs_quantity
      :format: mirraw
    - :name: :price
      :col: 79
      :operation:
      - :to_i
      :p_name: sarees_size_combo_3xs_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 3XS'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 80
      :p_name: sarees_size_combo_xxs_design_code
      :format: mirraw
    - :name: :quantity
      :col: 81
      :operation:
      - :to_i
      :p_name: sarees_size_combo_xxs_quantity
      :format: mirraw
    - :name: :price
      :col: 82
      :operation:
      - :to_i
      :p_name: sarees_size_combo_xxs_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo XXS'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 83
      :p_name: sarees_size_combo_xs_design_code
      :format: mirraw
    - :name: :quantity
      :col: 84
      :operation:
      - :to_i
      :p_name: sarees_size_combo_xs_quantity
      :format: mirraw
    - :name: :price
      :col: 85
      :operation:
      - :to_i
      :p_name: sarees_size_combo_xs_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo XS'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 86
      :p_name: sarees_size_combo_small_design_code
      :format: mirraw
    - :name: :quantity
      :col: 87
      :operation:
      - :to_i
      :p_name: sarees_size_combo_small_quantity
      :format: mirraw
    - :name: :price
      :col: 88
      :operation:
      - :to_i
      :p_name: sarees_size_combo_small_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ComboSmall'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 89
      :p_name: sarees_size_combo_medium_design_code
      :format: mirraw
    - :name: :quantity
      :col: 90
      :operation:
      - :to_i
      :p_name: sarees_size_combo_medium_quantity
      :format: mirraw
    - :name: :price
      :col: 91
      :operation:
      - :to_i
      :p_name: sarees_size_combo_medium_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ComboMedium'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 92
      :p_name: sarees_size_combo_large_design_code
      :format: mirraw
    - :name: :quantity
      :col: 93
      :operation:
      - :to_i
      :p_name: sarees_size_combo_large_quantity
      :format: mirraw
    - :name: :price
      :col: 94
      :operation:
      - :to_i
      :p_name: sarees_size_combo_large_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ComboLarge'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 95
      :p_name: sarees_size_combo_xl_design_code
      :format: mirraw
    - :name: :quantity
      :col: 96
      :operation:
      - :to_i
      :p_name: sarees_size_combo_xl_quantity
      :format: mirraw
    - :name: :price
      :col: 97
      :operation:
      - :to_i
      :p_name: sarees_size_combo_xl_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo XL'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 98
      :p_name: sarees_size_combo_xxl_design_code
      :format: mirraw
    - :name: :quantity
      :col: 99
      :operation:
      - :to_i
      :p_name: sarees_size_combo_xxl_quantity
      :format: mirraw
    - :name: :price
      :col: 100
      :operation:
      - :to_i
      :p_name: sarees_size_combo_xxl_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo XXL'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 101
      :p_name: sarees_size_combo_3xl_design_code
      :format: mirraw
    - :name: :quantity
      :col: 102
      :operation:
      - :to_i
      :p_name: sarees_size_combo_3xl_quantity
      :format: mirraw
    - :name: :price
      :col: 103
      :operation:
      - :to_i
      :p_name: sarees_size_combo_3xl_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 3XL'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 104
      :p_name: sarees_size_combo_4xl_design_code
      :format: mirraw
    - :name: :quantity
      :col: 105
      :operation:
      - :to_i
      :p_name: sarees_size_combo_4xl_quantity
      :format: mirraw
    - :name: :price
      :col: 106
      :operation:
      - :to_i
      :p_name: sarees_size_combo_4xl_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 4XL'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 107
      :p_name: sarees_size_combo_5xl_design_code
      :format: mirraw
    - :name: :quantity
      :col: 108
      :operation:
      - :to_i
      :p_name: sarees_size_combo_5xl_quantity
      :format: mirraw
    - :name: :price
      :col: 109
      :operation:
      - :to_i
      :p_name: sarees_size_combo_5xl_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 5XL'
      :mapping: :combo_sarees_size
  - - :name: :design_code
      :col: 110
      :p_name: sarees_size_combo_6xl_design_code
      :format: mirraw
    - :name: :quantity
      :col: 111
      :operation:
      - :to_i
      :p_name: sarees_size_combo_6xl_quantity
      :format: mirraw
    - :name: :price
      :col: 112
      :operation:
      - :to_i
      :p_name: sarees_size_combo_6xl_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 6XL'
      :mapping: :combo_sarees_size
      :value_if: :is_stitched
      
:HomeDecor:
- :extra:
    :designable: "HomeDecor"
    :product_type: "home_decor"
- :name: :gst_rate
  :col: 26
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 27
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :description
  :col: 24
  :operation:
  - :strip
  :p_name: description
- :name: :package_details
  :col: 6
  :present: true
  :p_name: package details
- :name: :price
  :col: 4
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
- :name: :discount_percent
  :col: 5
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 10
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 23
  :operation:
  - :strip
  :p_name: tag_list
- :name: :quantity
  :col: 3
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
- :name: :designer_collection_id
  :col: 25
  :mapping: :collection
  :p_name:  collection
- :name: :decor_color
  :col: 11
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :decor_color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "HomeDecor"
- :name: :is_sheet
  :col: 2
  :set_variable:
    :name: is_sheet
    :value: "->(val){return ['bed-sheets', 'quilts', 'jaipuri-razai', 'razais', 'duvet-covers'].include?(val)}"
- :name: :is_tapestry
  :col: 2
  :set_variable:
    :name: is_tapestry
    :value: "->(val){return val == 'tapestries'}"
- :delay_images:
  - - :name: :url
      :col: 18
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 19
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 20
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 21
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 22
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
- :delay_designables:
  - - :name: :width
      :col: 8
      :present: true
      :p_name: width
    - :name: :length
      :col: 7
      :present: true
      :p_name: length
    - :name: :height
      :col: 9
      :p_name: height
    - :name: :kind
      :value:
      - HomeDecor
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :tapestries
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :home_furnishing
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :bolster_cushion
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :material
      :col: 12
      :p_name: material
      :present: true
  - - :name: :property_value_id
      :mapping: :work
      :col: 14
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :type
      :col: 13
      :p_name: type
      :present: true
  - - :name: :property_value_id
      :mapping: :color
      :col: 11
      :p_name: color
      :present: true
  - - :name: :property_value_id
      :mapping: :size
      :col: 15
      :p_name: size
      :present: :is_sheet
  - - :name: :property_value_id
      :mapping: :thread_count
      :col: 16
      :p_name: thread_count
      :present: :is_sheet
  - - :name: :property_value_id
      :mapping: :style
      :col: 17
      :p_name: style
      :present: :is_tapestry
:HomeDecorVariant:
- :extra:
    :designable: "HomeDecor"
    :product_type: "home_decor"
- :name: :gst_rate
  :col: 28
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 29
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :description
  :col: 26
  :operation:
  - :strip
  :p_name: description
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
- :name: :price
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 14
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 25
  :operation:
  - :strip
  :p_name: tag_list
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :designer_collection_id
  :col: 27
  :mapping: :collection
  :p_name:  collection
- :name: :decor_color
  :col: 15
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :decor_color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "HomeDecor"
- :name: :is_sheet
  :col: 2
  :set_variable:
    :name: is_sheet
    :value: "->(val){return ['bed-sheets', 'quilts', 'jaipuri-razai', 'razais', 'duvet-covers'].include?(val)}"
- :name: :is_curtain
  :col: 2
  :set_variable:
    :name: is_curtain
    :value: "->(val){return val == 'curtains'}"
- :delay_images:
  - - :name: :url
      :col: 20
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 21
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 22
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 23
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 24
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
- :delay_designables:
  - - :name: :width
      :col: 12
      :present: true
      :p_name: width
    - :name: :length
      :col: 11
      :present: true
      :p_name: length
    - :name: :height
      :col: 13
      :p_name: height
    - :name: :kind
      :value:
      - HomeDecor
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :home_furnishing
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :material
      :col: 16
      :p_name: material
      :present: true
  - - :name: :property_value_id
      :mapping: :work
      :col: 18
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :type
      :col: 17
      :p_name: type
      :present: :is_sheet
  - - :name: :property_value_id
      :mapping: :color
      :col: 15
      :p_name: color
      :present: true
  - - :name: :property_value_id
      :mapping: :thread_count
      :col: 19
      :p_name: thread_count
      :present: :is_sheet
- :variants:
  - - :name: :design_code
      :col: 30
      :p_name: bedsheet_size_single_design_code
      :format: mirraw
    - :name: :quantity
      :col: 31
      :operation:
      - :to_i
      :p_name: bedsheet_size_single_quantity
      :format: mirraw
    - :name: :price
      :col: 32
      :operation:
      - :to_i
      :p_name: bedsheet_size_single_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Single'
      :mapping: :bedsheet_size
  - - :name: :design_code
      :col: 33
      :p_name: bedsheet_size_double_design_code
      :format: mirraw
    - :name: :quantity
      :col: 34
      :operation:
      - :to_i
      :p_name: bedsheet_size_double_quantity
      :format: mirraw
    - :name: :price
      :col: 35
      :operation:
      - :to_i
      :p_name: bedsheet_size_double_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Double'
      :mapping: :bedsheet_size
  - - :name: :design_code
      :col: 36
      :p_name: bedsheet_size_queen_design_code
      :format: mirraw
    - :name: :quantity
      :col: 37
      :operation:
      - :to_i
      :p_name: bedsheet_size_queen_quantity
      :format: mirraw
    - :name: :price
      :col: 38
      :operation:
      - :to_i
      :p_name: bedsheet_size_queen_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Queen'
      :mapping: :bedsheet_size
  - - :name: :design_code
      :col: 39
      :p_name: bedsheet_size_king_design_code
      :format: mirraw
    - :name: :quantity
      :col: 40
      :operation:
      - :to_i
      :p_name: bedsheet_size_king_quantity
      :format: mirraw
    - :name: :price
      :col: 41
      :operation:
      - :to_i
      :p_name: bedsheet_size_king_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'King'
      :mapping: :bedsheet_size
  - - :name: :design_code
      :col: 42
      :p_name: curtain_size_5feet_design_code
      :format: mirraw
    - :name: :quantity
      :col: 43
      :operation:
      - :to_i
      :p_name: curtain_size_5feet_quantity
      :format: mirraw
    - :name: :price
      :col: 44
      :operation:
      - :to_i
      :p_name: curtain_size_5feet_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5FEET'
      :mapping: :curtain_size
  - - :name: :design_code
      :col: 45
      :p_name: curtain_size_7feet_design_code
      :format: mirraw
    - :name: :quantity
      :col: 46
      :operation:
      - :to_i
      :p_name: curtain_size_7feet_quantity
      :format: mirraw
    - :name: :price
      :col: 47
      :operation:
      - :to_i
      :p_name: curtain_size_7feet_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '7FEET'
      :mapping: :curtain_size
  - - :name: :design_code
      :col: 48
      :p_name: curtain_size_9feet_design_code
      :format: mirraw
    - :name: :quantity
      :col: 49
      :operation:
      - :to_i
      :p_name: curtain_size_9feet_quantity
      :format: mirraw
    - :name: :price
      :col: 50
      :operation:
      - :to_i
      :p_name: curtain_size_9feet_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '9FEET'
      :mapping: :curtain_size
  - - :name: :parent_sku
      :col: 4
      :p_name: parent_sku
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 6
      :p_name: size
      :child: true
      :skip: true
:Kurti:
- :extra:
    :url: https://www.dropbox.com/scl/fi/b4iycxv7573wzd19zme51/Bulk-Upload-Kurtis-old-sheet.xlsx?rlkey=f7xox4g6scsijp1sx0t5a18nr&st=wzacr8pp&dl=0
    :amazon_url: https://www.dropbox.com/scl/fi/t0ksu7k5mur5m2inz2vj9/BulkUpload-Kurti-Sheet.xlsx?rlkey=08as8f3rk1hlbxrxeqf5kt92d&st=zihfvmb0&dl=1
    :designable: "Kurti"
    :product_type: "kurti"
    :option_type: "kurtis_size"
- :name: :gst_rate
  :col: 41
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
  :myntra_p_name: GTIN
- :name: :hsn_code
  :col: 42
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
  :myntra_p_name: HSN
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :myntra_p_name: vendorSkuCode
  :child: true
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
  :myntra_p_name: vendorArticleName
- :name: :title
  :operation:
  - :strip
- :name: :embellish
  :col: 39
  :p_name: embellish
  :myntra_p_name: Ornamentation
- :name: :description
  :col: 34
  :operation:
  - :strip
  :p_name: description
  :myntra_p_name: materialCareDescription
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
  :myntra_p_name: Product Details
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
- :name: :video_link
  :col: 28
  :p_name: video_link
- :name: :dynamic_size_chart
  :col: 29
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :price
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
  :myntra_p_name: MRP
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 12
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 30
  :operation:
  - :strip
  :p_name: tag_list
  :myntra_p_name: tags
- :name: :pattern
  :col: 38
  :p_name: pattern
- :name: :region
  :col: 37
  :p_name: region
- :name: :designer_collection_id
  :col: 40
  :mapping: :collection
  :p_name:  collection
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :kurti_fabric
  :col: 13
  :p_name: kurti_fabric
  :myntra_p_name: Fabric
- :name: :work
  :col: 20
- :name: :kurti_color
  :col: 14
  :p_name: kurti_color
  :myntra_p_name: Brand Colour (Remarks)
- :name: :kurti_category
  :col: 2
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :kurti_color
  :p_name: kurti_color
  :myntra_p_name: Brand Colour (Remarks)
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Kurti"
- :name: :is_stitched
  :col: 22
  :set_variable:
    :name: is_stitched
    :value: "->(val){return val.downcase == 'stitched'}"
- :name: :is_color_child
  :col: 3
  :set_variable:
    :name: is_color_child
    :value: "->(val){return val.downcase == 'color_child'}"
- :delay_images:
  - - :name: :url
      :col: 23
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
      :myntra_p_name: Front Image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 24
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
      :myntra_p_name: Side Image
  - - :name: :url
      :col: 25
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
      :myntra_p_name: Back Image
  - - :name: :url
      :col: 26
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
      :myntra_p_name: Detail Angle
  - - :name: :url
      :col: 27
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
      :myntra_p_name: Look Shot Image
- :delay_design_groups:
  - - :name: :parent_sku
      :col: 4
      :value_if: :is_color_child
    - :name: :designer_batch_id
      :value:
      - :get_batch_id
      :value_if: :is_color_child
- :delay_designables:
  - - :name: :length
      :col: 11
      :p_name: kurti_length
      :myntra_p_name: Front Length ( Inches )
    - :name: :side_slit
      :col: 31
      :operation:
      - :if_transform
      - 'yes'
      - true
      - false
      :p_name: side slit
      :myntra_p_name: Slit Detail
    - :name: :kind
      :value:
      - Kurti
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :kurtas_and_kurtis
      :col: 2
      :p_name: category
      :myntra_p_name: articleType
    - :name: :category_id
      :mapping: :Women
      :col: 2
      :p_name: category
      :myntra_p_name: articleType
    - :name: :category_id
      :mapping: :plus_size_kurtis
      :col: 2
      :p_name: category
      :myntra_p_name: articleType
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :fabric
      :col: 13
      :p_name: kurti_fabric
      :present: true
      :myntra_p_name: Fabric
  - - :name: :property_value_id
      :mapping: :bottom_fabric
      :col: 15
      :p_name: bottom_fabric
  - - :name: :property_value_id
      :mapping: :dupatta_fabric
      :col: 17
      :p_name: dupatta_fabric
  - - :name: :property_value_id
      :mapping: :work
      :col: 20
      :p_name: work
      :present: true
      :myntra_p_name: Print or Pattern Type
  - - :name: :property_value_id
      :mapping: :type
      :col: 21
      :p_name: type
      :myntra_p_name:   Pattern
  - - :name: :property_value_id
      :mapping: :color
      :col: 14
      :present: true
      :p_name: kurti_color
      :myntra_p_name: Brand Colour (Remarks)
  - - :name: :property_value_id
      :mapping: :bottom_color
      :col: 16
      :p_name: bottom_color
      :myntra_p_name: Prominent Colour
  - - :name: :property_value_id
      :mapping: :dupatta_color
      :col: 18
      :p_name: dupatta_color
      :myntra_p_name: Second Prominent Colour
  - - :name: :property_value_id
      :mapping: :color
      :col: 19
      :p_name: optional_kurti_color
  - - :name: :property_value_id
      :mapping: :stitching
      :col: 22
      :present: true
      :p_name: stitching
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 32
      :p_name: occasion
      :myntra_p_name: Occasion
  - - :name: :property_value_id
      :mapping: :look
      :col: 33
      :p_name: look
  - - :name: :property_value_id
      :mapping: :sleeve
      :col: 35
      :present: true
      :p_name: sleeve
      :myntra_p_name: Sleeve Length
  - - :name: :property_value_id
      :mapping: :neck_style
      :col: 36
      :p_name: neck_style
      :myntra_p_name: Neck
- :combo_variants:
  - - :name: :design_code
      :col: 104
      :p_name: kurtis_size_combo_4xs_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 105
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_4xs_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 106
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_4xs_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 4XS'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 107
      :p_name: kurtis_size_combo_3xs_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 108
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_3xs_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 109
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_3xs_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 3XS'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 113
      :p_name: kurtis_size_combo_xxs_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 114
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_xxs_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 115
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_xxs_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo XXS'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 116
      :p_name: kurtis_size_combo_xs_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 117
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_xs_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 118
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_xs_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo XS'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 119
      :p_name: kurtis_size_combo_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 120
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 121
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ComboSmall'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 122
      :p_name: kurtis_size_combo_medium_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 123
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_medium_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 124
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_medium_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ComboMedium'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 125
      :p_name: kurtis_size_combo_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 126
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 127
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ComboLarge'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 128
      :p_name: kurtis_size_combo_xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 129
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 130
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo XL'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 131
      :p_name: kurtis_size_combo_xxl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 132
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_xxl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 133
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_xxl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo XXL'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 134
      :p_name: kurtis_size_combo_3xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 135
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_3xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 136
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_3xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 3XL'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 137
      :p_name: kurtis_size_combo_4xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 138
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_4xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 139
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_4xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 4XL'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 140
      :p_name: kurtis_size_combo_5xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 141
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_5xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 142
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_5xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 5XL'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 143
      :p_name: kurtis_size_combo_6xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 144
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_6xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 145
      :operation:
      - :to_i
      :p_name: kurtis_size_combo_6xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 6XL'
      :mapping: :combo_kurtis_size
      :value_if: :is_stitched
- :variants:
  - - :name: :design_code
      :col: 43
      :p_name: kurtis_size_4xs_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 44
      :operation:
      - :to_i
      :p_name: kurtis_size_4xs_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 45
      :operation:
      - :to_i
      :p_name: kurtis_size_4xs_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4XS'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 46
      :p_name: kurtis_size_3xs_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 47
      :operation:
      - :to_i
      :p_name: kurtis_size_3xs_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 48
      :operation:
      - :to_i
      :p_name: kurtis_size_3xs_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3XS'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 49
      :p_name: kurtis_size_extra_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 50
      :operation:
      - :to_i
      :p_name: kurtis_size_extra_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 51
      :operation:
      - :to_i
      :p_name: kurtis_size_extra_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraSmall'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 52
      :p_name: kurtis_size_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 53
      :operation:
      - :to_i
      :p_name: kurtis_size_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 54
      :operation:
      - :to_i
      :p_name: kurtis_size_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraSmall'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 55
      :p_name: kurtis_size_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 56
      :operation:
      - :to_i
      :p_name: kurtis_size_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 57
      :operation:
      - :to_i
      :p_name: kurtis_size_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Small'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 58
      :p_name: kurtis_size_medium_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 59
      :operation:
      - :to_i
      :p_name: kurtis_size_medium_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 60
      :operation:
      - :to_i
      :p_name: kurtis_size_medium_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Medium'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 61
      :p_name: kurtis_size_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 62
      :operation:
      - :to_i
      :p_name: kurtis_size_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 63
      :operation:
      - :to_i
      :p_name: kurtis_size_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Large'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 64
      :p_name: kurtis_size_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 65
      :operation:
      - :to_i
      :p_name: kurtis_size_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 66
      :operation:
      - :to_i
      :p_name: kurtis_size_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraLarge'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 67
      :p_name: kurtis_size_extra_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 68
      :operation:
      - :to_i
      :p_name: kurtis_size_extra_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 69
      :operation:
      - :to_i
      :p_name: kurtis_size_extra_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraLarge'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 70
      :p_name: kurtis_size_3xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 71
      :operation:
      - :to_i
      :p_name: kurtis_size_3xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 72
      :operation:
      - :to_i
      :p_name: kurtis_size_3xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 73
      :p_name: kurtis_size_4xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 74
      :operation:
      - :to_i
      :p_name: kurtis_size_4xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 75
      :operation:
      - :to_i
      :p_name: kurtis_size_4xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 76
      :p_name: kurtis_size_5xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 77
      :operation:
      - :to_i
      :p_name: kurtis_size_5xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 78
      :operation:
      - :to_i
      :p_name: kurtis_size_5xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 79
      :p_name: kurtis_size_6xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 80
      :operation:
      - :to_i
      :p_name: kurtis_size_6xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 81
      :operation:
      - :to_i
      :p_name: kurtis_size_6xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 82
      :p_name: kurtis_size_7xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 83
      :operation:
      - :to_i
      :p_name: kurtis_size_7xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 84
      :operation:
      - :to_i
      :p_name: kurtis_size_7xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '7XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 85
      :p_name: kurtis_size_8xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 86
      :operation:
      - :to_i
      :p_name: kurtis_size_8xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 87
      :operation:
      - :to_i
      :p_name: kurtis_size_8xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '8XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 88
      :p_name: kurtis_size_9xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 89
      :operation:
      - :to_i
      :p_name: kurtis_size_9xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 90
      :operation:
      - :to_i
      :p_name: kurtis_size_9xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '9XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 91
      :p_name: kurtis_size_10xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 92
      :operation:
      - :to_i
      :p_name: kurtis_size_10xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 93
      :operation:
      - :to_i
      :p_name: kurtis_size_10xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '10XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 94
      :p_name: kurtis_size_11xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 95
      :operation:
      - :to_i
      :p_name: kurtis_size_11xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 96
      :operation:
      - :to_i
      :p_name: kurtis_size_11xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '11XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 97
      :p_name: kurtis_size_12xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 98
      :operation:
      - :to_i
      :p_name: kurtis_size_12xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 99
      :operation:
      - :to_i
      :p_name: kurtis_size_12xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '12XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 100
      :p_name: kurtis_size_13xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 101
      :operation:
      - :to_i
      :p_name: kurtis_size_13xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 102
      :operation:
      - :to_i
      :p_name: kurtis_size_13xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '13XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 103
      :p_name: kurtis_size_14xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 104
      :operation:
      - :to_i
      :p_name: kurtis_size_14xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 105
      :operation:
      - :to_i
      :p_name: kurtis_size_14xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '14XL'
      :mapping: :kurtis_size
      :value_if: :is_stitched
  - - :name: :parent_sku
      :col: 4
      :p_name: parent_sku
      :value_if: :is_stitched
      :child: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 6
      :p_name: size
      :value_if: :is_stitched
      :child: true
      :skip: true
      :myntra_p_name: Standard Size
:SalwarKameez:
- :extra:
    :url: https://www.dropbox.com/s/u2ymm5zvn9kt9tk/Bulk%20Upload%20Salwar%20Kameez.xlsx?dl=1
    :amazon_url: https://www.dropbox.com/scl/fi/h9m2j7vg861u2obhkd3am/BulkUpload-SalwarKameez-sheet.xlsx?rlkey=vh67nucyqyakesokedd1ghq1e&st=bmzycja5&dl=1
    :designable: "SalwarKameez"
    :product_type: "salwar"
    :option_type: "suit_size"
- :name: :gst_rate
  :col: 51
  :present: true
  :p_name: gst_rate
  :myntra_p_name: GTIN
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 52
  :present: true
  :p_name: hsn_code
  :myntra_p_name: HSN
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
  :myntra_p_name: vendorSkuCode
- :name: :title
  :col: 1
  :present: true
  :operation:
  - :humanize 
  :p_name: title
  :myntra_p_name: vendorArticleName
- :name: :title
  :operation:
  - :strip
- :name: :embellish
  :col: 44
  :p_name: embellish
- :name: :description
  :col: 46
  :operation:
  - :strip
  :p_name: description
  :myntra_p_name: sizeAndFitDescription
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
  :skip: true
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
  :myntra_p_name: Product Details
- :name: :price
  :col: 8
  :present: true
  :child: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :myntra_p_name: MRP
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 11
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 34
  :operation:
  - :strip
  :p_name: tag_list
  :myntra_p_name: tags
- :name: :pattern
  :col: 43
  :p_name: pattern
- :name: :region
  :col: 42
  :p_name: region
- :name: :designer_collection_id
  :col: 50
  :mapping: :collection
  :p_name:  collection
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :kameez_fabric
  :col: 15
- :name: :work
  :col: 35
- :name: :video_link
  :col: 32
  :p_name: video_link
- :name: :dynamic_size_chart
  :col: 33
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :kameez_color
  :col: 13
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :kameez_color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "SalwarKameez"
- :name: :is_stitched
  :col: 27
  :set_variable:
    :name: is_stitched
    :value: "->(val){return val.downcase == 'stitched'}"
- :name: :is_unstitched
  :col: 27
  :set_variable:
    :name: is_unstitched
    :value: "->(val){return val.downcase == 'unstitched'}"
- :name: :is_semi_stitched
  :col: 27
  :set_variable:
    :name: is_semi_stitched
    :value: "->(val){return val.downcase == 'semi_stitched'}"
- :delay_images:
  - - :name: :url
      :col: 28
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
      :myntra_p_name: Front Image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 29
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
      :myntra_p_name: Side Image
  - - :name: :url
      :col: 30
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
      :myntra_p_name: Back Image
  - - :name: :url
      :col: 31
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
      :myntra_p_name: Detail Angle
- :delay_designables:
  - - :name: :max_kameez_length
      :col: 12
      :present: true
      :p_name: maximum_length_of_kameez_in_inches
    - :name: :max_bottom_length
      :col: 16
      :p_name: maximum_length_of_bottom_in_metres
    - :name: :dupatta_length
      :col: 20
      :present: :is_semi_stitched
      :p_name: maximum_length_of_dupatta_in_metres
    - :name: :lining_length
      :col: 25
      :p_name: length_of_lining_in_metres
    - :name: :lining_available
      :col: 24
      :operation:
      - :if_transform
      - 'yes'
      - true
      - false
      :p_name: lining_or_astar_availability
    - :name: :max_kameez_length
      :col: 12
      :present: true
      :value_if: :is_stitched
      :p_name: maximum_length_of_kameez_in_inches
    - :name: :max_kameez_length
      :col: 12
      :present: true
      :value_if: :is_semi_stitched
      :p_name: maximum_length_of_kameez_in_inches
    - :name: :kameez_length
      :col: 49
      :present: true
      :value_if: :is_unstitched
      :p_name: kameez_fabric_length
      :myntra_p_name: Front Length ( Inches )
    - :name: :kameez_color
      :col: 13
      :present: true
      :p_name: kameez_color
    - :name: :bottom_color
      :col: 17
      :p_name: bottom_color
    - :name: :dupatta_color
      :col: 21
      :p_name: dupatta_color
    - :name: :bottom_length
      :col: 16
      :p_name: maximum_length_of_bottom_in_metres
    - :name: :lining_fabric
      :col: 26
      :p_name: lining_fabric
    - :name: :min_bustsize_fit
      :col: 47
      :value_if: :is_semi_stitched
      :present: :is_semi_stitched
      :p_name: minimum_bustsize_fit
      :myntra_p_name: Bust ( Inches )
    - :name: :max_bustsize_fit
      :col: 48
      :value_if: :is_semi_stitched
      :present: :is_semi_stitched
      :p_name: maximum_bustsize_fit
      :myntra_p_name: To Fit Bust ( Inches )
    - :name: :max_dupatta_length
      :col: 20
      :present: :is_semi_stitched
      :p_name: maximum_length_of_dupatta_in_metres
    - :name: :kind
      :value:
      - SalwarKameez
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :salwar_kameez
      :col: 2
      :p_name: category
      :myntra_p_name: articleType
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :kameez_color
      :col: 13
      :p_name: kameez_color
      :present: true
  - - :name: :property_value_id
      :mapping: :kameez_color
      :col: 14
      :p_name: kameez_color_optional
  - - :name: :property_value_id
      :mapping: :bottom_color
      :col: 17
      :p_name: bottom_color
  - - :name: :property_value_id
      :mapping: :bottom_color
      :col: 18
      :p_name: bottom_color_optional
  - - :name: :property_value_id
      :mapping: :dupatta_color
      :col: 21
      :p_name: dupatta_color
  - - :name: :property_value_id
      :mapping: :dupatta_color
      :col: 22
      :p_name: dupatta_color_optional
  - - :name: :property_value_id
      :mapping: :kameez_fabric
      :col: 15
      :p_name: kameez_fabric
      :present: true
      :myntra_p_name: Fabric
  - - :name: :property_value_id
      :mapping: :bottom_fabric
      :col: 19
      :p_name: bottom_fabric
      :myntra_p_name: Fabric
  - - :name: :property_value_id
      :mapping: :dupatta_fabric
      :col: 23
      :p_name: dupatta_fabric
      :myntra_p_name: Fabric
  - - :name: :property_value_id
      :mapping: :lining_fabric
      :col: 26
      :p_name: lining_fabric
  - - :name: :property_value_id
      :mapping: :stitching
      :col: 27
      :p_name: stitching
      :present: true
  - - :name: :property_value_id
      :mapping: :work
      :col: 36
      :p_name: work
      :present: true
      :myntra_p_name: Pattern
  - - :name: :property_value_id
      :mapping: :type
      :col: 37
      :p_name: type
      :myntra_p_name: Type
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 35
      :p_name: occasion
      :myntra_p_name: Occasion
  - - :name: :property_value_id
      :mapping: :look
      :col: 38
      :p_name: look
  - - :name: :property_value_id
      :mapping: :bottom_style
      :col: 39
      :p_name: bottom_style
  - - :name: :property_value_id
      :mapping: :sleeve
      :col: 40
      :present: true
      :p_name: sleeve
      :myntra_p_name: Sleeve Length
  - - :name: :property_value_id
      :mapping: :neck_style
      :col: 41
      :p_name: neck_style
  - - :name: :property_value_id
      :mapping: :celebrity
      :col: 45
      :p_name: celebrity
- :combo_variants:
  - - :name: :design_code
      :col: 113
      :p_name: suit_size_combo_28_design_code
      :format: mirraw
    - :name: :quantity
      :col: 114
      :operation:
      - :to_i
      :p_name: suit_size_combo_28_quantity
      :format: mirraw
    - :name: :price
      :col: 115
      :operation:
      - :to_i
      :p_name: suit_size_combo_28_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 28'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 116
      :p_name: suit_size_combo_30_design_code
      :format: mirraw
    - :name: :quantity
      :col: 117
      :operation:
      - :to_i
      :p_name: suit_size_combo_30_quantity
      :format: mirraw
    - :name: :price
      :col: 118
      :operation:
      - :to_i
      :p_name: suit_size_combo_30_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 30'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 119
      :p_name: suit_size_combo_32_design_code
      :format: mirraw
    - :name: :quantity
      :col: 120
      :operation:
      - :to_i
      :p_name: suit_size_combo_32_quantity
      :format: mirraw
    - :name: :price
      :col: 121
      :operation:
      - :to_i
      :p_name: suit_size_combo_32_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 32'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 122
      :p_name: suit_size_combo_34_design_code
      :format: mirraw
    - :name: :quantity
      :col: 123
      :operation:
      - :to_i
      :p_name: suit_size_combo_34_quantity
      :format: mirraw
    - :name: :price
      :col: 124
      :operation:
      - :to_i
      :p_name: suit_size_combo_34_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 34'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 125
      :p_name: suit_size_combo_36_design_code
      :format: mirraw
    - :name: :quantity
      :col: 126
      :operation:
      - :to_i
      :p_name: suit_size_combo_36_quantity
      :format: mirraw
    - :name: :price
      :col: 127
      :operation:
      - :to_i
      :p_name: suit_size_combo_36_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 36'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 128
      :p_name: suit_size_combo_38_design_code
      :format: mirraw
    - :name: :quantity
      :col: 129
      :operation:
      - :to_i
      :p_name: suit_size_combo_38_quantity
      :format: mirraw
    - :name: :price
      :col: 130
      :operation:
      - :to_i
      :p_name: suit_size_combo_38_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 38'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 131
      :p_name: suit_size_combo_40_design_code
      :format: mirraw
    - :name: :quantity
      :col: 132
      :operation:
      - :to_i
      :p_name: suit_size_combo_40_quantity
      :format: mirraw
    - :name: :price
      :col: 133
      :operation:
      - :to_i
      :p_name: suit_size_combo_40_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 40'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 134
      :p_name: suit_size_combo_42_design_code
      :format: mirraw
    - :name: :quantity
      :col: 135
      :operation:
      - :to_i
      :p_name: suit_size_combo_42_quantity
      :format: mirraw
    - :name: :price
      :col: 136
      :operation:
      - :to_i
      :p_name: suit_size_combo_42_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 42'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 137
      :p_name: suit_size_combo_44_design_code
      :format: mirraw
    - :name: :quantity
      :col: 138
      :operation:
      - :to_i
      :p_name: suit_size_combo_44_quantity
      :format: mirraw
    - :name: :price
      :col: 139
      :operation:
      - :to_i
      :p_name: suit_size_combo_44_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 44'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 140
      :p_name: suit_size_combo_46_design_code
      :format: mirraw
    - :name: :quantity
      :col: 141
      :operation:
      - :to_i
      :p_name: suit_size_combo_46_quantity
      :format: mirraw
    - :name: :price
      :col: 142
      :operation:
      - :to_i
      :p_name: suit_size_combo_46_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 46'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 143
      :p_name: suit_size_combo_48_design_code
      :format: mirraw
    - :name: :quantity
      :col: 144
      :operation:
      - :to_i
      :p_name: suit_size_combo_48_quantity
      :format: mirraw
    - :name: :price
      :col: 145
      :operation:
      - :to_i
      :p_name: suit_size_combo_48_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 48'
      :mapping: :combo_suit_size
  - - :name: :design_code
      :col: 146
      :p_name: suit_size_combo_50_design_code
      :format: mirraw
    - :name: :quantity
      :col: 147
      :operation:
      - :to_i
      :p_name: suit_size_combo_50_quantity
      :format: mirraw
    - :name: :price
      :col: 148
      :operation:
      - :to_i
      :p_name: suit_size_combo_50_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 50'
      :mapping: :combo_suit_size
- :variants:
  - - :name: :design_code
      :col: 53
      :p_name: suit_size_28_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 54
      :operation:
      - :to_i
      :p_name: suit_size_28_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 55
      :operation:
      - :to_i
      :p_name: suit_size_28_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '28'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 56
      :p_name: suit_size_30_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 57
      :operation:
      - :to_i
      :p_name: suit_size_30_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 58
      :operation:
      - :to_i
      :p_name: suit_size_30_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '30'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 59
      :p_name: suit_size_32_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 60
      :operation:
      - :to_i
      :p_name: suit_size_32_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 61
      :operation:
      - :to_i
      :p_name: suit_size_32_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '32'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 62
      :p_name: suit_size_34_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 63
      :operation:
      - :to_i
      :p_name: suit_size_34_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 64
      :operation:
      - :to_i
      :p_name: suit_size_34_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '34'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 65
      :p_name: suit_size_36_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 66
      :operation:
      - :to_i
      :p_name: suit_size_36_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 67
      :operation:
      - :to_i
      :p_name: suit_size_36_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '36'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 68
      :p_name: suit_size_38_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 69
      :operation:
      - :to_i
      :p_name: suit_size_38_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 70
      :operation:
      - :to_i
      :p_name: suit_size_38_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '38'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 71
      :p_name: suit_size_40_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 72
      :operation:
      - :to_i
      :p_name: suit_size_40_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 73
      :operation:
      - :to_i
      :p_name: suit_size_40_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '40'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 74
      :p_name: suit_size_42_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 75
      :operation:
      - :to_i
      :p_name: suit_size_42_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 76
      :operation:
      - :to_i
      :p_name: suit_size_42_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '42'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 77
      :p_name: suit_size_44_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 78
      :operation:
      - :to_i
      :p_name: suit_size_44_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 79
      :operation:
      - :to_i
      :p_name: suit_size_44_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '44'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 80
      :p_name: suit_size_46_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 81
      :operation:
      - :to_i
      :p_name: suit_size_46_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 82
      :operation:
      - :to_i
      :p_name: suit_size_46_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '46'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 83
      :p_name: suit_size_48_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 84
      :operation:
      - :to_i
      :p_name: suit_size_48_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 85
      :operation:
      - :to_i
      :p_name: suit_size_48_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '48'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 86
      :p_name: suit_size_50_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 87
      :operation:
      - :to_i
      :p_name: suit_size_50_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 88
      :operation:
      - :to_i
      :p_name: suit_size_50_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '50'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 89
      :p_name: suit_size_52_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 90
      :operation:
      - :to_i
      :p_name: suit_size_52_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 91
      :operation:
      - :to_i
      :p_name: suit_size_52_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '52'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 92
      :p_name: suit_size_54_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 93
      :operation:
      - :to_i
      :p_name: suit_size_54_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 94
      :operation:
      - :to_i
      :p_name: suit_size_54_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '54'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 95
      :p_name: suit_size_56_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 96
      :operation:
      - :to_i
      :p_name: suit_size_56_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 97
      :operation:
      - :to_i
      :p_name: suit_size_56_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '56'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 98
      :p_name: suit_size_58_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 99
      :operation:
      - :to_i
      :p_name: suit_size_58_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 100
      :operation:
      - :to_i
      :p_name: suit_size_58_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '58'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 101
      :p_name: suit_size_60_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 102
      :operation:
      - :to_i
      :p_name: suit_size_60_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 103
      :operation:
      - :to_i
      :p_name: suit_size_60_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '60'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 104
      :p_name: suit_size_62_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 105
      :operation:
      - :to_i
      :p_name: suit_size_62_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 106
      :operation:
      - :to_i
      :p_name: suit_size_62_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '62'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 107
      :p_name: suit_size_64_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 108
      :operation:
      - :to_i
      :p_name: suit_size_64_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 109
      :operation:
      - :to_i
      :p_name: suit_size_64_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '64'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 110
      :p_name: suit_size_66_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 111
      :operation:
      - :to_i
      :p_name: suit_size_66_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 112
      :operation:
      - :to_i
      :p_name: suit_size_66_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '66'
      :mapping: :suit_size
      :value_if: :is_stitched
  - - :name: :design_id
      :col: 4
      :p_name: parent_sku
      :value_if: :is_stitched
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 6
      :p_name: size
      :myntra_p_name: Standard Size
      :value_if: :is_stitched
      :child: true
      :skip: true
:Jewellery:
- :extra:
    :url: https://www.dropbox.com/s/wmt147lihl7mk84/Bulk%20Upload%20Jewellery.xlsx?dl=1
    :amazon_url: https://www.dropbox.com/scl/fi/udzb5bxuvevpgl6rxngh4/BulkUpload-Jewellery-Sheet.xlsx?rlkey=tsqtfatx5fwgx9m1zal2539kc&st=wpy5gzqr&dl=1
    :designable: "Jewellery"
    :product_type: "jewellery"
- :name: :gst_rate
  :col: 41
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
  :myntra_p_name: GTIN
- :name: :hsn_code
  :col: 42
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
  :myntra_p_name: HSN
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
  :myntra_p_name: vendorSkuCode
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
  :myntra_p_name: productDisplayName
- :name: :title
  :operation:
  - :strip
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
  :skip: true
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
  :myntra_p_name: Product Details
- :name: :weight
  :col: 11
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :price
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
  :myntra_p_name: MRP
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :description
  :col: 38
  :operation:
  - :strip
  :p_name: description
  :myntra_p_name: sizeAndFitDescription
- :name: :designer_collection_id
  :col: 40
  :mapping: :collection
  :p_name:  collection
- :name: :tag_list
  :col: 39
  :operation:
  - :strip
  :p_name: tag_list
  :myntra_p_name: tags
- :name: :gemstones
  :col: 19
  :myntra_p_name: Stone Type
- :name: :color
  :col: 19
  :myntra_p_name: Prominent Colour
- :name: :category
  :col: 2
  :myntra_p_name: articleType
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :color
- :name: :video_link
  :col: 17
  :p_name: video_link
- :name: :dynamic_size_chart
  :col: 18
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Jewellery"
- :delay_images:
  - - :name: :url
      :col: 12
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
      :myntra_p_name: Front Image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 13
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
      :myntra_p_name: Side Image
  - - :name: :url
      :col: 14
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
      :myntra_p_name: Back Image
  - - :name: :url
      :col: 15
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
      :myntra_p_name: Detail Angle
  - - :name: :url
      :col: 16
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
      :myntra_p_name: Look Shot Image
- :categories_designs:
  - - :name: :category_id
      :mapping: :Jewellery
      :col: 2
      :p_name: category
      :myntra_p_name: articleType
    - :name: :category_earring
      :mapping: :Earrings
      :col: 2
      :p_name: category
      :myntra_p_name: articleType
      :set_variable:
        :name: is_earring
        :value: "->(val){return val.present?}"
- :delay_designables:
  - - :name: :earings_height
      :col: 24
      :p_name: height_in_mm
      :present: :is_earring
    - :name: :earings_width
      :col: 25
      :p_name: width_in_mm
      :present: :is_earring
    - :name: :height
      :col: 26
      :p_name: length_in_cms
    - :name: :width
      :col: 27
      :p_name: width_in_cms
    - :name: :speciality
      :col: 30
      :p_name: speciality
    - :name: :finish
      :col: 35
      :p_name: finish
    - :name: :setting
      :col: 36
      :p_name: setting
    - :name: :care
      :col: 37
      :p_name: care
    - :name: :kind
      :value:
      - Jewellery
    - :name: :params
      :value:
      - :get_attribute
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :jewellery_work
      :col: 19
      :p_name: jewellery_work
      :present: true
  - - :name: :property_value_id
      :mapping: :color
      :col: 20
      :p_name: color
      :present: true
      :myntra_p_name: Prominent Colour
  - - :name: :property_value_id
      :mapping: :gemstones
      :col: 21
      :p_name: gemstones
      :myntra_p_name: Stone Type
  - - :name: :property_value_id
      :mapping: :plating
      :col: 22
      :p_name: plating
      :present: true
      :myntra_p_name: Plating
  - - :name: :property_value_id
      :mapping: :base_material
      :col: 23
      :p_name: base_material
      :present: true
      :myntra_p_name: Base Metal
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 28
      :p_name: occasion
      :myntra_p_name: Occasion
  - - :name: :property_value_id
      :mapping: :look
      :col: 29
      :p_name: look
  - - :name: :property_value_id
      :mapping: :certification
      :col: 31
      :p_name: certification
  - - :name: :property_value_id
      :mapping: :pearl_type
      :col: 32
      :p_name: pearl type
  - - :name: :property_value_id
      :mapping: :pearl_shape
      :col: 33
      :p_name: pearl shape
  - - :name: :property_value_id
      :mapping: :piercing
      :col: 34
      :p_name: piercing
- :variants:
  - - :name: :design_code
      :col: 43
      :p_name: bangle_size_2_2_design_code
      :format: mirraw
    - :name: :quantity
      :col: 44
      :operation:
      - :to_i
      :p_name: bangle_size_2_2_quantity
      :format: mirraw
    - :name: :price
      :col: 45
      :operation:
      - :to_i
      :p_name: bangle_size_2_2_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '2.2'
      :mapping: :bangle_size
  - - :name: :design_code
      :col: 46
      :p_name: bangle_size_2_4_design_code
      :format: mirraw
    - :name: :quantity
      :col: 47
      :operation:
      - :to_i
      :p_name: bangle_size_2_4_quantity
      :format: mirraw
    - :name: :price
      :col: 48
      :operation:
      - :to_i
      :p_name: bangle_size_2_4_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '2.4'
      :mapping: :bangle_size
  - - :name: :design_code
      :col: 49
      :p_name: bangle_size_2_6_design_code
      :format: mirraw
    - :name: :quantity
      :col: 50
      :operation:
      - :to_i
      :p_name: bangle_size_2_6_quantity
      :format: mirraw
    - :name: :price
      :col: 51
      :operation:
      - :to_i
      :p_name: bangle_size_2_6_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '2.6'
      :mapping: :bangle_size
  - - :name: :design_code
      :col: 52
      :p_name: bangle_size_2_8_design_code
      :format: mirraw
    - :name: :quantity
      :col: 53
      :operation:
      - :to_i
      :p_name: bangle_size_2_8_quantity
      :format: mirraw
    - :name: :price
      :col: 54
      :operation:
      - :to_i
      :p_name: bangle_size_2_8_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '2.8'
      :mapping: :bangle_size
  - - :name: :design_code
      :col: 55
      :p_name: bangle_size_2_10_design_code
      :format: mirraw
    - :name: :quantity
      :col: 56
      :operation:
      - :to_i
      :p_name: bangle_size_2_10_quantity
      :format: mirraw
    - :name: :price
      :col: 57
      :operation:
      - :to_i
      :p_name: bangle_size_2_10_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '2.10'
      :mapping: :bangle_size
  - - :name: :design_code
      :col: 58
      :p_name: bangle_size_free_size_design_code
      :format: mirraw
    - :name: :quantity
      :col: 59
      :operation:
      - :to_i
      :p_name: bangle_size_free_size_quantity
      :format: mirraw
    - :name: :price
      :col: 60
      :operation:
      - :to_i
      :p_name: bangle_size_free_size_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'free_size'
      :mapping: :bangle_size

  - - :name: :design_code
      :col: 61
      :p_name: ring_size_7_design_code
      :format: mirraw
    - :name: :quantity
      :col: 62
      :operation:
      - :to_i
      :p_name: ring_size_7_quantity
      :format: mirraw
    - :name: :price
      :col: 63
      :operation:
      - :to_i
      :p_name: ring_size_7_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '7'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 64
      :p_name: ring_size_8_design_code
      :format: mirraw
    - :name: :quantity
      :col: 65
      :operation:
      - :to_i
      :p_name: ring_size_8_quantity
      :format: mirraw
    - :name: :price
      :col: 66
      :operation:
      - :to_i
      :p_name: ring_size_8_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '8'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 67
      :p_name: ring_size_9_design_code
      :format: mirraw
    - :name: :quantity
      :col: 68
      :operation:
      - :to_i
      :p_name: ring_size_9_quantity
      :format: mirraw
    - :name: :price
      :col: 69
      :operation:
      - :to_i
      :p_name: ring_size_9_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '9'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 70
      :p_name: ring_size_10_design_code
      :format: mirraw
    - :name: :quantity
      :col: 71
      :operation:
      - :to_i
      :p_name: ring_size_10_quantity
      :format: mirraw
    - :name: :price
      :col: 72
      :operation:
      - :to_i
      :p_name: ring_size_10_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '10'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 73
      :p_name: ring_size_11_design_code
      :format: mirraw
    - :name: :quantity
      :col: 74
      :operation:
      - :to_i
      :p_name: ring_size_11_quantity
      :format: mirraw
    - :name: :price
      :col: 75
      :operation:
      - :to_i
      :p_name: ring_size_11_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '11'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 76
      :p_name: ring_size_12_design_code
      :format: mirraw
    - :name: :quantity
      :col: 77
      :operation:
      - :to_i
      :p_name: ring_size_12_quantity
      :format: mirraw
    - :name: :price
      :col: 78
      :operation:
      - :to_i
      :p_name: ring_size_12_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '12'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 79
      :p_name: ring_size_13_design_code
      :format: mirraw
    - :name: :quantity
      :col: 80
      :operation:
      - :to_i
      :p_name: ring_size_13_quantity
      :format: mirraw
    - :name: :price
      :col: 81
      :operation:
      - :to_i
      :p_name: ring_size_13_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '13'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 82
      :p_name2: ring_size_14_design_code
      :format: mirraw
    - :name: :quantity
      :col: 83
      :operation:
      - :to_i
      :p_name: ring_size_14_quantity
      :format: mirraw
    - :name: :price
      :col: 84
      :operation:
      - :to_i
      :p_name: ring_size_14_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '14'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 85
      :p_name: ring_size_15_design_code
      :format: mirraw
    - :name: :quantity
      :col: 86
      :operation:
      - :to_i
      :p_name: ring_size_15_quantity
      :format: mirraw
    - :name: :price
      :col: 87
      :operation:
      - :to_i
      :p_name: ring_size_15_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '15'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 88
      :p_name: ring_size_16_design_code
      :format: mirraw
    - :name: :quantity
      :col: 89
      :operation:
      - :to_i
      :p_name: ring_size_16_quantity
      :format: mirraw
    - :name: :price
      :col: 90
      :operation:
      - :to_i
      :p_name: ring_size_16_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '16'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 91
      :p_name: ring_size_17_design_code
      :format: mirraw
    - :name: :quantity
      :col: 92
      :operation:
      - :to_i
      :p_name: ring_size_17_quantity
      :format: mirraw
    - :name: :price
      :col: 93
      :operation:
      - :to_i
      :p_name: ring_size_17_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '17'
      :mapping: :ring_size
  - - :name: :design_code
      :col: 94
      :p_name: ring_size_free_size_design_code
      :format: mirraw
    - :name: :quantity
      :col: 95
      :operation:
      - :to_i
      :p_name: ring_size_free_size_quantity
      :format: mirraw
    - :name: :price
      :col: 96
      :operation:
      - :to_i
      :p_name: ring_size_free_size_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'free_size'
      :mapping: :ring_size
  - - :name: :design_id
      :col: 4
      :p_name: parent_sku
      :value_if: :is_stitched
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 6
      :p_name: size
      :value_if: :is_stitched
      :child: true
      :skip: true
      :myntra_p_name: Standard Size
:Lehenga:
- :extra:
    :url: https://www.dropbox.com/s/4cd2al8r0y2tpn3/Bulk%20Upload%20Lehenga.xlsx?dl=1
    :amazon_url: https://www.dropbox.com/scl/fi/a6qpl8lae00ndy8yldmek/BulkUpload-Lehenga-Sheet.xlsx?rlkey=98j9a2dynivqrvm6b0icndm1f&st=1lv29one&dl=1
    :designable: "Lehenga"
    :product_type: "lehenga"
- :name: :gst_rate
  :col: 59
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
  :myntra_p_name: GTIN
- :name: :hsn_code
  :col: 60
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
  :myntra_p_name: HSN
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
  :myntra_p_name: vendorSkuCode
- :name: :title
  :col: 1
  :opration:
  - :humanize 
  :present: true
  :p_name: title
  :myntra_p_name: productDisplayName
- :name: :title
  :operation:
  - :strip
- :name: :video_link
  :col: 36
  :p_name: video_link
- :name: :dynamic_size_chart
  :col: 37
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :description
  :col: 2
  :operation:
  - :strip
  :p_name: description
  :myntra_p_name: sizeAndFitDescription
- :name: :parent_child
  :col: 4
  :p_name: parent_child
  :child: true
  :skip: true
- :name: :package_details
  :col: 11
  :present: true
  :p_name: package details
  :myntra_p_name: Package Contains
- :name: :weight
  :col: 12
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 38
  :operation:
  - :strip
  :p_name: tag_list
  :myntra_p_name: tags
- :name: :quantity
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :price
  :col: 9
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
  :myntra_p_name: MRP
- :name: :discount_percent
  :col: 10
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :region
  :col: 49
  :p_name: region
  :myntra_p_name: Regions
- :name: :pattern
  :col: 50
  :p_name: pattern
- :name: :embellish
  :col: 51
  :p_name: embellish
  :myntra_p_name: Ornamentation
- :name: :designer_collection_id
  :col: 58
  :mapping: :collection
  :p_name:  collection

- :name: :lehenga_fabric
  :col: 14
  :myntra_p_name: Lehenga Fabric
- :name: :work
  :col: 39
- :name: :lehenga_color
  :col: 15
  :myntra_p_name: Prominent Colour
- :name: :stitching
  :col: 47
  :myntra_p_name: Choli Stitch
- :name: :published
  :value:
  - true
- :name: :lehenga_color
  :value:
  - :lehenga_color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Lehenga"

- :name: :is_stitched
  :col: 48
  :set_variable:
    :name: is_stitched
    :value: "->(val){return val.downcase == 'stitched'}"
- :name: :is_unstitched
  :col: 47
  :set_variable:
    :name: is_unstitched
    :value: "->(val){return val.downcase == 'unstitched'}"
- :name: :blouse_available
  :col: 20
  :set_variable:
    :name: blouse_available
    :value: "->(val){return val.downcase == 'yes'}"

- :delay_images:
  - - :name: :url
      :col: 32
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
      :myntra_p_name: Front Image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 33
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
      :myntra_p_name: Side Image
  - - :name: :url
      :col: 34
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
      :myntra_p_name: Back Image
  - - :name: :url
      :col: 35
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
      :myntra_p_name: Detail Angle

- :categories_designs:
  - - :name: :category_id
      :mapping: :Lehengas
      :col: 3
      :p_name: category
      :myntra_p_name: articleType
    - :name: :category_id
      :mapping: :kids_lehenga_choli
      :col: 3
      :p_name: category
      :myntra_p_name: articleType
    - :name: :category_id
      :mapping: :plus_size_lehenga
      :col: 3
      :p_name: category
      :myntra_p_name: articleType

- :delay_designables:
  - - :name: :length
      :col: 13
      :p_name: lehenga_length
      :present: true
    - :name: :lining_available
      :col: 18
      :operation:
      - :if_transform
      - 'yes'
      - true
      - false
      :p_name: lining_or_astar_availability
    - :name: :blouse_available
      :col: 20
      :operation:
      - :if_transform
      - 'yes'
      - true
      - false
      :p_name: blouse_availability
    - :name: :blouse_image
      :col: 21
      :operation:
      - :if_transform
      - 'yes'
      - true
      - false
      :p_name: blouse_as_shown_in_the_image
    - :name: :blouse_fabric
      :col: 25
      :present: :blouse_available
      :p_name: blouse_fabric
      :myntra_p_name: Blouse Fabric
    - :name: :dupatta_length
      :col: 26
      :present: true
      :p_name: dupatta_length
    - :name: :model_size
      :col: 31
      :present: :is_stitched
      :p_name: model_size_wearing
    - :name: :min_waist_size
      :col: 52
      :present: :is_unstitched
      :p_name: minimum_waist_size
      :myntra_p_name: Across Shoulder ( Inches )
    - :name: :max_waist_size
      :col: 53
      :present: :is_unstitched
      :p_name: maximum_waist_size
    - :name: :min_hip_size
      :col: 54
      :present: :is_unstitched
      :p_name: minimum_hip_size
    - :name: :max_hip_size
      :col: 55
      :present: :is_unstitched
      :p_name: maximum_hip_size
    - :name: :lining_size
      :col: 56
      :present: :is_unstitched
      :p_name: size_of_lining_in_metres
    - :name: :blouse_size
      :col: 57
      :present: :is_unstitched
      :p_name: size_of_blouse_in_cms
    - :name: :kind
      :value:
      - Lehenga
    - :name: :params
      :value:
      - :get_attribute
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :lehenga_fabric
      :col: 14
      :p_name: lehenga_fabric
      :present: true
      :myntra_p_name: Lehenga Fabric
  - - :name: :property_value_id
      :mapping: :lehenga_color
      :col: 15
      :p_name: lehenga_color
      :present: true
      :myntra_p_name: Prominent Colour
  - - :name: :property_value_id
      :mapping: :lehenga_color
      :col: 16
      :p_name: lehenga_color_optional
  - - :name: :property_value_id
      :mapping: :lehenga_work
      :col: 17
      :p_name: lehenga_work
      :present: true
      :myntra_p_name: Top Pattern
  - - :name: :property_value_id
      :mapping: :lining_fabric
      :col: 19
      :p_name: lining_fabric
      :myntra_p_name: Lehenga Lining Fabric
  - - :name: :property_value_id
      :mapping: :blouse_color
      :col: 22
      :p_name: blouse_color
      :present: :blouse_available
      :myntra_p_name: Second Prominent Colour
  - - :name: :property_value_id
      :mapping: :blouse_color
      :col: 23
      :p_name: blouse_color_optional
  - - :name: :property_value_id
      :mapping: :blouse_work
      :col: 24
      :p_name: blouse_work
      :present: :blouse_available
      :myntra_p_name: Bottom Pattern
  - - :name: :property_value_id
      :mapping: :blouse_fabric
      :col: 25
      :p_name: blouse_fabric
      :present: :blouse_available
      :myntra_p_name: Blouse Fabric
  - - :name: :property_value_id
      :mapping: :dupatta_fabric
      :col: 27
      :p_name: dupatta_fabric
      :present: true
      :myntra_p_name: Dupatta Fabric
  - - :name: :property_value_id
      :mapping: :dupatta_color
      :col: 28
      :p_name: dupatta_color
      :present: true
      :myntra_p_name: Third Prominent Colour
  - - :name: :property_value_id
      :mapping: :dupatta_color
      :col: 29
      :p_name: dupatta_color_optional
  - - :name: :property_value_id
      :mapping: :dupatta_work
      :col: 30
      :p_name: dupatta_work
      :present: true
      :myntra_p_name: Dupatta Pattern
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 39
      :p_name: occasion
  - - :name: :property_value_id
      :mapping: :work
      :col: 40
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :type
      :col: 41
      :p_name: type
      :present: true
      :myntra_p_name: Technique
  - - :name: :property_value_id
      :mapping: :look
      :col: 42
      :p_name: look
      :present: true
  - - :name: :property_value_id
      :mapping: :pallu_style
      :col: 43
      :p_name: pallu_style
      :present: true
  - - :name: :property_value_id
      :mapping: :lehenga_style
      :col: 44
      :p_name: lehenga_style
      :present: true
  - - :name: :property_value_id
      :mapping: :choli_sleeve
      :col: 45
      :p_name: choli_sleeve
      :present: true
      :myntra_p_name: Sleeve Length
  - - :name: :property_value_id
      :mapping: :choli_neck_style
      :col: 46
      :p_name: choli_neck_style
      :present: true
      :myntra_p_name: Neck
  - - :name: :property_value_id
      :mapping: :celebrity
      :col: 47
      :p_name: celebrity
  - - :name: :property_value_id
      :mapping: :stitching
      :col: 48
      :p_name: stitching
      :present: true
      :myntra_p_name: Choli Stitch
  
- :combo_variants:
  - - :name: :design_code
      :col: 133
      :p_name: lehenga_blouse_size_combo_32_design_code
      :format: mirraw
    - :name: :quantity
      :col: 134
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_32_quantity
      :format: mirraw
    - :name: :price
      :col: 135
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_32_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 32'
      :mapping: :combo_kurta_size
  - - :name: :design_code
      :col: 136
      :p_name: lehenga_blouse_size_combo_34_design_code
      :format: mirraw
    - :name: :quantity
      :col: 137
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_34_quantity
      :format: mirraw
    - :name: :price
      :col: 138
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_34_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 34'
      :mapping: :combo_kurta_size
  - - :name: :design_code
      :col: 139
      :p_name: lehenga_blouse_size_combo_36_design_code
      :format: mirraw
    - :name: :quantity
      :col: 140
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_36_quantity
      :format: mirraw
    - :name: :price
      :col: 141
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_36_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 36'
      :mapping: :combo_kurta_size
  - - :name: :design_code
      :col: 142
      :p_name: lehenga_blouse_size_combo_40_design_code
      :format: mirraw
    - :name: :quantity
      :col: 143
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_40_quantity
      :format: mirraw
    - :name: :price
      :col: 144
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_40_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 40'
      :mapping: :combo_kurta_size
  - - :name: :design_code
      :col: 145
      :p_name: lehenga_blouse_size_combo_42_design_code
      :format: mirraw
    - :name: :quantity
      :col: 146
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_42_quantity
      :format: mirraw
    - :name: :price
      :col: 147
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_42_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 42'
      :mapping: :combo_kurta_size
  - - :name: :design_code
      :col: 148
      :p_name: lehenga_blouse_size_combo_44_design_code
      :format: mirraw
    - :name: :quantity
      :col: 149
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_44_quantity
      :format: mirraw
    - :name: :price
      :col: 150
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_44_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 44'
      :mapping: :combo_kurta_size
  - - :name: :design_code
      :col: 151
      :p_name: lehenga_blouse_size_combo_46_design_code
      :format: mirraw
    - :name: :quantity
      :col: 152
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_46_quantity
      :format: mirraw
    - :name: :price
      :col: 153
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_46_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 46'
      :mapping: :combo_kurta_size
  - - :name: :design_code
      :col: 154
      :p_name: lehenga_blouse_size_combo_48_design_code
      :format: mirraw
    - :name: :quantity
      :col: 155
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_48_quantity
      :format: mirraw
    - :name: :price
      :col: 156
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_48_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 48'
      :mapping: :combo_kurta_size
  - - :name: :design_code
      :col: 157
      :p_name: lehenga_blouse_size_combo_50_design_code
      :format: mirraw
    - :name: :quantity
      :col: 158
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_50_quantity
      :format: mirraw
    - :name: :price
      :col: 159
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_50_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 50'
      :mapping: :combo_kurta_size
  - - :name: :design_code
      :col: 160
      :p_name: lehenga_blouse_size_combo_38_design_code
      :format: mirraw
    - :name: :quantity
      :col: 161
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_38_quantity
      :format: mirraw
    - :name: :price
      :col: 162
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_combo_38_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 38'
      :mapping: :combo_kurta_size

- :variants:
  - - :name: :design_code
      :col: 61
      :p_name: lehenga_blouse_size_32_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 62
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_32_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 63
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_32_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '32'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 64
      :p_name: lehenga_blouse_size_34_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 65
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_34_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 66
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_34_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '34'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 67
      :p_name: lehenga_blouse_size_36_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 68
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_36_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 69
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_36_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '36'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 70
      :p_name: lehenga_blouse_size_38_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 71
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_38_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 72
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_38_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '38'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 73
      :p_name: lehenga_blouse_size_40_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 74
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_40_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 75
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_40_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '40'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 76
      :p_name: lehenga_blouse_size_42_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 77
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_42_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 78
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_42_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '42'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 79
      :p_name: lehenga_blouse_size_44_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 80
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_44_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 81
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_44_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '44'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 82
      :p_name: lehenga_blouse_size_46_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 83
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_46_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 84
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_46_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '46'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 85
      :p_name: lehenga_blouse_size_48_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 86
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_48_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 87
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_48_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '48'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 88
      :p_name: lehenga_blouse_size_50_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 89
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_50_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 90
      :operation:
      - :to_i
      :p_name: lehenga_blouse_size_50_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '50'
      :mapping: :lehenga_blouse_size
  - - :name: :design_code
      :col: 91
      :p_name: kids_clothing_size_3_to_6_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 92
      :operation:
      - :to_i
      :p_name: kids_clothing_size_3_to_6_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 93
      :operation:
      - :to_i
      :p_name: kids_clothing_size_3_to_6_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3_to_6_months'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 94
      :p_name: kids_clothing_size_6_to_12_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 95
      :operation:
      - :to_i
      :p_name: kids_clothing_size_6_to_12_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 96
      :operation:
      - :to_i
      :p_name: kids_clothing_size_6_to_12_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6_to_12_months'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 97
      :p_name: kids_clothing_size_12_to_18_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 98
      :operation:
      - :to_i
      :p_name: kids_clothing_size_12_to_18_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 99
      :operation:
      - :to_i
      :p_name: kids_clothing_size_12_to_18_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '12_to_18_months'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 100
      :p_name: kids_clothing_size_18_to_24_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 101
      :operation:
      - :to_i
      :p_name: kids_clothing_size_18_to_24_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 102
      :operation:
      - :to_i
      :p_name: kids_clothing_size_18_to_24_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '18_to_24_months'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 103
      :p_name: kids_clothing_size_1_to_2_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 104
      :operation:
      - :to_i
      :p_name: kids_clothing_size_1_to_2_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 105
      :operation:
      - :to_i
      :p_name: kids_clothing_size_1_to_2_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '1_to_2_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 106
      :p_name: kids_clothing_size_2_to_3_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 107
      :operation:
      - :to_i
      :p_name: kids_clothing_size_2_to_3_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 108
      :operation:
      - :to_i
      :p_name: kids_clothing_size_2_to_3_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '2_to_3_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 109
      :p_name: kids_clothing_size_3_to_4_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 110
      :operation:
      - :to_i
      :p_name: kids_clothing_size_3_to_4_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 111
      :operation:
      - :to_i
      :p_name: kids_clothing_size_3_to_4_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3_to_4_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 112
      :p_name: kids_clothing_size_4_to_5_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 113
      :operation:
      - :to_i
      :p_name: kids_clothing_size_4_to_5_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 114
      :operation:
      - :to_i
      :p_name: kids_clothing_size_4_to_5_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4_to_5_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 115
      :p_name: kids_clothing_size_5_to_6_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 116
      :operation:
      - :to_i
      :p_name: kids_clothing_size_5_to_6_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 117
      :operation:
      - :to_i
      :p_name: kids_clothing_size_5_to_6_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5_to_6_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 118
      :p_name: kids_clothing_size_6_to_7_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 119
      :operation:
      - :to_i
      :p_name: kids_clothing_size_6_to_7_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 120
      :operation:
      - :to_i
      :p_name: kids_clothing_size_6_to_7_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6_to_7_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 121
      :p_name: kids_clothing_size_7_to_8_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 122
      :operation:
      - :to_i
      :p_name: kids_clothing_size_7_to_8_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 123
      :operation:
      - :to_i
      :p_name: kids_clothing_size_7_to_8_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '7_to_8_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 124
      :p_name: kids_clothing_size_8_to_9_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 125
      :operation:
      - :to_i
      :p_name: kids_clothing_size_8_to_9_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 126
      :operation:
      - :to_i
      :p_name: kids_clothing_size_8_to_9_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '8_to_9_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 127
      :p_name: kids_clothing_size_9_to_10_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 128
      :operation:
      - :to_i
      :p_name: kids_clothing_size_9_to_10_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 129
      :operation:
      - :to_i
      :p_name: kids_clothing_size_9_to_10_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '9_to_10_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 130
      :p_name: kids_clothing_size_10_to_11_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 131
      :operation:
      - :to_i
      :p_name: kids_clothing_size_10_to_11_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 132
      :operation:
      - :to_i
      :p_name: kids_clothing_size_10_to_11_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '10_to_11_years'
      :mapping: :kids_clothing_size
  - - :name: :design_id
      :col: 5
      :p_name: parent_sku
      :value_if: :is_stitched
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 6
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 7
      :p_name: size
      :value_if: :is_stitched
      :child: true
      :skip: true
      :myntra_p_name: Standard Size
:Islamic:
- :extra:
    :url: https://www.dropbox.com/s/7t460xlmnkq9yee/Bulk%20Upload%20Islamic.xlsx?dl=1
    :amazon_url: https://www.dropbox.com/scl/fi/mq4nsprs75k3t3m1enntd/BulkUpload-Islamic-Sheet.xlsx?rlkey=m79zqqgbqr7yg6tefplo67cnn&st=h1oxfaa7&dl=1
    :designable: "Islamic"
    :product_type: "islamic"
    :option_type: "size"
- :name: :gst_rate
  :col: 37
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 38
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
- :name: :video_link
  :col: 25
  :p_name: video_link
- :name: :dynamic_size_chart
  :col: 26
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :title
  :col: 1
  :present: true
  :operation:
  - :humanize 
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :embellish
  :col: 35
  :p_name: embellish
- :name: :description
  :col: 30
  :operation:
  - :strip
  :p_name: description
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
  :skip: true
- :name: :price
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 13
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 27
  :operation:
  - :strip
  :p_name: tag_list
- :name: :pattern
  :col: 34
  :p_name: pattern
- :name: :region
  :col: 33
  :p_name: region
- :name: :designer_collection_id
  :col: 36
  :mapping: :collection
  :p_name:  collection
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :fabric
  :col: 14
- :name: :work
  :col: 17
- :name: :color
  :col: 15
- :name: :islamic_category
  :col: 2
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Islamic"
- :name: :is_stitched
  :col: 19
  :set_variable:
    :name: is_stitched
    :value: "->(val){return val.downcase == 'stitched'}"
- :name: :is_not_sleeve_category
  :col: 2
  :set_variable:
    :name: is_not_sleeve_category
    :value: "->(val){return ['hijab', 'islamic-accessories', 'islamic-pants', 'islamic-skirts', 'janamaz'].exclude?(val)}"
- :delay_images:
  - - :name: :url
      :col: 20
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 21
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 22
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 23
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 24
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
- :delay_designables:
  - - :name: :length
      :col: 11
      :p_name: length
      :present: true
    - :name: :width
      :col: 12
      :p_name: width
    - :name: :kind
      :value:
      - Islamic
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :islamic_clothing
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :fabric
      :col: 14
      :p_name: fabric
      :present: true
  - - :name: :property_value_id
      :mapping: :work
      :col: 17
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :type
      :col: 18
      :p_name: type
  - - :name: :property_value_id
      :mapping: :color
      :col: 15
      :present: true
      :p_name: color
  - - :name: :property_value_id
      :mapping: :color
      :col: 16
      :p_name: color_optional
  - - :name: :property_value_id
      :mapping: :stitching
      :col: 19
      :present: true
      :p_name: stitching
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 28
      :p_name: occasion
  - - :name: :property_value_id
      :mapping: :look
      :col: 29
      :p_name: look
  - - :name: :property_value_id
      :mapping: :sleeve
      :col: 31
      :present: :is_not_sleeve_category
      :p_name: sleeve
  - - :name: :property_value_id
      :mapping: :neck_style
      :col: 32
      :p_name: neck_style
- :variants:
  - - :name: :design_code
      :col: 39
      :p_name: size_extra_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 40
      :operation:
      - :to_i
      :p_name: size_extra_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 41
      :operation:
      - :to_i
      :p_name: size_extra_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraSmall'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 42
      :p_name: size_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 43
      :operation:
      - :to_i
      :p_name: size_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 44
      :operation:
      - :to_i
      :p_name: size_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraSmall'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 45
      :p_name: size_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 46
      :operation:
      - :to_i
      :p_name: size_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 47
      :operation:
      - :to_i
      :p_name: size_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Small'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 48
      :p_name: size_medium_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 49
      :operation:
      - :to_i
      :p_name: size_medium_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 50
      :operation:
      - :to_i
      :p_name: size_medium_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Medium'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 51
      :p_name: size_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 52
      :operation:
      - :to_i
      :p_name: size_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 53
      :operation:
      - :to_i
      :p_name: size_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Large'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 54
      :p_name: size_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 55
      :operation:
      - :to_i
      :p_name: size_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 56
      :operation:
      - :to_i
      :p_name: size_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraLarge'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 57
      :p_name: size_extra_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 58
      :operation:
      - :to_i
      :p_name: size_extra_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 59
      :operation:
      - :to_i
      :p_name: size_extra_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraLarge'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 60
      :p_name: size_3xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 61
      :operation:
      - :to_i
      :p_name: size_3xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 62
      :operation:
      - :to_i
      :p_name: size_3xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3XL'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 63
      :p_name: size_4xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 64
      :operation:
      - :to_i
      :p_name: size_4xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 65
      :operation:
      - :to_i
      :p_name: size_4xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4XL'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 66
      :p_name: size_5xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 67
      :operation:
      - :to_i
      :p_name: size_5xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 68
      :operation:
      - :to_i
      :p_name: size_5xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5XL'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 69
      :p_name: size_6xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 70
      :operation:
      - :to_i
      :p_name: size_6xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 71
      :operation:
      - :to_i
      :p_name: size_6xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6XL'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 72
      :p_name: size_7xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 73
      :operation:
      - :to_i
      :p_name: size_7xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 74
      :operation:
      - :to_i
      :p_name: size_7xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '7XL'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 75
      :p_name: size_free_size_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 76
      :operation:
      - :to_i
      :p_name: size_free_size_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 77
      :operation:
      - :to_i
      :p_name: size_free_size_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'FreeSize'
      :mapping: :size
      :value_if: :is_stitched
  - - :name: :parent_sku
      :col: 4
      :p_name: parent_sku
      :value_if: :is_stitched
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 6
      :p_name: size
      :value_if: :is_stitched
      :child: true
      :skip: true
:Footwear:
- :extra:
    :url: https://www.dropbox.com/s/jvm8kx6en3imc6o/Bulk%20Upload%20Footwear.xlsx?dl=1
    :amazon_url: https://www.dropbox.com/scl/fi/1tmfebo1wlu2j6iac409g/Footwear-sheet-size-chart.xlsx?rlkey=5o68ijkses8n2h019c1badliy&st=use7x9h4&dl=1
    :designable: "Other"
    :product_type: "other"
    :option_type: "footwear_size"
- :name: :gst_rate
  :col: 30
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 31
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
- :name: :video_link
  :col: 22
  :p_name: video_link
- :name: :title
  :col: 1
  :present: true
  :operation:
  - :humanize 
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :dynamic_size_chart
  :col: 23
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :description
  :col: 24
  :operation:
  - :strip
  :p_name: description
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
  :skip: true
- :name: :price
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 13
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :designer_collection_id
  :col: 29
  :mapping: :collection
  :p_name:  collection
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :color
  :col: 15
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Other"
- :name: :is_heel_flat
  :col: 2
  :set_variable:
    :name: is_heel_flat
    :value: "->(val){return ['flats', 'heels'].include?(val)}"
- :name: :is_footwear
  :col: 0
  :set_variable:
    :name: is_footwear
    :value: "->(val){return true}"
- :delay_images:
  - - :name: :url
      :col: 17
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 18
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 19
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 20
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 21
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
- :delay_designables:
  - - :name: :kind
      :value:
      - Other
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :footwear
      :col: 2
      :p_name: category
    - :name: :category_heel
      :mapping: :heels
      :col: 2
      :p_name: category
      :set_variable:
        :name: is_heel
        :value: "->(val){return val.present?}"
    - :name: :category_id
      :mapping: :premium_footwear
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :men_footwear
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :eid_footwear
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :material
      :col: 14
      :p_name: material
      :present: true
  - - :name: :property_value_id
      :mapping: :work
      :col: 16
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :footwear_type
      :col: 11
      :p_name: footwear_type
  - - :name: :property_value_id
      :mapping: :color
      :col: 15
      :present: true
      :p_name: color
  - - :name: :property_value_id
      :mapping: :heel_height
      :col: 25
      :present: :is_heel
      :value_if: :is_heel
      :p_name: heel_height_in_inches
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 12
      :p_name: occasion
  - - :name: :property_value_id
      :mapping: :heel_type
      :col: 26
      :present: :is_heel
      :value_if: :is_heel
      :p_name: heel_type
  - - :name: :property_value_id
      :mapping: :ankle_height
      :col: 27
      :present: :is_heel_flat
      :value_if: :is_heel_flat
      :p_name: ankle_height
  - - :name: :property_value_id
      :mapping: :toe_shape
      :col: 28
      :present: :is_heel_flat
      :value_if: :is_heel_flat
      :p_name: toe_shape
- :variants:
  - - :name: :design_code
      :col: 32
      :p_name: footwear_size_1_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 33
      :operation:
      - :to_i
      :p_name: footwear_size_1_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 34
      :operation:
      - :to_i
      :p_name: footwear_size_1_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '1'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 35
      :p_name: footwear_size_1_5_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 36
      :operation:
      - :to_i
      :p_name: footwear_size_1_5_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 37
      :operation:
      - :to_i
      :p_name: footwear_size_1_5_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '1.5'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 38
      :p_name: footwear_size_2_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 39
      :operation:
      - :to_i
      :p_name: footwear_size_2_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 40
      :operation:
      - :to_i
      :p_name: footwear_size_2_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '2'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 41
      :p_name: footwear_size_3_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 42
      :operation:
      - :to_i
      :p_name: footwear_size_3_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 43
      :operation:
      - :to_i
      :p_name: footwear_size_3_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 44
      :p_name: footwear_size_4_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 45
      :operation:
      - :to_i
      :p_name: footwear_size_4_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 46
      :operation:
      - :to_i
      :p_name: footwear_size_4_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 47
      :p_name: footwear_size_5_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 48
      :operation:
      - :to_i
      :p_name: footwear_size_5_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 49
      :operation:
      - :to_i
      :p_name: footwear_size_5_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 50
      :p_name: footwear_size_6_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 51
      :operation:
      - :to_i
      :p_name: footwear_size_6_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 52
      :operation:
      - :to_i
      :p_name: footwear_size_6_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 53
      :p_name: footwear_size_7_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 54
      :operation:
      - :to_i
      :p_name: footwear_size_7_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 55
      :operation:
      - :to_i
      :p_name: footwear_size_7_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '7'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 56
      :p_name: footwear_size_8_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 57
      :operation:
      - :to_i
      :p_name: footwear_size_8_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 58
      :operation:
      - :to_i
      :p_name: footwear_size_8_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '8'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 59
      :p_name: footwear_size_9_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 60
      :operation:
      - :to_i
      :p_name: footwear_size_9_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 61
      :operation:
      - :to_i
      :p_name: footwear_size_9_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '9'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 62
      :p_name: footwear_size_10_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 63
      :operation:
      - :to_i
      :p_name: footwear_size_10_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 64
      :operation:
      - :to_i
      :p_name: footwear_size_10_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '10'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 65
      :p_name: footwear_size_11_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 66
      :operation:
      - :to_i
      :p_name: footwear_size_11_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 67
      :operation:
      - :to_i
      :p_name: footwear_size_11_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '11'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :design_code
      :col: 68
      :p_name: footwear_size_12_design_code
      :value_if: :is_footwear
      :format: mirraw
    - :name: :quantity
      :col: 69
      :operation:
      - :to_i
      :p_name: footwear_size_12_quantity
      :value_if: :is_footwear
      :format: mirraw
    - :name: :price
      :col: 70
      :operation:
      - :to_i
      :p_name: footwear_size_12_price
      :value_if: :is_footwear
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '12'
      :mapping: :footwear_size
      :value_if: :is_footwear
  - - :name: :parent_sku
      :col: 4
      :p_name: parent_sku
      :value_if: :is_footwear
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 6
      :p_name: size
      :value_if: :is_footwear
      :child: true
      :skip: true
:Bag:
- :extra:
    :url: https://www.dropbox.com/scl/fi/kix2k4nj4234tf8eggikm/new_bag_sheet.xlsx?rlkey=7k5nbly9afn0tynw8g6kva2dp&st=bck5qpmn&dl=1
    :designable: "Bag"
    :product_type: "bag"
- :name: :gst_rate
  :col: 20
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 21
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :description
  :col: 18
  :operation:
  - :strip
  :p_name: description
- :name: :video_link
  :col: 14
  :p_name: video_link
- :name: :package_details
  :col: 3
  :present: true
  :p_name: package details
- :name: :price
  :col: 16
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
- :name: :discount_percent
  :col: 17
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 4
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :quantity
  :col: 15
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
- :name: :designer_collection_id
  :col: 19
  :mapping: :collection
  :p_name:  collection
- :name: :material
  :col: 5
  :present: true
- :name: :category
  :col: 2
  :p_name: category
- :name: :color
  :col: 6
  :present: true
  :p_name: color
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Bag"
- :delay_images:
  - - :name: :url
      :col: 9
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 10
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 11
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 12
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 13
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
- :delay_designables:
  - - :name: :width
      :col: 8
      :present: true
      :p_name: width
    - :name: :length
      :col: 7
      :present: true
      :p_name: length
    - :name: :kind
      :value:
      - Bag
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :bags
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :material
      :col: 5
      :p_name: material
      :present: true
  - - :name: :property_value_id
      :mapping: :color
      :col: 6
      :p_name: color
      :present: true
:WesternWear:
- :extra:
    :amazon_url: https://www.dropbox.com/scl/fi/b7n7zr4n2pzrm7ekc44ry/BulkUpload-WesternWear-sheet-size-chart.xlsx?rlkey=fgkoxehfkwdq7umg0purizp1c&st=ebreani5&dl=1
    :designable: "Other"
    :product_type: "other"
- :name: :gst_rate
  :col: 34
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
  :myntra_p_name: GTIN
- :name: :hsn_code
  :col: 35
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
  :myntra_p_name: HSN
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
  :myntra_p_name: vendorSkuCode
- :name: :video_link
  :col: 25
  :p_name: video_link
- :name: dynamic_size_chart
  :col: 26
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
  :myntra_p_name: productDisplayName
- :name: :title
  :operation:
  - :strip
- :name: :description
  :col: 29
  :operation:
  - :strip
  :p_name: description
  :myntra_p_name: sizeAndFitDescription
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
  :myntra_p_name: Package Contains
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
  :skip: true
- :name: :price
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
  :myntra_p_name: MRP
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 13
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 27
  :operation:
  - :strip
  :p_name: tag_list
  :myntra_p_name: tags
- :name: :designer_collection_id
  :col: 33
  :mapping: :collection
  :p_name:  collection
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :fabric
  :col: 14
  :myntra_p_name: Fabric
- :name: :work
  :col: 17
  :myntra_p_name: Pattern
- :name: :color
  :col: 15
  :myntra_p_name: Prominent Colour
- :name: :other_category
  :col: 2
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Other"
- :name: :is_stitched
  :col: 19
  :set_variable:
    :name: is_stitched
    :value: "->(val){return val.downcase == 'stitched'}"
- :delay_images:
  - - :name: :url
      :col: 20
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
      :myntra_p_name: Front Image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 21
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
      :myntra_p_name: Side Image
  - - :name: :url
      :col: 22
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
      :myntra_p_name: Back Image
  - - :name: :url
      :col: 23
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
      :myntra_p_name: Detail Angle
  - - :name: :url
      :col: 24
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
      :myntra_p_name: Look Shot Image
- :delay_designables:
  - - :name: :length
      :col: 11
      :p_name: length
      :present: true
      :myntra_p_name: Length
    - :name: :kind
      :value:
      - Other
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :western_wear
      :col: 2
      :p_name: category
      :myntra_p_name: articleType
    - :name: :category_id
      :mapping: :plus_size_tops
      :col: 2
      :p_name: category
      :myntra_p_name: articleType
    - :name: category_id
      :mapping: :apparel
      :col: 2
      :p_name: category   
      :myntra_p_name: articleType 
    - :name: :category_shirt
      :mapping: :women_shirts
      :col: 2
      :p_name: category
      :set_variable:
        :name: is_shirt
        :value: "->(val){return val.present?}"
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :length
      :col: 11
      :p_name: length
      :myntra_p_name: Length
  - - :name: :property_value_id
      :mapping: :style
      :col: 12
      :p_name: style
  - - :name: :property_value_id
      :mapping: :fabric
      :col: 14
      :p_name: fabric
      :present: true
      :myntra_p_name: Fabric
  - - :name: :property_value_id
      :mapping: :work
      :col: 17
      :p_name: work
      :present: true
      :myntra_p_name: Pattern
  - - :name: :property_value_id
      :mapping: :type
      :col: 18
      :p_name: type
      :myntra_p_name: Print or Pattern Type
  - - :name: :property_value_id
      :mapping: :color
      :col: 15
      :present: true
      :p_name: color
      :myntra_p_name: Prominent Colour
  - - :name: :property_value_id
      :mapping: :color
      :col: 16
      :p_name: color_optional
      :myntra_p_name: Second Prominent Colour
  - - :name: :property_value_id
      :mapping: :stitching
      :col: 19
      :present: true
      :p_name: stitching
      :myntra_p_name: Stitch
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 28
      :p_name: occasion
      :myntra_p_name: Occasion
  - - :name: :property_value_id
      :mapping: :collar
      :present: :is_shirt
      :value_if: :is_shirt
      :col: 32
      :p_name: collar
  - - :name: :property_value_id
      :mapping: :sleeve
      :col: 30
      :present: true
      :p_name: sleeve
      :myntra_p_name: Sleeve Length
  - - :name: :property_value_id
      :mapping: :neck_style
      :col: 31
      :p_name: neck_style
      :myntra_p_name: Neck
- :variants:
  - - :name: :design_code
      :col: 36
      :p_name: tops_size_extra_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 37
      :operation:
      - :to_i
      :p_name: tops_size_extra_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 38
      :operation:
      - :to_i
      :p_name: tops_size_extra_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraSmall'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 39
      :p_name: tops_size_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 40
      :operation:
      - :to_i
      :p_name: tops_size_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 41
      :operation:
      - :to_i
      :p_name: tops_size_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraSmall'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 42
      :p_name: tops_size_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 43
      :operation:
      - :to_i
      :p_name: tops_size_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 44
      :operation:
      - :to_i
      :p_name: tops_size_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Small'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 45
      :p_name: tops_size_medium_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 46
      :operation:
      - :to_i
      :p_name: tops_size_medium_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 47
      :operation:
      - :to_i
      :p_name: tops_size_medium_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Medium'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 48
      :p_name: tops_size_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 49
      :operation:
      - :to_i
      :p_name: tops_size_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 50
      :operation:
      - :to_i
      :p_name: tops_size_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Large'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 51
      :p_name: tops_size_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 52
      :operation:
      - :to_i
      :p_name: tops_size_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 53
      :operation:
      - :to_i
      :p_name: tops_size_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraLarge'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 54
      :p_name: tops_size_extra_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 55
      :operation:
      - :to_i
      :p_name: tops_size_extra_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 56
      :operation:
      - :to_i
      :p_name: tops_size_extra_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraLarge'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 57
      :p_name: tops_size_3xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 58
      :operation:
      - :to_i
      :p_name: tops_size_3xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 59
      :operation:
      - :to_i
      :p_name: tops_size_3xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3XL'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 60
      :p_name: tops_size_4xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 61
      :operation:
      - :to_i
      :p_name: tops_size_4xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 62
      :operation:
      - :to_i
      :p_name: tops_size_4xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4XL'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 63
      :p_name: tops_size_5xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 64
      :operation:
      - :to_i
      :p_name: tops_size_5xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 65
      :operation:
      - :to_i
      :p_name: tops_size_5xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5XL'
      :mapping: :tops_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 66
      :p_name: tshirt_size_extra_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 67
      :operation:
      - :to_i
      :p_name: tshirt_size_extra_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 68
      :operation:
      - :to_i
      :p_name: tshirt_size_extra_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraSmall'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 69
      :p_name: tshirt_size_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 70
      :operation:
      - :to_i
      :p_name: tshirt_size_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 71
      :operation:
      - :to_i
      :p_name: tshirt_size_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraSmall'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 72
      :p_name: tshirt_size_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 73
      :operation:
      - :to_i
      :p_name: tshirt_size_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 74
      :operation:
      - :to_i
      :p_name: tshirt_size_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Small'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 75
      :p_name: tshirt_size_medium_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 76
      :operation:
      - :to_i
      :p_name: tshirt_size_medium_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 77
      :operation:
      - :to_i
      :p_name: tshirt_size_medium_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Medium'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 78
      :p_name: tshirt_size_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 79
      :operation:
      - :to_i
      :p_name: tshirt_size_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 80
      :operation:
      - :to_i
      :p_name: tshirt_size_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Large'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 81
      :p_name: tshirt_size_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 82
      :operation:
      - :to_i
      :p_name: tshirt_size_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 83
      :operation:
      - :to_i
      :p_name: tshirt_size_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraLarge'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 84
      :p_name: tshirt_size_extra_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 85
      :operation:
      - :to_i
      :p_name: tshirt_size_extra_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 86
      :operation:
      - :to_i
      :p_name: tshirt_size_extra_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraLarge'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 87
      :p_name: tshirt_size_3xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 88
      :operation:
      - :to_i
      :p_name: tshirt_size_3xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 89
      :operation:
      - :to_i
      :p_name: tshirt_size_3xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3XL'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 90
      :p_name: tshirt_size_4xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 91
      :operation:
      - :to_i
      :p_name: tshirt_size_4xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 92
      :operation:
      - :to_i
      :p_name: tshirt_size_4xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4XL'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 93
      :p_name: tshirt_size_5xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 94
      :operation:
      - :to_i
      :p_name: tshirt_size_5xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 95
      :operation:
      - :to_i
      :p_name: tshirt_size_5xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5XL'
      :mapping: :tshirt_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 96
      :p_name: dresses_size_extra_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 97
      :operation:
      - :to_i
      :p_name: dresses_size_extra_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 98
      :operation:
      - :to_i
      :p_name: dresses_size_extra_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraSmall'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 99
      :p_name: dresses_size_extra_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 100
      :operation:
      - :to_i
      :p_name: dresses_size_extra_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 101
      :operation:
      - :to_i
      :p_name: dresses_size_extra_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraSmall'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 102
      :p_name: dresses_size_small_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 103
      :operation:
      - :to_i
      :p_name: dresses_size_small_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 104
      :operation:
      - :to_i
      :p_name: dresses_size_small_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Small'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 105
      :p_name: dresses_size_medium_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 106
      :operation:
      - :to_i
      :p_name: dresses_size_medium_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 107
      :operation:
      - :to_i
      :p_name: dresses_size_medium_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Medium'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 108
      :p_name: dresses_size_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 109
      :operation:
      - :to_i
      :p_name: dresses_size_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 110
      :operation:
      - :to_i
      :p_name: dresses_size_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Large'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 111
      :p_name: dresses_size_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 112
      :operation:
      - :to_i
      :p_name: dresses_size_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 113
      :operation:
      - :to_i
      :p_name: dresses_size_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraLarge'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 114
      :p_name: dresses_size_extra_extra_large_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 115
      :operation:
      - :to_i
      :p_name: dresses_size_extra_extra_large_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 116
      :operation:
      - :to_i
      :p_name: dresses_size_extra_extra_large_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'ExtraExtraLarge'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 117
      :p_name: dresses_size_3xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 118
      :operation:
      - :to_i
      :p_name: dresses_size_3xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 119
      :operation:
      - :to_i
      :p_name: dresses_size_3xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3XL'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 120
      :p_name: dresses_size_4xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 121
      :operation:
      - :to_i
      :p_name: dresses_size_4xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 122
      :operation:
      - :to_i
      :p_name: dresses_size_4xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4XL'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 123
      :p_name: dresses_size_5xl_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 124
      :operation:
      - :to_i
      :p_name: dresses_size_5xl_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 125
      :operation:
      - :to_i
      :p_name: dresses_size_5xl_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5XL'
      :mapping: :dresses_size
      :value_if: :is_stitched
  - - :name: :design_id
      :col: 4
      :p_name: parent_sku
      :value_if: :is_stitched
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 6
      :p_name: size
      :value_if: :is_stitched
      :child: true
      :skip: true
      :myntra_p_name: Standard Size
:BottomWear:
- :extra:
    :amazon_url: https://www.dropbox.com/scl/fi/2h3u0ucd9badwyv95p8ap/BulkUpload-BottomWear-Sheet.xlsx?rlkey=3a5n0v87a9rdiph9a6pn17oym&e=1&st=sk2e0j3o&dl=1
    :designable: "Other"
    :product_type: "other"
    :option_type: "bottom_size"
- :name: :gst_rate
  :col: 32
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 33
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :description
  :col: 29
  :operation:
  - :strip
  :p_name: description
- :name: :video_link
  :col: 25
  :p_name: video_link
- :name: :dynamic_size_chart
  :col: 26
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
  :skip: true
- :name: :price
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 13
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 27
  :operation:
  - :strip
  :p_name: tag_list
- :name: :designer_collection_id
  :col: 31
  :mapping: :collection
  :p_name:  collection
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :fabric
  :col: 14
- :name: :work
  :col: 17
- :name: :color
  :col: 15
- :name: :other_category
  :col: 2
- :name: :published
  :value:
  - true
- :name: :color
  :value:
  - :color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Other"
- :name: :is_stitched
  :value:
  - 'stitched'
  :set_variable:
    :name: is_stitched
    :value: "->(val){return val.downcase == 'stitched'}"
- :delay_images:
  - - :name: :url
      :col: 20
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 21
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 22
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 23
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 24
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
- :delay_designables:
  - - :name: :length
      :col: 11
      :p_name: length
      :present: true
    - :name: :kind
      :value:
      - Other
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :bottoms
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :men_bottoms
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :women_pants
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :length
      :col: 11
      :p_name: length
  - - :name: :property_value_id
      :mapping: :style
      :col: 12
      :p_name: style
  - - :name: :property_value_id
      :mapping: :fabric
      :col: 14
      :p_name: fabric
      :present: true
  - - :name: :property_value_id
      :mapping: :work
      :col: 17
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :type
      :col: 18
      :p_name: type
  - - :name: :property_value_id
      :mapping: :color
      :col: 15
      :present: true
      :p_name: color
  - - :name: :property_value_id
      :mapping: :color
      :col: 16
      :p_name: color_optional
  - - :name: :property_value_id
      :mapping: :stitching
      :col: 19
      :present: true
      :p_name: stitching
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 28
      :p_name: occasion
  - - :name: :property_value_id
      :mapping: :waist_rise
      :col: 30
      :present: true
      :p_name: waist_rise
- :variants:
  - - :name: :design_code
      :col: 34
      :p_name: bottom_size_24_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 35
      :operation:
      - :to_i
      :p_name: bottom_size_24_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 36
      :operation:
      - :to_i
      :p_name: bottom_size_24_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '24'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 37
      :p_name: bottom_size_26_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 38
      :operation:
      - :to_i
      :p_name: bottom_size_26_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 39
      :operation:
      - :to_i
      :p_name: bottom_size_26_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '26'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 40
      :p_name: bottom_size_28_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 41
      :operation:
      - :to_i
      :p_name: bottom_size_28_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 42
      :operation:
      - :to_i
      :p_name: bottom_size_28_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '28'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 43
      :p_name: bottom_size_30_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 44
      :operation:
      - :to_i
      :p_name: bottom_size_30_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 45
      :operation:
      - :to_i
      :p_name: bottom_size_30_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '30'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 46
      :p_name: bottom_size_32_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 47
      :operation:
      - :to_i
      :p_name: bottom_size_32_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 48
      :operation:
      - :to_i
      :p_name: bottom_size_32_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '32'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 49
      :p_name: bottom_size_34_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 50
      :operation:
      - :to_i
      :p_name: bottom_size_34_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 51
      :operation:
      - :to_i
      :p_name: bottom_size_34_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '34'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 52
      :p_name: bottom_size_36_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 53
      :operation:
      - :to_i
      :p_name: bottom_size_36_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 54
      :operation:
      - :to_i
      :p_name: bottom_size_36_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '36'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 55
      :p_name: bottom_size_38_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 56
      :operation:
      - :to_i
      :p_name: bottom_size_38_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 57
      :operation:
      - :to_i
      :p_name: bottom_size_38_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '38'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 58
      :p_name: bottom_size_40_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 59
      :operation:
      - :to_i
      :p_name: bottom_size_40_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 60
      :operation:
      - :to_i
      :p_name: bottom_size_40_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '40'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 61
      :p_name: bottom_size_42_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 62
      :operation:
      - :to_i
      :p_name: bottom_size_42_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 63
      :operation:
      - :to_i
      :p_name: bottom_size_42_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '42'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 64
      :p_name: bottom_size_free_size_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 65
      :operation:
      - :to_i
      :p_name: bottom_size_free_size_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 66
      :operation:
      - :to_i
      :p_name: bottom_size_free_size_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'FreeSize'
      :mapping: :bottom_size
      :value_if: :is_stitched
  - - :name: :design_id
      :col: 4
      :p_name: parent_sku
      :value_if: :is_stitched
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 6
      :p_name: size
      :value_if: :is_stitched
      :child: true
      :skip: true
:Kid:
- :extra:
    :url: https://www.dropbox.com/s/f4ogzp1qb91el6l/Bulk%20Upload%20Kid%20old%40.xlsx?dl=0
    :amazon_url: https://www.dropbox.com/scl/fi/4p4iq21c43vozq0m6uxj5/BulkUpload-Kid-sheet.xlsx?rlkey=5tug6o36q42x3kbpm3zhyvp9k&st=f3412u2u&dl=1
    :designable: "Kid"
    :product_type: "kid"
- :name: :gst_rate
  :col: 43
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 44
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :embellish
  :col: 41
  :p_name: embellish
- :name: :video_link
  :col: 31
  :p_name: video_link
- :name: :dynamic_size_chart
  :col: 32
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :description
  :col: 36
  :operation:
  - :strip
  :p_name: description
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
  :skip: true
- :name: :price
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 11
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 33
  :operation:
  - :strip
  :p_name: tag_list
- :name: :pattern
  :col: 40
  :p_name: pattern
- :name: :accessories
  :col: 22
  :p_name: accessories
- :name: :region
  :col: 39
  :p_name: region
- :name: :designer_collection_id
  :col: 42
  :mapping: :collection
  :p_name:  collection
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :kid_fabric
  :col: 12
- :name: :work
  :col: 23
- :name: :kid_color
  :col: 14
- :name: :kid_category
  :col: 2
- :name: :published
  :value:
  - true
- :name: :kid_color
  :value:
  - :kid_color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Kid"
- :name: :is_stitched
  :col: 25
  :set_variable:
    :name: is_stitched
    :value: "->(val){return val.downcase == 'stitched'}"
- :name: :is_not_skirt
  :col: 2
  :set_variable:
    :name: is_not_skirt
    :value: "->(val){return val != 'kids-skirts'}"
- :delay_images:
  - - :name: :url
      :col: 26
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 27
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 28
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 29
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 30
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
- :delay_designables:
  - - :name: :chunari_length
      :col: 21
      :p_name: chunari_length
    - :name: :kind
      :value:
      - Kid
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :kids
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :kids_pavadai_set
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :boys_shirts
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :top_fabric
      :col: 12
      :p_name: top_fabric
      :present: :is_not_skirt
  - - :name: :property_value_id
      :mapping: :top_fabric
      :col: 13
      :p_name: top_fabric_optional
  - - :name: :property_value_id
      :mapping: :work
      :col: 23
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :type
      :col: 24
      :p_name: type
  - - :name: :property_value_id
      :mapping: :top_color
      :col: 14
      :present: :is_not_skirt
      :p_name: top_color
  - - :name: :property_value_id
      :mapping: :top_color
      :col: 15
      :p_name: top_color_optional
  - - :name: :property_value_id
      :mapping: :bottom_style
      :col: 16
      :p_name: bottom_style
  - - :name: :property_value_id
      :mapping: :bottom_color
      :col: 17
      :p_name: bottom_color
  - - :name: :property_value_id
      :mapping: :bottom_fabric
      :col: 18
      :p_name: bottom_fabric
  - - :name: :property_value_id
      :mapping: :chunari_fabric
      :col: 19
      :p_name: chunari_fabric
  - - :name: :property_value_id
      :mapping: :chunari_color
      :col: 20
      :p_name: chunari_color
  - - :name: :property_value_id
      :mapping: :stitching
      :col: 25
      :present: true
      :p_name: stitching
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 34
      :p_name: occasion
  - - :name: :property_value_id
      :mapping: :look
      :col: 35
      :p_name: look
  - - :name: :property_value_id
      :mapping: :sleeve
      :col: 37
      :present: :is_not_skirt
      :p_name: sleeve
  - - :name: :property_value_id
      :mapping: :neck_style
      :col: 38
      :p_name: neck_style
  - - :name: :property_value_id
      :mapping: :gender
      :col: 45
      :p_name: gender
- :combo_variants:
  - - :name: :design_code
      :col: 159
      :p_name: kids_clothing_size_combo_30_design_code
      :format: mirraw
    - :name: :quantity
      :col: 160
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_30_quantity
      :format: mirraw
    - :name: :price
      :col: 161
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_30_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 30'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 162
      :p_name: kids_clothing_size_combo_32_design_code
      :format: mirraw
    - :name: :quantity
      :col: 163
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_32_quantity
      :format: mirraw
    - :name: :price
      :col: 164
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_32_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 32'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 165
      :p_name: kids_clothing_size_combo_34_design_code
      :format: mirraw
    - :name: :quantity
      :col: 166
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_34_quantity
      :format: mirraw
    - :name: :price
      :col: 167
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_34_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 34'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 168
      :p_name: kids_clothing_size_combo_36_design_code
      :format: mirraw
    - :name: :quantity
      :col: 169
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_36_quantity
      :format: mirraw
    - :name: :price
      :col: 170
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_36_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 36'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 171
      :p_name: kids_clothing_size_combo_38_design_code
      :format: mirraw
    - :name: :quantity
      :col: 172
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_38_quantity
      :format: mirraw
    - :name: :price
      :col: 173
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_38_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 38'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 174
      :p_name: kids_clothing_size_combo_40_design_code
      :format: mirraw
    - :name: :quantity
      :col: 175
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_40_quantity
      :format: mirraw
    - :name: :price
      :col: 176
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_40_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 40'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 177
      :p_name: kids_clothing_size_combo_42_design_code
      :format: mirraw
    - :name: :quantity
      :col: 178
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_42_quantity
      :format: mirraw
    - :name: :price
      :col: 179
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_42_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 42'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 180
      :p_name: kids_clothing_size_combo_44_design_code
      :format: mirraw
    - :name: :quantity
      :col: 181
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_44_quantity
      :format: mirraw
    - :name: :price
      :col: 182
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_44_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 44'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 183
      :p_name: kids_clothing_size_combo_46_design_code
      :format: mirraw
    - :name: :quantity
      :col: 184
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_46_quantity
      :format: mirraw
    - :name: :price
      :col: 185
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_46_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 46'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 186
      :p_name: kids_clothing_size_combo_48_design_code
      :format: mirraw
    - :name: :quantity
      :col: 187
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_48_quantity
      :format: mirraw
    - :name: :price
      :col: 188
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_48_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 48'
      :mapping: :combo_kids_clothing_size
  - - :name: :design_code
      :col: 189
      :p_name: kids_clothing_size_combo_50_design_code
      :format: mirraw
    - :name: :quantity
      :col: 190
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_50_quantity
      :format: mirraw
    - :name: :price
      :col: 191
      :operation:
      - :to_i
      :p_name: kids_clothing_size_combo_50_price
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - 'Combo 50'
      :mapping: :combo_kids_clothing_size
- :variants:
  - - :name: :design_code
      :col: 46
      :p_name: kids_clothing_size_3_to_6_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 47
      :operation:
      - :to_i
      :p_name: kids_clothing_size_3_to_6_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 48
      :operation:
      - :to_i
      :p_name: kids_clothing_size_3_to_6_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3_to_6_months'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 49
      :p_name: kids_clothing_size_6_to_12_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 50
      :operation:
      - :to_i
      :p_name: kids_clothing_size_6_to_12_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 51
      :operation:
      - :to_i
      :p_name: kids_clothing_size_6_to_12_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6_to_12_months'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 52
      :p_name: kids_clothing_size_12_to_18_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 53
      :operation:
      - :to_i
      :p_name: kids_clothing_size_12_to_18_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 54
      :operation:
      - :to_i
      :p_name: kids_clothing_size_12_to_18_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '12_to_18_months'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 55
      :p_name: kids_clothing_size_18_to_24_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 56
      :operation:
      - :to_i
      :p_name: kids_clothing_size_18_to_24_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 57
      :operation:
      - :to_i
      :p_name: kids_clothing_size_18_to_24_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '18_to_24_months'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 58
      :p_name: kids_clothing_size_1_to_2_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 59
      :operation:
      - :to_i
      :p_name: kids_clothing_size_1_to_2_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 60
      :operation:
      - :to_i
      :p_name: kids_clothing_size_1_to_2_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '1_to_2_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 61
      :p_name: kids_clothing_size_2_to_3_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 62
      :operation:
      - :to_i
      :p_name: kids_clothing_size_2_to_3_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 63
      :operation:
      - :to_i
      :p_name: kids_clothing_size_2_to_3_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '2_to_3_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 64
      :p_name: kids_clothing_size_3_to_4_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 65
      :operation:
      - :to_i
      :p_name: kids_clothing_size_3_to_4_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 66
      :operation:
      - :to_i
      :p_name: kids_clothing_size_3_to_4_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3_to_4_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 67
      :p_name: kids_clothing_size_4_to_5_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 68
      :operation:
      - :to_i
      :p_name: kids_clothing_size_4_to_5_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 69
      :operation:
      - :to_i
      :p_name: kids_clothing_size_4_to_5_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4_to_5_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 70
      :p_name: kids_clothing_size_5_to_6_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 71
      :operation:
      - :to_i
      :p_name: kids_clothing_size_5_to_6_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 72
      :operation:
      - :to_i
      :p_name: kids_clothing_size_5_to_6_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5_to_6_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 73
      :p_name: kids_clothing_size_6_to_7_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 74
      :operation:
      - :to_i
      :p_name: kids_clothing_size_6_to_7_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 75
      :operation:
      - :to_i
      :p_name: kids_clothing_size_6_to_7_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6_to_7_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 76
      :p_name: kids_clothing_size_7_to_8_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 77
      :operation:
      - :to_i
      :p_name: kids_clothing_size_7_to_8_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 78
      :operation:
      - :to_i
      :p_name: kids_clothing_size_7_to_8_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '7_to_8_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 79
      :p_name: kids_clothing_size_8_to_9_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 80
      :operation:
      - :to_i
      :p_name: kids_clothing_size_8_to_9_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 81
      :operation:
      - :to_i
      :p_name: kids_clothing_size_8_to_9_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '8_to_9_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 82
      :p_name: kids_clothing_size_9_to_10_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 83
      :operation:
      - :to_i
      :p_name: kids_clothing_size_9_to_10_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 84
      :operation:
      - :to_i
      :p_name: kids_clothing_size_9_to_10_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '9_to_10_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 85
      :p_name: kids_clothing_size_10_to_11_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 86
      :operation:
      - :to_i
      :p_name: kids_clothing_size_10_to_11_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 87
      :operation:
      - :to_i
      :p_name: kids_clothing_size_10_to_11_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '10_to_11_years'
      :mapping: :kids_clothing_size
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 88
      :p_name: kids_clothing_size_11_to_12_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 89
      :operation:
      - :to_i
      :p_name: kids_clothing_size_11_to_12_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 90
      :operation:
      - :to_i
      :p_name: kids_clothing_size_11_to_12_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '11_to_12_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 91
      :p_name: kids_clothing_size_12_to_13_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 92
      :operation:
      - :to_i
      :p_name: kids_clothing_size_12_to_13_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 93
      :operation:
      - :to_i
      :p_name: kids_clothing_size_12_to_13_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '12_to_13_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 94
      :p_name: kids_clothing_size_13_to_14_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 95
      :operation:
      - :to_i
      :p_name: kids_clothing_size_13_to_14_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 96
      :operation:
      - :to_i
      :p_name: kids_clothing_size_13_to_14_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '13_to_14_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 97
      :p_name: kids_clothing_size_14_to_15_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 98
      :operation:
      - :to_i
      :p_name: kids_clothing_size_14_to_15_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 99
      :operation:
      - :to_i
      :p_name: kids_clothing_size_14_to_15_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '14_to_15_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 100
      :p_name: kids_clothing_size_15_to_16_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 101
      :operation:
      - :to_i
      :p_name: kids_clothing_size_15_to_16_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 102
      :operation:
      - :to_i
      :p_name: kids_clothing_size_15_to_16_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '15_to_16_years'
      :mapping: :kids_clothing_size
  - - :name: :design_code
      :col: 103
      :p_name: boys_clothing_size_3_to_6_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 104
      :operation:
      - :to_i
      :p_name: boys_clothing_size_3_to_6_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 105
      :operation:
      - :to_i
      :p_name: boys_clothing_size_3_to_6_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3_to_6_months'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 106
      :p_name: boys_clothing_size_6_to_12_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 107
      :operation:
      - :to_i
      :p_name: boys_clothing_size_6_to_12_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 108
      :operation:
      - :to_i
      :p_name: boys_clothing_size_6_to_12_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6_to_12_months'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 109
      :p_name: boys_clothing_size_12_to_18_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 110
      :operation:
      - :to_i
      :p_name: boys_clothing_size_12_to_18_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 111
      :operation:
      - :to_i
      :p_name: boys_clothing_size_12_to_18_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '12_to_18_months'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 112
      :p_name: boys_clothing_size_18_to_24_months_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 113
      :operation:
      - :to_i
      :p_name: boys_clothing_size_18_to_24_months_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 114
      :operation:
      - :to_i
      :p_name: boys_clothing_size_18_to_24_months_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '18_to_24_months'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 115
      :p_name: boys_clothing_size_1_to_2_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 116
      :operation:
      - :to_i
      :p_name: boys_clothing_size_1_to_2_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 117
      :operation:
      - :to_i
      :p_name: boys_clothing_size_1_to_2_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '1_to_2_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 118
      :p_name: boys_clothing_size_2_to_3_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 119
      :operation:
      - :to_i
      :p_name: boys_clothing_size_2_to_3_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 120
      :operation:
      - :to_i
      :p_name: boys_clothing_size_2_to_3_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '2_to_3_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 121
      :p_name: boys_clothing_size_3_to_4_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 122
      :operation:
      - :to_i
      :p_name: boys_clothing_size_3_to_4_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 123
      :operation:
      - :to_i
      :p_name: boys_clothing_size_3_to_4_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '3_to_4_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 124
      :p_name: boys_clothing_size_4_to_5_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 125
      :operation:
      - :to_i
      :p_name: boys_clothing_size_4_to_5_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 126
      :operation:
      - :to_i
      :p_name: boys_clothing_size_4_to_5_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '4_to_5_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 127
      :p_name: boys_clothing_size_5_to_6_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 128
      :operation:
      - :to_i
      :p_name: boys_clothing_size_5_to_6_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 129
      :operation:
      - :to_i
      :p_name: boys_clothing_size_5_to_6_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '5_to_6_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 130
      :p_name: boys_clothing_size_6_to_7_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 131
      :operation:
      - :to_i
      :p_name: boys_clothing_size_6_to_7_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 132
      :operation:
      - :to_i
      :p_name: boys_clothing_size_6_to_7_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '6_to_7_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 133
      :p_name: boys_clothing_size_7_to_8_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 134
      :operation:
      - :to_i
      :p_name: boys_clothing_size_7_to_8_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 135
      :operation:
      - :to_i
      :p_name: boys_clothing_size_7_to_8_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '7_to_8_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 136
      :p_name: boys_clothing_size_8_to_9_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 137
      :operation:
      - :to_i
      :p_name: boys_clothing_size_8_to_9_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 138
      :operation:
      - :to_i
      :p_name: boys_clothing_size_8_to_9_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '8_to_9_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 139
      :p_name: boys_clothing_size_9_to_10_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 140
      :operation:
      - :to_i
      :p_name: boys_clothing_size_9_to_10_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 141
      :operation:
      - :to_i
      :p_name: boys_clothing_size_9_to_10_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '9_to_10_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 142
      :p_name: boys_clothing_size_10_to_11_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 143
      :operation:
      - :to_i
      :p_name: boys_clothing_size_10_to_11_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 144
      :operation:
      - :to_i
      :p_name: boys_clothing_size_10_to_11_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '10_to_11_years'
      :mapping: :boys_clothing
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 145
      :p_name: boys_clothing_size_11_to_12_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 146
      :operation:
      - :to_i
      :p_name: boys_clothing_size_11_to_12_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 147
      :operation:
      - :to_i
      :p_name: boys_clothing_size_11_to_12_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '11_to_12_years'
      :mapping: :boys_clothing
  - - :name: :design_code
      :col: 148
      :p_name: boys_clothing_size_12_to_13_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 149
      :operation:
      - :to_i
      :p_name: boys_clothing_size_12_to_13_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 150
      :operation:
      - :to_i
      :p_name: boys_clothing_size_12_to_13_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '12_to_13_years'
      :mapping: :boys_clothing
  - - :name: :design_code
      :col: 151
      :p_name: boys_clothing_size_13_to_14_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 152
      :operation:
      - :to_i
      :p_name: boys_clothing_size_13_to_14_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 153
      :operation:
      - :to_i
      :p_name: boys_clothing_size_13_to_14_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '13_to_14_years'
      :mapping: :boys_clothing
  - - :name: :design_code
      :col: 154
      :p_name: boys_clothing_size_14_to_15_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 155
      :operation:
      - :to_i
      :p_name: boys_clothing_size_14_to_15_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 156
      :operation:
      - :to_i
      :p_name: boys_clothing_size_14_to_15_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '14_to_15_years'
      :mapping: :boys_clothing
  - - :name: :design_code
      :col: 157
      :p_name: boys_clothing_size_15_to_16_years_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 158
      :operation:
      - :to_i
      :p_name: boys_clothing_size_15_to_16_years_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 159
      :operation:
      - :to_i
      :p_name: boys_clothing_size_15_to_16_years_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '15_to_16_years'
      :mapping: :boys_clothing
  - - :name: :design_id
      :col: 4
      :p_name: parent_sku
      :value_if: :is_stitched
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
      :skip: true
  - - :name: :size
      :col: 6
      :p_name: size
      :value_if: :is_stitched
      :child: true
      :skip: true


:Men:
- :extra:
    :url: https://www.dropbox.com/s/0h96bu9avpso5vh/Bulk%20Upload%20Men%20old%20%40.xlsx?dl=0
    :amazon_url: https://www.dropbox.com/scl/fi/siyzd1ypj7fbzfx6j5mbw/BulkUpload-Men-sheet.xlsx?rlkey=kd7swyta2cquthqk8nx7lej9y&st=wo11kqbe&dl=1
    :designable: "Kurta"
    :product_type: "kurta"
    :option_type: "size"
- :name: :gst_rate
  :col: 43
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 44
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
  :child: true
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :embellish
  :col: 41
  :p_name: embellish
- :name: :video_link
  :col: 31
  :p_name: video_link
- :name: :dynamic_size_chart
  :col: 32
  :p_name: dynamic_size_chart
  :operation:
  - :gsub
  - !ruby/regexp /\?dl=0/
  - "?dl=1"
- :name: :description
  :col: 36
  :operation:
  - :strip
  :p_name: description
- :name: :package_details
  :col: 10
  :present: true
  :p_name: package details
- :name: :parent_child
  :col: 3
  :p_name: parent_child
  :child: true
  :skip: true
- :name: :price
  :col: 8
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
  :child: true
- :name: :discount_percent
  :col: 9
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :weight
  :col: 11
  :present: true
  :operation:
  - :to_i
  :p_name: weight_in_gms
- :name: :tag_list
  :col: 33
  :operation:
  - :strip
  :p_name: tag_list
  :present: true
- :name: :pattern
  :col: 40
  :p_name: pattern
- :name: :accessories
  :col: 22
  :p_name: accessories
- :name: :region
  :col: 39
  :p_name: region
- :name: :designer_collection_id
  :col: 42
  :mapping: :collection
  :p_name:  collection
- :name: :quantity
  :col: 7
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
  :child: true
- :name: :kurta_fabric
  :col: 12
- :name: :work
  :col: 23
- :name: :kurta_color
  :col: 14
- :name: :kurta_category
  :col: 2
- :name: :size_option_name
  :col: 5
  :operation:
  - :downcase
- :name: :published
  :value:
  - true
- :name: :kurta_color
  :value:
  - :kurta_color
- :name: :sell_count
  :value:
  - 0
- :name: :grade
  :value:
  - 0
- :name: :state
  :value:
  - "processing"
- :name: :designable_type
  :value:
  - "Kurta"
- :name: :is_dhoti
  :col: 2
  :set_variable:
    :name: is_dhoti
    :value: "->(val){return ['dhotis','men-pyjamas'].exclude?(val)}"
- :name: :is_stitched
  :col: 25
  :set_variable:
    :name: is_stitched
    :value: "->(val){return val.downcase == 'stitched'}"
- :delay_images:
  - - :name: :url
      :col: 26
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 27
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 28
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 29
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 30
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
- :delay_designables:
  - - :name: :chunari_length
      :col: 21
      :p_name: chunari_length
    - :name: :kind
      :value:
      - Kurta
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :men_apparel
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :men_pyjamas
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :men_islamic_clothing
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :men
      :col: 2
      :p_name: category
    - :name: :category_id
      :mapping: :men_jackets
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :top_fabric
      :col: 12
      :p_name: top_fabric
      :present: :is_dhoti
  - - :name: :property_value_id
      :mapping: :top_fabric
      :col: 13
      :p_name: top_fabric_optional
  - - :name: :property_value_id
      :mapping: :work
      :col: 23
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :type
      :col: 24
      :p_name: type
  - - :name: :property_value_id
      :mapping: :top_color
      :col: 14
      :present: :is_dhoti
      :p_name: top_color
  - - :name: :property_value_id
      :mapping: :top_color
      :col: 15
      :p_name: top_color_optional
  - - :name: :property_value_id
      :mapping: :bottom_style
      :col: 16
      :p_name: bottom_style
  - - :name: :property_value_id
      :mapping: :bottom_color
      :col: 17
      :p_name: bottom_color
  - - :name: :property_value_id
      :mapping: :bottom_fabric
      :col: 18
      :p_name: bottom_fabric
  - - :name: :property_value_id
      :mapping: :chunari_fabric
      :col: 19
      :p_name: chunari_fabric
  - - :name: :property_value_id
      :mapping: :chunari_color
      :col: 20
      :p_name: chunari_color
  - - :name: :property_value_id
      :mapping: :stitching
      :col: 25
      :present: true
      :p_name: stitching
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 34
      :p_name: occasion
  - - :name: :property_value_id
      :mapping: :look
      :col: 35
      :p_name: look
  - - :name: :property_value_id
      :mapping: :sleeve
      :col: 37
      :p_name: sleeve
  - - :name: :property_value_id
      :mapping: :neck_style
      :col: 38
      :p_name: neck_style
  - - :name: :property_value_id
      :mapping: :fabric
      :col: 45
      :p_name: fabric
  - - :name: :property_value_id
      :mapping: :color
      :col: 46
      :p_name: color
- :variants:
  - - :name: :design_code
      :col: 47
      :p_name: size_30_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 48
      :operation:
      - :to_i
      :p_name: size_30_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 49
      :operation:
      - :to_i
      :p_name: size_30_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '30'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 50
      :p_name: size_32_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 51
      :operation:
      - :to_i
      :p_name: size_32_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 52
      :operation:
      - :to_i
      :p_name: size_32_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '32'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 53
      :p_name: size_34_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 54
      :operation:
      - :to_i
      :p_name: size_34_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 55
      :operation:
      - :to_i
      :p_name: size_34_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '34'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 56
      :p_name: size_36_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 57
      :operation:
      - :to_i
      :p_name: size_36_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 58
      :operation:
      - :to_i
      :p_name: size_36_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '36'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 59
      :p_name: size_38_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 60
      :operation:
      - :to_i
      :p_name: size_38_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 61
      :operation:
      - :to_i
      :p_name: size_38_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '38'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 62
      :p_name: size_40_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 63
      :operation:
      - :to_i
      :p_name: size_40_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 64
      :operation:
      - :to_i
      :p_name: size_40_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '40'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 65
      :p_name: size_42_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 66
      :operation:
      - :to_i
      :p_name: size_42_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 67
      :operation:
      - :to_i
      :p_name: size_42_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '42'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 68
      :p_name: size_44_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 69
      :operation:
      - :to_i
      :p_name: size_44_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 70
      :operation:
      - :to_i
      :p_name: size_44_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '44'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 71
      :p_name: size_46_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 72
      :operation:
      - :to_i
      :p_name: size_46_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 73
      :operation:
      - :to_i
      :p_name: size_46_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '46'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 74
      :p_name: size_48_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 75
      :operation:
      - :to_i
      :p_name: size_48_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 76
      :operation:
      - :to_i
      :p_name: size_48_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '48'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 77
      :p_name: size_50_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 78
      :operation:
      - :to_i
      :p_name: size_50_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 79
      :operation:
      - :to_i
      :p_name: size_50_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '50'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_code
      :col: 80
      :p_name: size_52_design_code
      :value_if: :is_stitched
      :format: mirraw
    - :name: :quantity
      :col: 81
      :operation:
      - :to_i
      :p_name: size_52_quantity
      :value_if: :is_stitched
      :format: mirraw
    - :name: :price
      :col: 82
      :operation:
      - :to_i
      :p_name: size_52_price
      :value_if: :is_stitched
      :format: mirraw
    - :name: :option_type_value_id
      :instance_operation:
      - :set_option_type_value_id
      :value:
      - '52'
      :dynamic_mapping: "garment_size,jacket_size,sherwani_size,kurta_size,indo_western_size"
      :value_if: :is_stitched
  - - :name: :design_id
      :col: 4
      :p_name: parent_sku
      :value_if: :is_stitched
      :child: true
      :skip: true
  - - :name: :sub_type
      :col: 5
      :p_name: sub_type
      :child: true
  - - :name: :size
      :col: 6
      :p_name: size
      :value_if: :is_stitched
      :child: true
      :skip: true
:Dupatta:
- :extra:
    :url: https://www.dropbox.com/scl/fi/kht3edf6ep5z1wt89cycg/dupatta_upload_sheet.xlsx?rlkey=i2cfwlnphw8ic6446gmxqdyqx&e=1&st=56wmoofu&dl=1
    :designable: "Dupatta"
    :product_type: "dupatta"
- :name: :gst_rate
  :col: 25
  :present: true
  :p_name: gst_rate
  :operation:
  - :to_i
- :name: :hsn_code
  :col: 26
  :present: true
  :p_name: hsn_code
  :operation:
  - :to_i
- :name: :design_code
  :col: 0
  :present: true
  :p_name: design_code
- :name: :title
  :col: 1
  :operation:
  - :humanize 
  :present: true
  :p_name: title
- :name: :title
  :operation:
  - :strip
- :name: :description
  :col: 23
  :operation:
  - :strip
  :p_name: description
- :name: :package_details
  :col: 3
  :present: true
  :p_name: package details
- :name: :price
  :col: 17
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){ return (val >= MIN_PRICE_PER_PRODUCT && val <= MAX_PRICE_PER_PRODUCT)}"
  :message: "Price must be in between #{MIN_PRICE_PER_PRODUCT} and #{MAX_PRICE_PER_PRODUCT}"
  :p_name: price
- :name: :discount_percent
  :col: 18
  :operation:
  - :to_i
  :p_name: discount_percent
- :name: :quantity
  :col: 16
  :present: true
  :operation:
  - :to_i
  :validation: "->(val){return val>=0}"
  :message: Quantity must be more than equal to zero
  :p_name: quantity
- :name: :designer_collection_id
  :col: 24
  :mapping: :collection
  :p_name:  collection
- :delay_images:
  - - :name: :url
      :col: 9
      :present: true
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image
    - :name: :kind
      :value:
      - master
  - - :name: :url
      :col: 10
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image1
  - - :name: :url
      :col: 11
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image2
  - - :name: :url
      :col: 12
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image3
  - - :name: :url
      :col: 13
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image4
  - - :name: :url
      :col: 14
      :operation:
      - :gsub
      - !ruby/regexp /\?dl=0/
      - "?dl=1"
      :operation:
      - :gsub
      - !ruby/regexp /\&dl=0/
      - "&dl=1"
      :p_name: image5
- :delay_designables:
  - - :name: :width
      :col: 5
      :p_name: width
    - :name: :length
      :col: 4
      :present: true
      :operation:
      - :to_i
      :p_name: length
    - :name: :height
      :col: 6
      :present: true
      :operation:
      - :to_i
      :p_name: height
    - :name: :weight
      :col: 7
      :present: true
      :p_name: weight_in_gms
    - :name: :kind
      :value:
      - Dupatta
    - :name: :params
      :value:
      - :get_attribute
- :categories_designs:
  - - :name: :category_id
      :mapping: :Dupattas
      :col: 2
      :p_name: category
- :designs_property_values:
  - - :name: :property_value_id
      :mapping: :work
      :col: 21
      :p_name: work
      :present: true
  - - :name: :property_value_id
      :mapping: :type
      :col: 22
      :p_name: type
      :present: true
  - - :name: :property_value_id
      :mapping: :fabric
      :col: 15
      :p_name: fabric_of_dupatta
  - - :name: :property_value_id
      :mapping: :occasion
      :col: 19
      :p_name: occasion
  - - :name: :property_value_id
      :mapping: :look
      :col: 20
      :p_name: look
  - - :name: :property_value_id
      :mapping: :color
      :col: 8
      :p_name: color
