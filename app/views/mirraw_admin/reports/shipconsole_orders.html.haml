= javascript_include_tag 'admin_mirraw'
= form_tag shipconsole_orders_mirraw_admin_reports_path, method: :get, class: 'form-inline mb-3' do
  .form-group
    = label_tag :start_time, 'Start Time:'
    = datetime_local_field_tag :start_time, params[:start_time] || (1.day.ago.in_time_zone('Kolkata').strftime("%Y-%m-%dT%H:%M")), class: 'form-control mx-2', style: 'color:black', autocomplete: 'off'

  .form-group
    = label_tag :end_time, 'End Time:'
    = datetime_local_field_tag :end_time, params[:end_time] || (Time.now.in_time_zone('Kolkata').strftime("%Y-%m-%dT%H:%M")), class: 'form-control mx-2', style: 'color:black', autocomplete: 'off'

  = submit_tag 'Filter', class: 'btn btn-secondary mx-2'

  = hidden_field_tag :download_report, nil, id: 'download_report_field'

  %button.btn.btn-primary{ type: 'button', id: 'download_csv_button' }
    Download CSV
