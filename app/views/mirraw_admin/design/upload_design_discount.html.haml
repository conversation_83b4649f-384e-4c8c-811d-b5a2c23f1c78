= stylesheet_link_tag 'mirraw_admin'
.container.text-center
  %h3 For Design
  = form_tag update_design_discount_mirraw_admin_design_index_path, method: 'post', multipart: true do
    .form-group
      = label_tag :csv_file, 'Upload CSV File'
      %br
      %br
      = file_field_tag :csv_file, accept: 'text/csv', required: true, class: 'csv_upload'
    .form-group
      %p
        Not sure how to structure your CSV? Download our demo CSV to see an example file.
        = link_to "Download Demo CSV", 'https://ticket-images-new.s3.amazonaws.com/design_update-02-12-2024-ashwini.bavdane%40mirraw.com-designs3.csv', class: "demo-link"
    = submit_tag "Upload", class: "btn btn-primary"
  %br
  %br
  %br
  %h3 For Child
  = form_tag update_variant_price_mirraw_admin_design_index_path, method: 'post', multipart: true do
    .form-group
      = label_tag :csv_file, 'Upload CSV File'
      %br
      %br
      = file_field_tag :csv_file, accept: 'text/csv', required: true, class: 'csv_upload'
    .form-group
      %p
        Not sure how to structure your CSV? Download our demo CSV to see an example file.
        = link_to "Download Demo CSV", 'https://ticket-images-new.s3.amazonaws.com/variant_update-02-21-2024-ashwini.bavdane%40mirraw.com-variant.csv', class: "demo-link"
    = submit_tag "Upload", class: "btn btn-primary"
  %br
  %br
  %br
  %h3 Bulk Updates
  = form_tag update_designs_bulk_mirraw_admin_design_index_path, method: 'post', multipart: true do
    .form-group
      = label_tag :design_update_file, 'Upload a valid CSV or Excel File'
      %br
      %br
      = file_field_tag :design_update_file, accept: '.csv,.xlsx', required: true, class: 'csv_upload'
    .form-group
      %p
        Not sure how to structure your CSV / Excel? Download our demo file to see an example.
        = link_to "Download Demo File", "https://test-lightning-deal.s3.amazonaws.com/design_values_update-06-25-2025-shreyash.sherigar%40mirraw.com-Bulk%20Update%20Sheet.xlsx?X-Amz-Expires=86400&X-Amz-Date=20250625T090306Z&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQN2LP2TPZFBFN27Z/20250625/us-east-1/s3/aws4_request&X-Amz-SignedHeaders=host&X-Amz-Signature=dec74945e3e351cfc9db5b2d0bc1ec4d86e3e85d832863bc0459de1d59097c90", class: "demo-link"
    = submit_tag "Upload", class: "btn btn-primary"