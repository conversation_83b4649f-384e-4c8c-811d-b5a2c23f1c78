.designer-order-bulk-state-change-form
  = form_tag bulk_state_mark_complete_perform_mirraw_admin_designer_orders_path, method: 'post', multipart: true, class: 'centered-form', id:'designer-bulk-state-change-form' do
    %h3 Upload the CSV file containing Order Numbers

    .input-row
      .file-input
        = file_field_tag :csv_file, accept: '.csv', required: true
      .dropdown-box
        %div.dropdown-btn Select Designer IDs ▼
        #checkbox-dropdown.dropdown-content{style: "display: none;"}
          - INHOUSE_VENDORS_FOR_STATE_CHANGE.each do |id|
            %label
              %input{type: "checkbox", name: "selected_ids[]", value: id}
              = id
    %br
    = submit_tag 'Submit', class: 'button btn btn-success'

:javascript
  $(document).ready(function(){
    MR.designer_orders.init();
  });

= stylesheet_link_tag 'mirraw_admin'
= javascript_include_tag 'admin_mirraw'