- payout_ratio = designer_order.ship_to == 'mirraw' ? designer_order.get_payout_ratio : 1
- all_line_items = designer_order.line_items.not_canceled
- all_line_items = all_line_items.select{|item| item.rtv_quantity.to_i > 0 } if designer_order.replacement_pending?
- all_line_items.each do |item|
  -next if item.available_in_warehouse? && designer_order.pending?
  %tr
    %td.col-md-4
      - title = item.title + " - # " + item.design.id.to_s
      - title = title + ' - ' + item.design.design_code if item.design.design_code.present?
      = link_to title, designer_design_path(@designer, item.design), :style => "text-decoration:underline;"
      - if item.note?
        %p
          - if designer_order.ship_to == 'customer' && item.note.match('Rakhi Schedule Delivery')
            %div{:style => 'background-color:#2bbf7b;color:white;padding:3px;border-radius:4px;font-size:16px;width:352px;'}= "Notes: Rakhi Schedule Delivery Date: #{RAKHI_PRE_ORDER[1]}"
          - elsif (designer_order.ship_to == 'mirraw' && item.note.match('Rakhi Schedule Delivery')) || item.note.match('Discount')
            -line_item_note = item.note.sub('Rakhi Schedule Delivery','').gsub(/Discount(.*?)off/,"").gsub("|  |","").to_s
            - if line_item_note.present?
              %h5.note_highlight= "Notes : #{line_item_note}"
          - else
            %h5.note_highlight= "Notes : #{item.note}"
          -if item.combo_variant.present?
            %h5.note_highlight
              = item.combo_variant_option_details
      - unless bulk
        %button.btn.btn-info.btn-xs{"data-target" => "#create_vendor_issue_modal_#{item.id}", "data-toggle" => "modal", :type => "button", style: 'background-color:#07519c;border-radius:15px;font-size:12px;'} Order Followup
        .modal.fade{:id => "create_vendor_issue_modal_#{item.id}", :role => "dialog", :tabindex => "-1", style: 'color:black !important;', 'data-backdrop' => 'static'}
          .modal-dialog{:role => "document"}
            .modal-content{style: "width: 85%"}
              .modal-header
                %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"} 
                  %span{"aria-hidden" => "true"} &times;
                %h4.modal-title{id: "vendor_issue_#{item.id}"} Add Followup for #{designer_order.order.number} with Product ID #{item.design_id}
              .modal-body
                = form_tag add_vendor_notes_path, method: 'post', class: 'form form-group create_issue_form', id: "create_issue_form_#{item.id}" do
                  .row
                    .col-md-10.col-md-offset-1
                      = hidden_field_tag :item_id, item.id
                      = hidden_field_tag :designer_id, designer_order.designer_id
                      = select_tag :note_issue_type, options_for_select(VENDOR_ISSUE_LIST), required: true, prompt: 'Select Issue Type', class: 'form-control issue_type_select', data: {id: item.id}
                      #received_status_check{style: 'margin-top:10px;display:none;'}                          
                        %h5= 'Did You Received the Product?'
                        .row
                          .col-md-4
                            %label.radio-inline
                              = radio_button_tag 'received_status','Received Yes',checked: true
                              &nbsp&nbsp 
                              = 'Yes'
                          .col-md-4
                            %label.radio-inline
                              = radio_button_tag 'received_status', 'Received No'
                              &nbsp&nbsp 
                              = 'No'
                      %br
                      = text_area_tag :note_reason, '', placeholder: 'Enter if any reason', class: 'form-control'
                      %br
                      = submit_tag 'Submit', class: 'form-control btn btn-success btn-sm' 

    %td.col-md-2= image_tag(item.thumbnail_image)
    %td.col-md-1.text-center
      - item_quantity = designer_order.replacement_pending? ? item.rtv_quantity : item.quantity
      - if item_quantity > 1
        %h4
          .label.label-info= item_quantity
      - else
        = item_quantity
    -transfer_price = item.price(RETURN_NORMAL)
    %td.col-md-1.text-center= (transfer_price * payout_ratio).round(2)
    %td.col-md-2.text-center
      - total_addons_price = 0
      - item.vendor_addon_items.each do |addon|
        -if addon.snapshot_price(RETURN_NORMAL) != 0
          - addon_price = addon.snapshot_price(RETURN_NORMAL) * item.quantity * payout_ratio
          - total_addons_price += addon_price
          - addon_text = "#{addon.addon_type_value.try(:name).try(:camelize)} - Rs #{addon_price.round(2)}"
        -else
          - addon_text = "#{addon.addon_type_value.try(:name).try(:camelize)} - FREE"
        %pre
          = word_wrap addon_text, :line_width => 70
    %td.col-md-2.text-center
      = (transfer_price * item.quantity * payout_ratio + total_addons_price).round(2)
    - if (DISABLE_ADMIN_FUCTIONALITY['show_unpacking_label_on_seller_panel'] || DISABLE_ADMIN_FUCTIONALITY['unpacking_label_exceptions'].include?(designer_order.designer_id)) && designer_order.ship_to == 'mirraw'
      %td.col-md-2.text-center
        = link_to 'Unpacking Label', unpacking_label_path(item_id: item.id, format: 'pdf'), target: '_blank'
  - if item.available_in_warehouse?
    %tr
      %td.label.label-success{style: 'color:black;font-size:12px;'}= 'Used From Warehouse Inventory'