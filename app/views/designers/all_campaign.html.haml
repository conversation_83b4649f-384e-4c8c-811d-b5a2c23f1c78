= javascript_include_tag 'seller_campaign'
= stylesheet_link_tag 'mirraw_admin'
= content_for :page_title, 'Campaign Portal'
.row
  .col-12
    - if @seller_campaigns.present?
    - else
      .alert.alert-danger.mt-4
        %strong Note:
        | No campaigns are currently active.
.container-fluid.campaign-container.seller_admin_portal
  .date_filter 
    = render :partial => '/designers/date_filter', locals: {date_filter_path: all_campaign_path}
  .campaign-row
    - @seller_campaigns.each do |seller_campaign|
      .campaign_columns
        .card-campaign.shadow-sm
          .card-body-campaign.d-flex.flex-column.text-center
            .campaign_portal_card_info.mx-auto
              %h5.card-title.text-primary= seller_campaign.name
              %p.card-text.text-muted= seller_campaign.description
              .campaign_portal_date_format.mt-3.mx-auto
                .start_date
                  .date-item.d-flex.align-items-center.mb-2.justify-content-center
                    %svg.calendar-icon.mr-2{width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg"}
                      %path{d: "M19 4H5C3.89543 4 3 4.89543 3 6V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V6C21 4.89543 20.1046 4 19 4Z", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                      %path{d: "M16 2V6", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                      %path{d: "M8 2V6", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                      %path{d: "M3 10H21", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                    %strong Start:
                    = seller_campaign.start_date.strftime("%B %d, %Y")
                  .date-item.d-flex.align-items-center.mb-2.justify-content-center
                    %svg.clock-icon.mr-2{width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg"}
                      %path{d: "M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                      %path{d: "M12 6V12L16 14", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                    = seller_campaign.start_date.strftime("%I:%M %p")
                .end_date
                  .date-item.d-flex.align-items-center.mb-2.justify-content-center.end-date-filter
                    %svg.calendar-icon.mr-2{width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg"}
                      %path{d: "M19 4H5C3.89543 4 3 4.89543 3 6V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V6C21 4.89543 20.1046 4 19 4Z", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                      %path{d: "M16 2V6", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                      %path{d: "M8 2V6", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                      %path{d: "M3 10H21", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                    %strong End:
                    = seller_campaign.end_date.strftime("%B %d, %Y")
                  .date-item.d-flex.align-items-center.mb-2.justify-content-center.end-time-filter
                    %svg.clock-icon.mr-2{width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg"}
                      %path{d: "M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                      %path{d: "M12 6V12L16 14", stroke: "#94a3b8", "stroke-width": "2", "stroke-linecap": "round", "stroke-linejoin": "round"}
                    = seller_campaign.end_date.strftime("%I:%M %p")
            - if @designer.present?     
              - participation = @designer.designer_campaign_participations.find_by(seller_campaign_id: seller_campaign.id)      
              - if !participation.present? && seller_campaign.end_date >= Time.now    
                = form_tag participate_in_campaign_path(seller_campaign_id: seller_campaign.id),multipart: true, method: :post do  
                  .upload-area.p-4.text-center.border.rounded.mb-3
                    %label.font-medium.text-gray-700{:for => :csv_file} Upload CSV File
                    %br
                    %br
                    .upload-csv.flex.items-center.gap-4.mt-2
                      = file_field_tag :csv_file, accept: 'text/csv',required: true, class: 'file-input px-4 py-2 border border-gray-300 rounded cursor-pointer'
                  .csv-demo-link.align-right
                    = link_to "Download Demo CSV", 'https://mirraw-test.s3.ap-southeast-1.amazonaws.com/discount_csv.csv', class: "text-blue-500 underline demo-link"
                  = hidden_field_tag :designer_id, @designer.id
                  = submit_tag 'Participate', class: 'btn submit-btn btn-primary btn-block participate-campaign'

              - elsif participation.present?
                - if seller_campaign.start_date <= Time.now
                  = link_to 'Participated', '#', class: 'btn participated-btn btn-success btn-block mt-auto mx-auto', style: "max-width: 400px; color: white"
                - elsif seller_campaign.start_date > Time.now
                  = link_to 'Cancel Participation', cancel_participation_path(participation), method: :delete, data: { confirm: 'Are you sure you want to cancel your participation?' }, class: 'btn cancel-btn btn-danger btn-block mt-auto mx-auto', style: "max-width: 400px"
  = will_paginate @seller_campaigns
