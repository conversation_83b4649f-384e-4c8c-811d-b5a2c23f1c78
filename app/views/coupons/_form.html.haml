- content_for :footer_js do
  = javascript_include_tag 'coupons'

= form_for [@designer, @coupon] do |f|
  -if @coupon.errors.any?
    #error_explanation
      %h2= "#{pluralize(@coupon.errors.count, "error")} prohibited this coupon from being saved:"
      %ul
        - @coupon.errors.full_messages.each do |msg|
          %li= msg

  .field.form-group.row
    .col-md-3= f.label :name
    .col-md-8= f.text_field :name, class: 'form-control',required: true
  .field.form-group.row
    .col-md-3= f.label :code
    .col-md-8= f.text_field :code, :readonly => "readonly"  , class: 'form-control'
  .field.row.form-inline
    .col-md-3= f.label :start_date
    .col-md-8= f.date_select :start_date, {:order => [:day, :month, :year]}, class: 'form-control'
  %br
  .field.form-inline.row
    .col-md-3= f.label :end_date
    .col-md-8= f.date_select :end_date, {:order => [:day, :month, :year]}, class: 'form-control'
  %br
  .field.form-group.row
    .col-md-3= f.label :coupon_type
    .col-md-8= f.select :coupon_type, Coupon::COUPON_TYPE.except('Credit Discounts'),{}, class: 'form-control'
  .percent_off(style="display:none;")
    .field.form-group.row
      .col-md-3= f.label :percent_off, "Percent off"
      .col-md-8= f.select :percent_off, (0..95).step(5).to_a,{}, class: 'form-control'
    .field.form-group.row
      .col-md-3= f.label :max_discount, "Max Discount (₹)"
      .col-md-8= f.number_field :max_discount, value: @coupon.max_discount, class: 'form-control', min: 1
  .flat_off
    .field.form-group.row
      .col-md-3= f.label :flat_off, "Flat Rs. Off"
      .col-md-8= f.number_field :flat_off, value: 0, class: 'form-control'
  .field.form-group.row
    .col-md-3= f.label :min_amount, 'Min Order Value'
    .col-md-8= f.number_field :min_amount, class: 'form-control',max: 100000
  .field.form-group.row
    .col-md-3= f.label :limit, 'Number Of Coupons'
    .col-md-8= f.number_field :limit, class: 'form-control',max: 100000,min: 1
  .field.form-group.row
    .col-md-3= f.label :advertise
    .col-md-3= f.check_box :advertise
    .col-md-6
      %p (Ask Mirraw to promote this coupon code)   
  .button-medium
    .field.form-group.row
      .col-md-3
      .col-md-3= f.submit 'Create Coupon' 
  %p Tips
  %ul
    %li Post this coupon code to your twitter account and facebook page.
    %li If you have list of buyers, surprise them by mailing this coupon code.
    %li Make coupon code really really attractive by giving good discounts.
    %li If you clicked on Advertise, Mirraw will mail this coupon code to all your followers and previous buyers.
