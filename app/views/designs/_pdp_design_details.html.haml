.row
  .table-container
    %table.custom-table
      %tr.table-row
        %td.table-cell
          %strong Return Count
        %td.table-cell
          %h5= @design.return_count
      %tr.table-row
        - total_count = LineItem.where(design_id: @design.id).count
        - qc_reject_count = LineItem.where(design_id: @design.id, qc_status: 'false').count
        %td.table-cell
          %strong QC Reject Rate
        %td.table-cell
          - if total_count != 0
            %h5= ((qc_reject_count.to_f / total_count) * 100).round(2).to_s + "%"
      %tr.table-row
        - delivered_count_dom = LineItem.joins(:designer_order => :order).where(design_id: @design.id).where("designer_orders.delivered_at IS NOT NULL").where(orders: { currency_code: 'RS' }).count
        %td.table-cell
          %strong Delivered Count (Domestic)
        %td.table-cell
          %h5= delivered_count_dom
      %tr.table-row
        %td.table-cell
          %strong Seller Rating
        %td.table-cell
          %h5= @design.designer.average_rating.to_f
      %tr.table-row
        %td.table-cell
          %strong Product Rating
        %td.table-cell
          %h5= @design.average_rating
      %tr.table-row
        %td.table-cell
          %strong Additional Discount
        %td.table-cell
          %h5= "#{@design.discount_percent}%"
      %tr.table-row
        - product_delivered_count = LineItem.joins(:designer_order).where(design_id: @design.id).where("designer_orders.delivered_at IS NOT NULL").count
        - product_return_count = LineItem.joins(:designer_order).where(design_id: @design.id).where.not(return_id: nil).where("designer_orders.delivered_at IS NOT NULL").count
        %td.table-cell
          %strong Buyer Return
        %td.table-cell
          - if product_delivered_count != 0
            %h5= ((product_return_count.to_f / product_delivered_count) * 100).round(2).to_s + "%"
      %tr.table-row
        %td.table-cell
          %strong USD Price
        %td.table-cell
          %h5 $ #{(@design.effective_price(country_code: 'US') / CONVERSION_RATES['US']).to_f.round(2)}
      %tr.table-row
        %td.table-cell
          %strong INR Price
        %td.table-cell
          %h5 ₹ #{@design[:price] - (@design[:price] * @design.discount_percent)/100}
    %table.custom-table
      %tr.table-row
        %td.table-cell
          %strong Designer Quantity
        %td.table-cell
          %h5= (@design.quantity.to_i - @design.in_stock_warehouse.to_i)
      %tr.table-row
        %td.table-cell
          %strong Quality
        %td.table-cell
          %h5= (@design.quality_level.to_f * 10000.0).round(2)
      %tr.table-row
        %td.table-cell
          %strong Payout
        %td.table-cell
          %h5 ₹ #{(@design.effective_price(country_code: 'IN') * (100 - @design.designer.transaction_rate.to_i) / 100).to_i}
      %tr.table-row
        %td.table-cell
          %strong Last Sold out
        %td.table-cell
          - if @design.last_sold_out.present?
            %h5= @design.last_sold_out.strftime('%d %b %Y')
      %tr.table-row
        %td.table-cell
          %strong Last In Stock
        %td.table-cell
          - if @design.last_in_stock.present?
            %h5= @design.last_in_stock.strftime('%d %b %Y')
      %tr.table-row
        %td.table-cell
          %strong Last Seller Out Of Stock
        %td.table-cell
          - if @design.last_seller_out_of_stock.present?
            %h5= @design.last_seller_out_of_stock.strftime('%d %b %Y, %I:%M %p')
      %tr.table-row
        %td.table-cell
          %strong Sell Count (International)
        %td.table-cell
          %h5= count_of_design_by_region(@design.id)[:international_count]
      %tr.table-row
        %td.table-cell
          %strong Sell Count (Domestic)
        %td.table-cell
          %h5= count_of_design_by_region(@design.id)[:domestic_count]
      %tr.table-row
        - payout = (@design.effective_price(country_code: 'IN') * (100 - @design.designer.transaction_rate.to_i) / 100).to_i
        - usd_price = (@design.effective_price(country_code: 'US') / CONVERSION_RATES['US']).to_f.round(2)
        - usd_to_inr_price = usd_price * CurrencyConvert.find_by_country_code('US').market_rate.to_f
        %td.table-cell
          %strong Margin (International)
        %td.table-cell
          %h5= (((usd_to_inr_price - payout) / usd_to_inr_price).to_f.round(2) * 100).to_s + "%"
      %tr.table-row
        - inr_price = @design.effective_price(country_code: 'IN')
        - payout = (@design.effective_price(country_code: 'IN') * (100 - @design.designer.transaction_rate.to_i) / 100).to_i
        %td.table-cell
          %strong Margin (Domestic)
        %td.table-cell
          %h5= (((inr_price - payout) / inr_price.to_f).round(2) * 100).round(2).to_s + "%"
    %table.custom-table
      %tr.table-row
        %td.table-cell
          %strong Design ETA
        %td.table-cell
          %h5= @design.eta.to_i
      %tr.table-row
        %td.table-cell
          %strong SOR Quantity
        %td.table-cell
          %h5= @design.in_stock_warehouse
      %tr.table-row
        %td.table-cell
          %strong Vacation time
        %td.table-cell
          %h5= @design.designer.vacation_days_count.to_i
      %tr.table-row
        %td.table-cell
          %strong Product Created Date
        %td.table-cell
          %h5= @design.created_at.strftime('%d %b %Y')
      %tr.table-row
        - rpv_data = RevenuePerView.where(design_id: @design.id).first
        %td.table-cell
          %strong RPV
        %td.table-cell
          - if rpv_data.present?
            %h5= rpv_data.rpv
