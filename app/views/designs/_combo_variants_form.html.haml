%fieldset.fixed-header-class#variants-panel
  %legend{:style => 'text-align:center'}Combo Variant Options
  %table.table
    %thead
      %tr
        %th.col-md-1 Size
        %th.col-md-2 Sku_code
        %th.col-md-2 Quantity
    %tbody
      = form.fields_for :combo_variants do |v|
        %tr{v.object.persisted? ? {id: "variant_row_#{v.object.id}"} : {class: 'new_combo_variants', style: 'display:none;'}}
          - v.object.option_type_values.each do |otv|
            %td.col-md-1
              = otv.p_name.presence || otv.name
              = v.hidden_field 'option_type_value_ids', :value => otv.id
            %td.col-md-2= v.text_field :design_code, id: "design_variant_code_#{v.object.id}",class: 'form-control'            
            %td.col-md-2= v.text_field :quantity, id: "design_variant_quantity_#{v.object.id}",class: 'form-control'
