- cache "menu_list/#{request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY']}/#{session[:country][:country_code]}" do
  - menus.each do |menu|
    - menu_columns = menu.menu_columns
    - menu_columns = menu_columns.sort_by(&:position)
    %li.menu-list
      - if menu.id == 42
        = link_to menu.link, class: 'menu-link', target: '_blank' do
          = menu.title
          %sup= MENU_SUPER_TAG[menu.title]
        %span.arrow{style: ("width: 4px; height:4px" unless @integration_status == "new")}
      - else
        = link_to menu.link, class: 'menu-link' do
          = menu.title
          - if MENU_SUPER_TAG.keys.include?(menu.title)
            %sup= MENU_SUPER_TAG[menu.title]
          %span.arrow{style: ("width: 4px; height:4px" unless @integration_status == "new")}
      .megamenu-box.container-fluid
        = render partial: '/layouts/menu_items', locals: {menu_columns: menu_columns, menu: menu} 