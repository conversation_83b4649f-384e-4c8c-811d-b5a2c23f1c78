%table
  %tr
    %th
    - if @variant.present?
      %h3
        = @design.title
        %small
          = "Variant: #{@variant.option_type_values.map(&:name).join(', ')}"
    - else
      %h3= @design.title

    - @dynamic_p.each do |x|
      = form_for x, :url => {:action => :update, :method=> :put} do |f|
        = f.hidden_field :country, value: x.country_id
        - if @variant.present?
          = hidden_field_tag :variant_id, @variant.id
        %tr
          %td
            .country_name
              = x.country_id
              = @countries_hash.delete (x.country_id)

          %td
            .price
              - base_price = @variant.present? ? @variant.price(RETURN_NORMAL) : @design.price(RETURN_NORMAL)
              = get_price_in_currency_with_symbol_for_scaling(base_price, x.country_code)
          %td
            = f.text_field :scale, value: x.scale.to_s , size: 3
          %td
            .scaled_price
              - base_price = @variant.present? ? @variant.price(RETURN_NORMAL) : @design.price(RETURN_NORMAL)
              = get_price_in_currency_with_symbol_for_scaling(x.scale * base_price, x.country_code)
          %td
            = f.submit "Change"
  %tr
    %td
      = form_for @dynamic_price, :url => @return_path do |w|
        = w.select :country_id, @countries_hash.map {|t,v| [v,t]}
        = w.submit 'Add'

  - if @variant.present?
    %tr
      %td
        = link_to "Back to Design", show_dynamic_price_path(@design), class: "btn btn-default"
  - else
    %tr
      %td
        %h4 Variants
        %ul
          - @design.variants.each do |variant|
            %li
              = link_to "#{variant.option_type_values.map(&:name).join(', ')} - #{variant.price}", show_variant_dynamic_price_path(@design, variant)