= stylesheet_link_tag 'dynamic_prices'
.container
  %h2 Dynamic Pricing
  %table.table.table-striped
    %tr
      %h4 Upload file format
      %p -headers of the file must have - design_id | country_id | scale | country_code
      %p -OR for variants: variant_id | country_id | scale | country_code
      %p -file should not have any blank spaces under any column.
      %p -please make sure scale selected for any country is not below minimum value(which is country specific).
      %p -if possible, try to keep one file per country.
      %p -upload all the file before 6pm to avoid clearing cache.
    %br
    %tr
      %h4 Apply B1G1
      %p -will apply Buy 1 Get 1 Offer and Update Dynamic Pricing on specified designs, of country code in uploaded file.
    %br
    %tr
      %h4 Remove B1G1 & Update Dynamic Pricing
      %p -will remove Buy 1 Get 1 Offer and Update Dynamic Pricing on specified designs, of country code in uploaded file.
    %br
    %tr
      %h4 Remove B1G1 & Revert Dynamic Pricing
      %p -will remove Buy 1 Get 1 Offer on specified designs, of country code in uploaded file.
      %p *Dynamic Pricing of specified designs will scale to 1.0
    %br
    .note
      **Selecting none will only update dynamic pricing
    %hr
    %tr
    = form_tag upload_panel_url, :method => 'POST', :multipart => true do
      = label_tag :csv_file, 'CSV File'
      = file_field_tag :csv_file, :accept => 'text/csv', :required => true
      
      - # Apply B1G1 section
      %h4 Apply B1G1 Offer
      = radio_button_tag :remove, {dynamic_pricing: true,buy_get_free: 1}.to_json, false
      = label_tag :bmgn_international, 'Apply B1G1 For International'

      = radio_button_tag :remove, {dynamic_pricing: true,buy_get_free: 2}.to_json, false
      = label_tag :bmgn_domestic, 'Apply B1G1 for Domestic'

      %p
      - # Remove B1G1 section
      %h4 Remove B1G1 Offer
      = radio_button_tag :remove, {dynamic_pricing: true,buy_get_free: nil}.to_json, false
      = label_tag :remove, 'Remove B1G1 & Update Dynamic Pricing'

      = radio_button_tag :remove, {dynamic_pricing: false,buy_get_free: nil}.to_json, false
      = label_tag :remove, 'Remove B1G1 & Revert Dynamic Pricing'
      %p
      = submit_tag :update