.row.hidden-xs.footer-info
  - designs = @designs.to_a.first(10).presence || @bestseller_designs.to_a.first(10)
  - if designs.present?
    %section.price-list-wrapper
      %h2 Latest #{@seo.label.titleize} Collection with Price
      %table
        %thead
          %tr
            %th Sr. No.
            %th.title-header #{@seo.label.titleize} List
            %th Price (#{@symbol})
        %tbody
          - counter = 0
          - designs.each do |design|
            - next unless design.designer.present?
            - counter += 1
            %tr
              %td #{counter}.
              %td
                - if design.uses_new_url_structure?
                  = link_to design.title, simplified_design_path(design)
                - else
                  = link_to design.title, designer_design_path(design.designer, design)
              %td= get_price_in_currency_with_symbol(get_price_in_currency(design.effective_price), true)
      .updated-date Data last updated on #{designs.collect(&:updated_at).max.strftime("%Y/%m/%d")}
  =raw @seo.post.gsub('<linebreak>','')