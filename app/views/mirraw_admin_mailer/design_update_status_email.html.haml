%h3{ style: "font-size: 22px; color: #333; margin-bottom: 20px;" } Design Update Summary

- if @update_status[:successful_designs].present?
  %h4{ style: "font-size: 18px; color: green; margin-top: 20px;" } Successful Designs
  %table{ style: "border-collapse: collapse; width: auto;" }
    %thead
      %tr
        %th{ style: "padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ccc;" } Design ID
        %th{ style: "padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ccc;" } Status
    %tbody
      - @update_status[:successful_designs].each do |id|
        %tr
          %td{ style: "padding: 8px; text-align: left; border: 1px solid #ccc;" }= id
          %td{ style: "padding: 8px; text-align: left; border: 1px solid #ccc;" } Successful

- if @update_status[:failed_designs].present?
  %h4{ style: "font-size: 18px; color: red; margin-top: 20px;" } Failed Designs
  %table{ style: "border-collapse: collapse; width: auto;" }
    %thead
      %tr
        %th{ style: "padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ccc;" } Design ID
        %th{ style: "padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ccc;" } Status
        %th{ style: "padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ccc;" } Error
    %tbody
      - @update_status[:failed_designs].each do |d|
        %tr
          %td{ style: "padding: 8px; text-align: left; border: 1px solid #ccc;" }= d[:id]
          %td{ style: "padding: 8px; text-align: left; border: 1px solid #ccc;" } Failed
          %td{ style: "padding: 8px; text-align: left; border: 1px solid #ccc;" }= d[:error].presence || 'None'

- if @update_status[:missing_designs].present?
  %h4{ style: "font-size: 18px; color: orange; margin-top: 20px;" } Designs Not Found
  %table{ style: "border-collapse: collapse; width: auto;" }
    %thead
      %tr
        %th{ style: "padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ccc;" } Design ID
        %th{ style: "padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ccc;" } Status
    %tbody
      - @update_status[:missing_designs].each do |id|
        %tr
          %td{ style: "padding: 8px; text-align: left; border: 1px solid #ccc;" }= id
          %td{ style: "padding: 8px; text-align: left; border: 1px solid #ccc;" } Not Found

- if @update_status[:already_updated].present?
  %h4{ style: "font-size: 18px; color: blue; margin-top: 20px;" } Already Updated Designs
  %table{ style: "border-collapse: collapse; width: auto;" }
    %thead
      %tr
        %th{ style: "padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ccc;" } Design ID
        %th{ style: "padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ccc;" } Status
    %tbody
      - @update_status[:already_updated].each do |id|
        %tr
          %td{ style: "padding: 8px; text-align: left; border: 1px solid #ccc;" }= id
          %td{ style: "padding: 8px; text-align: left; border: 1px solid #ccc;" } Already Updated

- if @update_status.values.all?(&:empty?)
  %h4{ style: "font-size: 18px; color: gray; margin-top: 20px;" } No Designs Updated :/
