- content_for :footer_js do
  = javascript_include_tag 'shipments'
- if @shipment.present?
  %div.alert.alert-success Generating UPS Invoice Please Wait            
- order_status = @order.get_courier_automation_related_details(@shipment, 'ups', current_account.id)
- if order_status[:error]
  =render :partial => 'orders/shipment_page_initial_checks' , :locals => {order_number: @order.number, error_msg: order_status[:error_message]}
- else
  - shipment_data = order_status[:shipment_data]
  = form_tag ups_invoice_url(@order.number), id: 'shipment_dispatch_form', :method => "post" do
    %table.table{:border => 1}
      %caption{:style => 'background-color:white;color:black;'}
        UPS Shipment - 
        = link_to @order.number, orders_url(:number => @order.number)
      %thead
        %tr
          %td Include
          %td Design
          %td Image
          %td Name
          %td Quantity
          %td Weight
          %td Price
      %tbody
        - product_naming_hash = {}
        - shipment_data[:item].each do |index, item_data|
          - array_name = 'item['+ index.to_s + ']'
          - design_link = designer_design_url(item_data[:designer_id], item_data[:design_id])
          %tr
            %td= check_box_tag array_name+'[selected]','selected', :checked => true
            %td= link_to item_data[:design_link_text], design_link
            %td
              = link_to design_link do
                = image_tag(item_data[:image_link], :size => '50x50')
            %td
              = hidden_field_tag  array_name+'[item_id]', item_data[:item_id]
              = hidden_field_tag  array_name+'[addon_price]', item_data[:addon_price]
              = hidden_field_tag  array_name+'[designable_type]', item_data[:designable_type]
              = hidden_field_tag array_name+'[hsn_code]', item_data[:hsn_code]
              = hidden_field_tag  array_name+'[designer_discount]', item_data[:designer_discount]
              = hidden_field_tag  array_name+'[meis]', item_data[:meis]
              - counter = product_naming_hash[item_data[:name].to_s]
              - if counter.nil?
                - counter = 1
                - product_naming_hash[item_data[:name].to_s] = counter
              - else
                - counter = counter + 1
                - product_naming_hash[item_data[:name].to_s] = counter
              = text_field_tag    array_name+'[name]', "#{item_data[:name]} - #{counter}", style: 'color: #303030'
            %td= number_field_tag array_name+'[quantity]', item_data[:quantity], {:required => true, :placeholder => 'Quantity', :min => 1, :readonly => true, :size => 5, style: 'color: #303030'}
            %td= number_field_tag array_name+'[weight]', item_data[:weight], {required: true, placeholder: 'Weight in Kgs', min: 0, readonly: true, size: 5, style: 'color: #303030'}

            -if @not_edit
              %td= text_field_tag   array_name+'[price]', item_data[:price], {:required => true, :placeholder => 'Price', :size => 5, readonly: true, style: 'color: #303030'}
            -else
              %td= text_field_tag   array_name+'[price]', item_data[:price], {:required => true, :placeholder => 'Price', :size => 5, style: 'color: #303030'}
      %tfooter
        %tr
          %td{:colspan => 1}
          %td{:colspan => 4}
            %b
              ="Package - Max Weight |"
              ="Pak - 2.5 | "
              ="Small Box - 9 | "
              ="Medium Box - 9 | "
              ="Large Box - 13 | "
              ="Others - No Limit"
          %td{:colspan => 1}
        %tr
          %td{:colspan => '100%'}
            %b="Weight Selected Items: "
            = number_field_tag :total_weight, shipment_data[:total_weight], {:required => true, :min => 0, :max => 9999, :step => 'any', style: 'color: #303030'}
            =" KGs | "
            %b= " Packager: "
            = text_field_tag :packer_id, shipment_data[:packer_id], readonly: true, style: 'width: 15%;color: #303030'
            %br
            %b= "Volume for box: "
            = "Depth"
            = number_field_tag :shipment_depth, shipment_data[:shipment_depth], { :min => 1, :max => 9999, :step => 'any',required: true, style: 'color: #303030'}
            = "cm | Width"
            = number_field_tag :shipment_width, shipment_data[:shipment_width], { :min => 1, :max => 9999, :step => 'any',required: true, style: 'color: #303030'}
            = "cm | Height"
            = number_field_tag :shipment_height, shipment_data[:shipment_height], { :min => 1, :max => 9999, :step => 'any',required: true, style: 'color: #303030'}
            = "cm"
            = hidden_field_tag :packaging_type, shipment_data[:packaging_type]
            = hidden_field_tag :packaging_type, shipment_data[:packaging_type]
            - if @order.shipment_allocations.blank?
              = submit_tag 'Create Shipment', class: 'btn btn-success'
            - else 
              %span.btn.btn-outline-info.btn.btn-success Dispatch via UPS ShipConsole
