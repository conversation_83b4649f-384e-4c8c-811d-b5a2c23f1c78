%table{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0", :width => "700"}
  %tbody
    %tr
      %td{:align => "center", :bgcolor => "#ADB1B1", :style => "text-align: center;padding:6px 0 8px 0;background:#222426", :valign => "top"}
        %div{:style => "color:white;font-size:13px;font-weight:bold;line-height:1.2;font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif"}
          Shipment Details

%table{:cellpadding => "0", :cellspacing => "0", :border => "0", :width => "100%", :style => "border-collapse: collapse; font-family: Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif; font-size: 12px; color: #000;"}
  %thead
    %tr{:style => "background:#f0f0f0; border: 1px solid #ccc"}
      %th{:style => "padding:10px; border-right:1px solid #ccc; width: 35%; text-align:left"} Designer
      %th{:style => "padding:10px; border-right:1px solid #ccc; width: 35%; text-align:left"} Item
      %th{:style => "padding:10px; border-right:1px solid #ccc; width: 15%; text-align:center"} Quantity
      %th{:style => "padding:10px; width: 15%; text-align:right"} Price

  %tbody
    - if @shipment && @shipment.line_items.present?
      - @shipment.line_items.each do |item|
        %tr{:style => "border-top:1px solid #ccc; background:#fff"}
          %td{:style => "padding:10px; border-right:1px solid #e3e3e3; vertical-align:top"}
            = link_to item.design.designer.name, designer_url(:id => item.design.designer), style: "color:#000; text-decoration:none; font-weight:bold"
            - image_url = item.design.master_image.photo.url(:small_mo)
            = wicked_pdf_image_tag(image_url, :alt => item.title, :style => "width:80px; margin-bottom:5px; display:block; border:1px solid #ddd")
          %td{:style => "padding:10px; border-right:1px solid #e3e3e3; vertical-align:top"}
            = link_to truncate(item.title, :length => 80), designer_design_url(:designer_id => item.design.designer, :id => item.design), style: "color:#000; text-decoration:none"
            
            - if item.line_item_addons.present?
              %div{:style => "background: #f3f3f3; padding: 10px;"}
                - item.line_item_addons.each do |addon|
                  %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:15px"}
                    = '+' + addon.addon_type_value.name
                    - if addon.add_paid_option_types(item.design) > 0
                      %br
                      = "+#{addon.notes.chomp(', ')}"
          %td{:style => "padding:10px; border-right:1px solid #e3e3e3; text-align:center; vertical-align:top"}
            = item.quantity
          %td{:style => "padding:10px; text-align:right; vertical-align:top"}
            = get_actual_price_in_currency_with_symbol(item.price, @order.currency_rate, @order.currency_code)
