class GaReportService
  require 'curb'
  require 'uri'
  require 'json'
  require 'jwt'
  require 'openssl'

  TOKEN_URI = 'https://oauth2.googleapis.com/token'.freeze
  GA4_REPORT_URI = 'https://analyticsdata.googleapis.com/v1beta/properties'.freeze

  def initialize
    @property_id = ENV.fetch("GA4_PROPERTY_ID"){ raise "Missing GA4_PROPERTY_ID" }.to_i
    @service_account = Rails.application.config_for(:google_ga4).deep_symbolize_keys
    @service_account[:private_key] = @service_account[:private_key].gsub("\\n", "\n")
    @access_token = fetch_access_token
  end

  def with_retry(max_attempts = 3)
    attempts = 0

    begin
      attempts += 1
      yield
    rescue Curl::Err::HostResolutionError, 
           Curl::Err::TimeoutError, 
           Curl::Err::ConnectionFailedError, 
           OpenSSL::SSL::SSLError,
           SocketError,
           EOFError => e
      if attempts < max_attempts
        puts "Request failed with #{e.class.name}: #{e.message}, retrying (#{attempts}/#{max_attempts})..."
        sleep(2**attempts)
        retry
      else
        puts "All retries exhausted. Raising error."
        raise
      end
    end
  end

  def fetch_access_token
    with_retry do
      now = Time.now.to_i
      payload = {
        iss: @service_account[:client_email],
        scope: 'https://www.googleapis.com/auth/analytics.readonly',
        aud: TOKEN_URI,
        exp: now + 3600,
        iat: now
      }

      private_key = OpenSSL::PKey::RSA.new(@service_account[:private_key])
      jwt_token = JWT.encode(payload, private_key, 'RS256')

      c = Curl::Easy.http_post(TOKEN_URI,
        Curl::PostField.content('grant_type', 'urn:ietf:params:oauth:grant-type:jwt-bearer'),
        Curl::PostField.content('assertion', jwt_token)
      )
      response = JSON.parse(c.body_str)
      response['access_token'] || raise("Failed to get access token: #{c.body_str}")
    end
  end

  def fetch_event_counts_with_pagination(offset, limit, start_date, end_date, item_id = nil)
    with_retry do
      property_id = @property_id

      dimension_filter = {
        "filter" => {
          "fieldName" => "eventName",
          "inListFilter" => {
            "values" => ["purchase", "view_item"]
          }
        }
      }

      if item_id.present? && item_id.is_a?(Array)
        dimension_filter = {
          "andGroup" => {
            "expressions" => [
              dimension_filter,
              {
                "filter" => {
                  "fieldName" => "itemId",
                  "inListFilter" => {
                    "values" => item_id.map(&:to_s)
                  }
                }
              }
            ]
          }
        }
      end

      # dimension_filter = {
      #   "andGroup" => {
      #     "expressions" => [
      #       dimension_filter,
      #       {
      #         "filter" => {
      #           "fieldName" => "country",
      #           "inListFilter" => {
      #             "values" => ["United States"]
      #           }
      #         }
      #       }
      #     ]
      #   }
      # }

      report_request_body = {
        "dateRanges" => [
          { "startDate" => start_date, "endDate" => end_date }
        ],
        "dimensions" => [
          { "name" => "itemId" },
          { "name" => "country" }
        ],
        "metrics" => [
          { "name" => "itemRevenue" },
          { "name" => "itemsViewed" },
          { "name" => "itemsPurchased" }
        ],
        "dimensionFilter" => dimension_filter,
        "metricFilter" => {
          "orGroup" => {
            "expressions" => [
              {
                "filter" => {
                  "fieldName" => "itemsViewed",
                  "numericFilter" => {
                    "operation" => "GREATER_THAN",
                    "value" => { "int64Value" => "0" }
                  }
                }
              },
              {
                "filter" => {
                  "fieldName" => "itemsPurchased",
                  "numericFilter" => {
                    "operation" => "GREATER_THAN",
                    "value" => { "int64Value" => "0" }
                  }
                }
              },
              {
                "filter" => {
                  "fieldName" => "itemRevenue",
                  "numericFilter" => {
                    "operation" => "GREATER_THAN",
                    "value" => { "int64Value" => "0" }
                  }
                }
              }
            ]
          }
        },
        "orderBys" => [
          {
            "metric" => { "metricName" => "itemsPurchased" },
            "desc" => true
          }
        ],
        "limit" => limit,
        "offset" => offset
      }

      url = "#{GA4_REPORT_URI}/#{property_id}:runReport"

      c = Curl::Easy.new(url) do |curl|
        curl.headers['Content-Type'] = 'application/json'
        curl.headers['Authorization'] = "Bearer #{@access_token}"
        curl.ssl_verify_peer = true
        curl.post_body = report_request_body.to_json
        curl.post
      end

      if c.response_code == 200
        data = JSON.parse(c.body_str)
        rows = data['rows'] || []

        results = []
        rows.each do |row|
          item_id     = row['dimensionValues'].first['value']
          country     = row['dimensionValues'].second['value']
          revenue     = row['metricValues'].first['value'].to_f
          viewed      = row['metricValues'].second['value'].to_i
          sales_count = row['metricValues'].third['value'].to_i

          results << {
            design_id: item_id,
            country: country,
            item_revenue: revenue,
            items_viewed: viewed,
            sales_count: sales_count
          }
        end
        results
      else
        raise "Error fetching report: #{c.response_code} #{c.body_str}"
      end
    end
  end
end
