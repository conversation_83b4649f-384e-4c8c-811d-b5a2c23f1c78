# app/services/rule_engine/event_calculators/ltv_calculator.rb
module RuleEngine
  module EventCalculators
    class LTVCalculator < BaseCalculator
      def call
        international_revenue + domestic_revenue
      end

      private
      def international_revenue
        international_orders.sum(:paid_amount).to_f
      end
      
      def domestic_revenue
        fully_delivered_order_ids = domestic_orders
          .joins(line_items: { designer_order: :shipment })
          .group('orders.id')
          .having('COUNT(line_items.id) = SUM(CASE WHEN shipments.shipment_state = ? THEN 1 ELSE 0 END)', 'delivered')
          .pluck(:id)

        Order.where(id: fully_delivered_order_ids).sum(:paid_amount).to_f
      end
    end
  end
end

  
