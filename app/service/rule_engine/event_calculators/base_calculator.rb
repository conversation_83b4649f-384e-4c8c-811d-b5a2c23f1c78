# app/services/rule_engine/event_calculators/base_calculator.rb
module RuleEngine
    module EventCalculators
      class BaseCalculator
        attr_reader :user
  
        def initialize(user)
          @user = user
        end
  
        def call
          raise NotImplementedError, "#{self.class} must implement #call"
        end
  
        protected
  
        def dispatched_orders
          user.orders.where(state: 'dispatched')
        end
  
        def domestic_orders
          user.orders.where(state: 'sane', country_code: 'IN')
        end
  
        def international_orders
          user.orders.where(state: 'dispatched').where.not(country_code: 'IN')
        end
  
        def delivered_line_items?(order)
          order.line_items.all? do |line_item|
            shipment = line_item.designer_order.try(:shipment) 
            shipment.present? && shipment.shipment_state == 'delivered'
          end
        end
      end
    end
  end
  