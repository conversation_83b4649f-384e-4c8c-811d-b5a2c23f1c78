# app/services/rule_engine/event_calculators/aov_calculator.rb
module RuleEngine
    module EventCalculators
      class AOVCalculator < BaseCalculator
        def call
          order_count = OrderCountCalculator.new(user).call
          return 0 if order_count.zero?
  
          ltv_amount = LTVCalculator.new(user).call
          (ltv_amount.to_f / order_count).round(2)
        end
      end
    end
end
  