module Unbxd
    require 'stringio'
    class FeedUpload
        def initialize()
            @unbxd_configs = get_unbxd_configurations()
            @upload_file_name = "/tmp/dwEfaHNSh85RwGP7.json"
            @product_categories = {}
            @design_images = {}
            @product_facets = {}
            @size_facets = {}
        end

        def curl_request_with_file()
            cmd = "curl -X POST 'http://feed-apac.unbxd.io/api/" + @unbxd_configs[:UNBXD_SITE_KEY] + "/upload/catalog/full' -F 'file=@" + @upload_file_name + "'" + " -H 'Authorization: " + @unbxd_configs[:UNBXD_SECRET_KEY] + "'"
            puts "Not Full String"
            puts cmd
            didItWork = system(cmd)
        end

        def delete_file()
            cmd = "rm -rf " + @upload_file_name
            didItWork = system(cmd)          
        end

        def send_email_about_progress(content)
            UnbxdMailer.send_progress_notification(@unbxd_configs, content, "Re <> Unbxd Feed Upload").deliver_now    
        end

        def get_upload_file_name()
            @upload_file_name
        end

        def run_the_feed
            begin
              start_time = Time.now # Capture the start time
              send_email_about_progress("Full Feed Upload has started")
              delete_file()
              make_design_images()
              make_product_facets()
              insert_into_file(convert_to_unbxd_format(get_products()))
              curl_request_with_file()
              delete_file()
              end_time = Time.now # Capture the end time
              execution_time = end_time - start_time # Calculate the total execution time
              send_email_about_progress("Full Feed Upload has Completed. Execution time: #{execution_time} seconds. Check the status of upload")
            rescue Exception => e
              end_time = Time.now # Capture the end time even if an error occurs
              execution_time = end_time - start_time # Calculate the total execution time
              send_email_about_progress("Full Feed Upload Failed! Error: #{e}. Execution time: #{execution_time} seconds.")
            end
          end
          

        def make_design_images()
            query = "
            select CONCAT('/', i.id, '/', i.photo_file_name) AS final_photo_name, i.design_id
            from designs d,images i,designers ds
            where d.id=i.design_id 
            and i.kind='master' 
            and d.state = 'in_stock' 
            and d.designer_id=ds.id 
            and ds.state_machine='review'
            "

            result = ActiveRecord::Base.connection.execute(query)
            images = result.to_a
            @design_images = Hash.new { |h, k| h[k] = [] }

            images.each do |row|
                image_url = row['final_photo_name'] ? [row['final_photo_name']] : []
                @design_images[row['design_id'].to_i] = image_url
            end

        end
        
        def category_deep_linking(id, arr)
            category = Category.find_by_id(id)

            arr << category.p_name

            if category.parent_id.nil?
                return arr
            end

            category_deep_linking(category.parent_id, arr)
        end

        def make_product_categories_from_category_table(design_id)

            categories_design = Design.find_by_id(design_id).categories.distinct

            main_category_designs_arr = []
            final_design_category = []
            categories_design.each do |category|
                final_design_category << fetch_or_cache_category_path(category.id)
            end

            final_design_category
    
        
        end

        def fetch_or_cache_category_path(id)
            # Use Rails.cache.fetch to retrieve from or write to the cache
            Rails.cache.fetch("unbxd_category_deep_linking_path_#{id}") do
              category_path = category_deep_linking(id, [])
              category_path.reverse!
              category_path.join('>')
            end
        end
          
        def make_size_facets()
            query = <<-SQL
            SELECT
                designs.id as unique_id,
                option_type_values.name as size,
                option_types.name AS option_type_names
            FROM
                designs
                left outer join variants on variants.design_id=designs.id 
                left outer join option_type_values_variants on option_type_values_variants.variant_id=variants.id 
                left outer join option_type_values on option_type_values.id=option_type_values_variants.option_type_value_id
                left outer join option_types on option_types.id=option_type_values.option_type_id 
            WHERE
                option_types.name IS NOT NULL
                AND option_type_values.name IS NOT NULL
                AND designs.state = 'in_stock'
            GROUP BY
            designs.id, 
            option_types.name,
            option_type_values.name         
            SQL
            
            result = ActiveRecord::Base.connection.execute(query)
            facets = result.to_a
          
            # Initializing a hash to store the facets data
            data = Hash.new { |h, k| h[k] = { 'sizes' => [], 'option_type_names' => [] } }
          
            # Iterating through the query result and populating the data hash
            facets.each do |row|
              design_id = row['unique_id']
              size = row['size']  # Capitalize the size to maintain consistency
              option_type_name = row['option_type_names']
          
              # Add size to the corresponding design entry
              data[design_id]['sizes'] << size unless data[design_id]['sizes'].include?(size)
          
              # Add option type name to the corresponding design entry
              data[design_id]['option_type_names'] << option_type_name unless data[design_id]['option_type_names'].include?(option_type_name)
            end
          
            # Storing the processed facets data in the instance variable
            @size_facets = data

        end


        def make_product_facets()
            query = <<-SQL
            SELECT
                designs.id as unique_id,
                properties.name AS property_name,
                property_values.p_name AS property_value,
                property_values.id AS property_value_id
            FROM
                designs
                LEFT JOIN designs_property_values ON designs_property_values.design_id = designs.id
                LEFT JOIN property_values ON property_values.id = designs_property_values.property_value_id
                LEFT JOIN properties ON properties.id = property_values.property_id
                JOIN designers ON designers.id = designs.designer_id 
            WHERE
                properties.name IS NOT NULL
                AND property_values.name IS NOT NULL
                AND designs.state = 'in_stock'
                AND properties.name <> 'gemstones'
                AND designers.state_machine = 'review'                    
            SQL

            result = ActiveRecord::Base.connection.execute(query)
            facets = result.to_a

            data = Hash.new { |h, k| h[k] = {} }
            facets.each do |row|
            design_id = row['unique_id']
            property_name = row['property_name']
            property_value = row['property_value']
            property_value_id = row['property_value_id']

            data[design_id][property_name] ||= []
            data[design_id][property_name] << property_value.capitalize

            data[design_id]['property_value_id'] ||= []
            data[design_id]['property_value_id'] << property_value_id
            end

            @product_facets = data            
            
            make_size_facets()
        end

        def insert_into_file(json_object)
            filename = @upload_file_name
            File.open(filename, 'w') do |file|
                file.write(JSON.dump(json_object))
            end
        end

        def convert_to_unbxd_format(products)
            unbxd_object = {
                "feed" => {
                    "catalog" => {
                        "add" => {
                            "items" => products
                        }
                    }
                }
            }
            return unbxd_object

        end

        def get_products()
            query = "
            SELECT 
                designs.discount_percent::numeric AS discount_percent, 
                designs.id AS unique_id, 
                designs.price, 
                designs.title,
                designs.discount_price,
                designs.in_catalog_one,
                designs.description,
                designs.design_code,
                designers.name AS designer,
                designs.cached_slug AS design_slug,
                designs.created_at,
                designers.cached_slug AS designer_slug,
                COALESCE(click_count.clicks,0)AS clicks,
                COALESCE(click_count.sell_count,0)AS sell_count,
                COALESCE(
                    (
                        SELECT 
                            count(*)AS count 
                        FROM 
                            line_items 
                        WHERE(
                            (line_items.design_id=designs.id)
                            AND (line_items.cart_id IS NOT NULL)
                            AND('updated_at'::text>='2014-06-25'::text)
                        )
                    ),(0)::bigint
                )AS added_to_cart,
                COALESCE(
                    (
                        (designs.state)::text='in_stock'::text),FALSE) AS availability,
                        designs.discount_percent AS discount,
                        designs.total_review AS total_review,
                        designs.average_rating AS average_rating,
                        designs.mirraw_certified AS mirraw_certified,
                        designs.premium AS premium,
                        designs.grade AS grade,
                        designs.ready_to_ship AS ready_to_ship,
                        designs.likes_count AS likes_count,
                        designs.state AS state,
                        designs.quantity AS stock,
                        designs.designable_type AS TYPE,
                        (CASE 
                        WHEN designs.average_rating>=3.5 
                        THEN TRUE 
                        ELSE FALSE END)AS mirraw_recommended,
                        designers.additional_discount_end_date AS promotion_offer_end_time,
                        designers.average_rating AS designer_rating,
                        category_ids.category_ids
                        FROM(
                            (designs JOIN designers ON((designs.designer_id=designers.id)))
                            LEFT JOIN (
                                SELECT
                                    categories_designs.design_id,
                                    array_agg(categories_designs.category_id) AS category_ids
                                FROM
                                    categories_designs
                                GROUP BY
                                    categories_designs.design_id
                            ) category_ids ON category_ids.design_id = designs.id
                            LEFT JOIN(
                                SELECT 
                                    designs_clicked.id,
                                    designs_clicked.clicks,
                                    designs_clicked.sell_count 
                                FROM 
                                    designs designs_clicked 
                                WHERE(
                                    designs_clicked.updated_at>'2014-06-25 00:00:00'::timestamp without time zone
                                )
                            )click_count ON((
            click_count.id=designs.id)))
            WHERE
                (designs.state)::text='in_stock' 
                and designers.state_machine = 'review' 
                and (designs.in_catalog_one = 2 OR designs.in_catalog_one = 1 OR designs.in_catalog_one is null);
            "
            items = ActiveRecord::Base.connection.execute(query)
            products = []

            items.each do |product|
                design_id = product['unique_id']
                categorypath = make_product_categories_from_category_table(design_id.to_s) 
                if categorypath.present?
                    %w(discount_percent price discount_price clicks sell_count added_to_cart discount grade stock category_id designer_rating uniqueId sku in_catalog_one).each {|attri|
                        product[attri] = product[attri].to_i
                    }
                    design = Design.includes(:variants, :designer).find_by_id(design_id)
                    product['ready_to_ship'] = design.ready_to_ship_status ? 'Yes' : 'No'

                    if product['created_at'].present?
                        product['created_at'] = Time.parse(product['created_at'].to_s) rescue product['created_at']
                    end
                    %w(availability mirraw_certified mirraw_recommended).each{|attri|
                    product[attri] = product[attri] == 't'
                    }

                    product = product.to_h
                    
                    if product.key?('promotion_offer_end_time')
                        product['promotion_offer_end_time'] = product['promotion_offer_end_time'].to_s
                    end

                    product['categoryPath'] = categorypath 
                    product['image_urls'] = @design_images[design_id.to_i] || []
                
                    product['product_url'] = if product['designer_slug'].present? && product['design_slug'].present?
                                                '/designers/' + product['designer_slug'] + '/designs/' + product['design_slug']
                                            else
                                                ''
                                            end

                    product.merge!(@product_facets[design_id] || {})
                    product.merge!(@size_facets[design_id] || {})
                                        
                    product['uniqueId'] = product['sku'] = product.delete('unique_id').to_s
                                        
                   
                    products << product
                end
            end
            products
        end

        private

        def get_unbxd_configurations()
            Mirraw::Application.config.unbxd
        end

        
        
    end
end