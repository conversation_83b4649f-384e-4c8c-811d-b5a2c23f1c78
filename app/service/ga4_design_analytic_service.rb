class Ga4DesignAnalyticService
  BATCH_SIZE = 99000

  def initialize(start_date:, end_date:, period:)
    @start_date = start_date
    @end_date = end_date
    @period = period
    @ga_report_service = GaReportService.new
  end

  def perform
    offset = 0
  
    loop do
      begin
        results = @ga_report_service.fetch_event_counts_with_pagination(offset, BATCH_SIZE, @start_date, @end_date)
        break if results.empty?
  
        records = results.map do |metrics|
          next if metrics[:country].blank?
  
          GaRpvDesignAnalytics.new(
            design_id: metrics[:design_id],
            country: metrics[:country],
            period: @period,
            revenue: metrics[:item_revenue],
            views: metrics[:items_viewed],
            sales: metrics[:sales_count],
            created_at: Time.current,
            updated_at: Time.current
          )
        end.compact
  
        GaRpvDesignAnalytics.import(
          records,
          on_duplicate_key_update: {
            conflict_target: [:design_id, :country, :period],
            columns: [:revenue, :views, :sales, :updated_at]
          }
        )
      rescue => e
        Rails.logger.error "[Ga4DesignAnalyticService] Error at offset #{offset}: #{e.class} - #{e.message}"
      ensure
        offset += BATCH_SIZE
      end
    end
  end
  
end
