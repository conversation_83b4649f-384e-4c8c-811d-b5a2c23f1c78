require 'csv'
require 'open-uri'
require 'tempfile'
require 'rubyXL'

class ProcessShipconsoleInvoice
  def initialize(email, csv_file_path)
    @old_sheet = 'https://mirraw-test.s3.ap-southeast-1.amazonaws.com/gst_file.xlsm'
    @csv_file_path = csv_file_path
    @invoice_csv = nil
    @email = email
  end

  def run
    download_and_parse_workbook
    parse_csv_file
    extract_invoice_metadata
    insert_items_into_sheet
    insert_invoice_summary
    write_and_send_excel
  end

  def download_and_parse_workbook
    tempfile = Tempfile.new(['gst_file', '.xlsx'])
    tempfile.binmode
    open(@old_sheet) { |stream| tempfile.write(stream.read) }
    tempfile.flush
    @workbook = RubyXL::Parser.parse(tempfile.path)
    tempfile.close
    tempfile.unlink
  end

  def parse_csv_file
    @invoice_csv = Tempfile.new(['shipconsole_invoice', '.csv'])
    open(@csv_file_path) do |remote_file|
      @invoice_csv.binmode
      @invoice_csv.write(remote_file.read)
      @invoice_csv.rewind
    end
    @csv_rows = CSV.read(@invoice_csv.path)
    @invoice_csv.close
    @invoice_csv.unlink
  end

  def extract_invoice_metadata
    @invoice_number = nil
    @invoice_date = nil
    @total_value = nil
    @postal_code = nil
    @name = nil

    @csv_rows.each_with_index do |row, index|
      row_text = row.compact.join(" ")

      @invoice_date ||= row.find { |cell| cell && cell =~ /\d{1,2}-[A-Za-z]{3}-\d{4}/ } if row_text.include?("Invoice Date")
      @invoice_number ||= row.find { |cell| cell && cell =~ /[A-Z]+\d+/ } if row_text.include?("Invoice Number")
      if @postal_code.nil? && row_text.include?("Postal Code")
        matches = row_text.scan(/\b\d{5}\b/)
        @postal_code = matches.first if matches.any?
      end
      if @total_value.nil? && row[0].to_s.include?("Total Invoice Value:") && index > 0
        @total_value = @csv_rows[index - 1][2].strip rescue nil
      end
      @name ||= row[3].to_s.strip if row_text.include?("# 1")
      break if @invoice_date && @invoice_number && @total_value && @postal_code && @name
    end
  end

  def insert_items_into_sheet
    items_sheet = @workbook.worksheets.find { |ws| ws.sheet_name == 'Items' }
    return unless items_sheet && items_sheet[2]

    item_headers = items_sheet[2].cells.map { |c| c && c.value.to_s.strip }
    insert_row = 4
    serial_number = 0

    @csv_rows.each do |row|
      row_text = row.compact.join(" ")
      next unless row_text.include?("#")

      desc = row[9]
      hsn_code = row[13]
      quantity = row[7]
      unit_price = row[15]
        total_price = row[17]
        if desc && hsn_code && quantity && unit_price && total_price
            serial_number += 1

            item_row = {
              "Document Type *" => "Tax Invoice",
              "Document Number *" => @invoice_number,
              "Document Date (DD/MM/YYYY) *" => @invoice_date,
              "Sl.No. *" => serial_number,
              "Product Description" => desc,
              "Is_Service *" => "NO",
              "HSN code *" => hsn_code,
              "Quantity *" => quantity,
              "Unit *" => "PIECES",
              "Unit Price *" => unit_price,
              "Gross Amount *" => total_price,
              "Taxable value *" => total_price,
              "GST Rate (%) *" => "0",
              "Item Total *" => total_price
            }

            item_headers.each_with_index do |header, idx|
              value = item_row[header] || ''
              items_sheet.add_cell(insert_row, idx, value)
            end
            insert_row += 1
        end
    end
  end

  def insert_invoice_summary
    sheet = @workbook.worksheets.find { |ws| ws.sheet_name == 'eInvoice' }
    return unless sheet

    header_row = sheet[2].cells.map { |c| c && c.value.to_s.strip }
    insert_row = 3
    while sheet[insert_row] && sheet[insert_row].cells.any? { |c| c && c.value.to_s.strip != '' }
      insert_row += 1
    end

    row_data = {
      "Supply Type code *" => "EXPWOP",
      "Document Type *" => "Tax Invoice",
      "Document Number *" => @invoice_number,
      "Document Date (DD/MM/YYYY) *" => @invoice_date,
      "Buyer GSTIN *" => "URP",
      "Buyer Legal Name *" => @name,
      "Buyer POS *" => "OTHER COUNTRIES",
      "Buyer Addr1 *" => "United States",
      "Buyer Location *" => "United States",
      "Buyer Pin Code" => @postal_code,
      "Buyer State *" => "OTHER COUNTRIES",
      "Total Taxable value *" => @total_value,
      "Total Invoice value *" => @total_value
    }

    header_row.each_with_index do |header, idx|
      sheet.add_cell(insert_row, idx, row_data[header] || '')
    end
  end

  def write_and_send_excel
    output_path = "/tmp/e_invoice_#{Time.now.to_i}.xlsx"
    @workbook.write(output_path)
    MirrawAdminMailer.send_updated_doc(@email, output_path,"Your E-Invoice Document").deliver_now
    File.delete(output_path) if File.exist?(output_path)
  end
end
