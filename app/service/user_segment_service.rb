# app/services/user_segment_service.rb
class UserSegmentService
    def initialize(user)
      @user = user
    end
  
    def evaluate
      SegmentRule.includes(:segment_rule_conditions).where(active: true).find_each do |rule|
        begin
          if satisfies_composite_rule?(rule)
            assign_segment(rule)
          else
            remove_segment(rule) # optional cleanup
          end
        rescue ActiveRecord::RecordInvalid => e
          ExceptionNotify.sidekiq_delay.notify_exceptions("Failed to assign rule #{rule.name} (ID: #{rule.id}) to user #{@user.id}", e.message, {error: e})
        rescue StandardError => e
          ExceptionNotify.sidekiq_delay.notify_exceptions("Unexpected error during rule #{rule.name} (ID: #{rule.id}) evaluation for user #{@user.id}", e.message, {error: e})
        end
      end
    end
  
    private
  
    def satisfies_composite_rule?(rule)
      metric_cache = {}
      rule.segment_rule_conditions.all? do |condition|
        value = metric_cache[condition.metric_key] ||= calculate_event_value(condition.metric_key)
        value.present? && evaluate_condition(value, condition)
      end
    end
    
    def evaluate_condition(value, condition)
      min = condition.min_value.to_f
      max = condition.max_value.to_f
      op  = condition.operator
  
      case op
      when '>'
        value > min
      when '>='
        value >= min
      when '<'
        value < min
      when '<='
        value <= min
      when '='
        value == min
      when '!='
        value != min
      when 'between'
        value >= min && value <= max
      else
        Rails.logger.warn "Unknown operator '#{op}' in rule #{condition.segment_rule_id}"
        false
      end
    end
  
    def calculate_event_value(metric_key)
      case metric_key
      when 'ltv'
        RuleEngine::EventCalculators::LTVCalculator.new(@user).call
      when 'aov'
        RuleEngine::EventCalculators::AOVCalculator.new(@user).call
      when 'order_count'
        RuleEngine::EventCalculators::OrderCountCalculator.new(@user).call
      else
        Rails.logger.error "Unsupported metric: #{metric_key}"
        nil
      end
    end
  
    def assign_segment(rule)
      UserSegmentRule.find_or_create_by!(user: @user, segment_rule: rule)
    end
  
    def remove_segment(rule)
      UserSegmentRule.where(user: @user, segment_rule: rule).destroy_all
    end
  end
  