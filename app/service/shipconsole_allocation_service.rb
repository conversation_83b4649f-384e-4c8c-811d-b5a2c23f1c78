class ShipconsoleAllocationService

    def initialize(order, design_rack_quantity)
      @order = order
      @design_rack_quantity = design_rack_quantity
    end
  
    def call

      all_valid_hsn = @order.line_items.all? do |item|
        SHIPCONSOLE_HSN_CODE.include?(item.design.categories.hsn_code)
      end
  
      if all_valid_hsn
        shipper = Shipper.fetch_shipper(@order.best_shipper)
        line_item_ids = @design_rack_quantity.values.map { |v| v[:line_item_id] }
        line_item_ids.each do |line_item_id|
          ShipmentAllocation.create!(
            order_id: @order.id,
            shipper_id: shipper.id,
            shipper_service_id: shipper.shipper_services.first.id,
            line_item_id: line_item_id
          )
        end
      end
    end
  end
  