require 'csv'

class ShipconsoleTrackingService
    
    def initialize(email, csv_file_path)
        @email = email
        @csv_file_path = csv_file_path
	end

    def run
        update_tracking
    end

    def update_tracking
        missed_products = []
        hsn_codes = []
        master_tracking_number = nil
        csv = CSV.new(open(@csv_file_path), headers: true, col_sep: ";")
        csv.each do |row|
            begin
                order_number = row["OrderID"]
                tracking_number = row["tracking"]
                master_tracking = row["ref1"].split("#").first.to_s.strip
                weight = row["Weight"]
                account = Account.where(email: @email).first
                order = Order.where(number: order_number).first
                master_tracking_number ||= master_tracking
                if order.present? && tracking_number.present? && master_tracking.present?
                    shipper = Shipper.fetch_shipper(order.best_shipper)
                    shipment = order.shipments.where(number: tracking_number, shipper_id: shipper.id).first_or_initialize
                    shipment.assign_attributes(
                        weight: weight,
                        packer_id: account.id,
                        invoicer_id: account.id
                    )
                    shipment.save!
                    order.shipment_allocations.update_all(master_tracking_number: master_tracking)
                    shipment_allocations = order.shipment_allocations.includes(line_item: { design: :categories })
                    shipment_allocations.each do |alloc|
                        li = alloc.line_item
                        li.update_column(:shipment_id, shipment.id)
                        hsn_codes << li.design.categories.hsn_code
                    end
                    if order.shipment_allocations.count == order.line_items.count
                        order.package_shipped! if order.state.downcase == 'ready_for_dispatch'
                    end
                else
                    missed_products << { object: order_number, reason: 'Missing order, tracking order master tracking for this object' }
                    MirrawAdminMailer.failed_shipconsole_order_update(missed_products,@email,"Missed Following orders tracking").deliver_now if missed_products.present?
                end
            rescue => error
                ExceptionNotify.sidekiq_delay.notify_exceptions('Upload Tracking Error', 'Tracking issue', { error: error.inspect })
            end
        end
        if hsn_codes.present? && master_tracking_number.present?
            service = DocumentUpdater.new(hsn_codes, master_tracking_number)
            path = service.call
            MirrawAdminMailer.send_updated_doc(@email, path, "Your Sli Document").deliver_now
            File.delete(path) if File.exist?(path)
        end
    end
end
