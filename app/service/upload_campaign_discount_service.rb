class UploadCampaignDiscountService
    def initialize(csv_file_path, user_email, seller_campaign_id, designer_id)
        @csv_file_path = csv_file_path
        @user_email = user_email
        @seller_campaign_id = seller_campaign_id
        @designer_id = designer_id
    end

    def run
        missing_designs = []
        CSV.new(open(@csv_file_path), headers: true).each do |row|
            design_id = row['design_id'].to_i if row['design_id'].present?
            campaign_discount = row['discount_percent'].to_f if row['discount_percent'].present?
            begin
                design = Design.find design_id
                current_account = Account.where(email: @user_email).first
                if current_account.admin? || (current_account.designer? && current_account.designer == design.designer)
                    if campaign_discount.present?
                        if campaign_discount >= 0 && campaign_discount <= 99
                            design_campaign_discount = DesignCampaignDiscount.find_or_initialize_by(
                              design_id: design_id,
                              seller_campaign_id: @seller_campaign_id,
                              designer_id: design.designer_id
                            )
                            design_campaign_discount.discount = campaign_discount
                            design_campaign_discount.save!
                        else
                          missing_designs << { design_id: design_id, reason: "Discount percent #{campaign_discount} out of allowed range (0–99)" }
                        end
                    else
                        missing_designs << { design_id: design_id, reason: 'Discount percent is missing' }
                    end
                else
                    missing_designs << { design_id: design_id, reason: 'Unauthorized access' }
                end
            rescue => exception
                missing_designs << { design_id: design_id, reason: 'Design ID not found' }
            end
        end
        DesignerMailer.missing_designs_for_campaign(missing_designs, @user_email).deliver_now! if missing_designs.any?
    end
end