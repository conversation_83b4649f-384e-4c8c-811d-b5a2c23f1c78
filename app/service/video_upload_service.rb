class VideoUploadService
  def initialize(email, csv_file_path)
    @csv_file_path = csv_file_path
    @email = email
  end

  def run
    missing_designs = []
    successful_designs = []
    csv_content = open(@csv_file_path).read
    CSV.parse(csv_content, headers: true, header_converters: ->(header) { header.strip }) do |row|
      design_id = row['design_id'].strip.to_i if row['design_id'].present?
      video_link = row['video_link'] if row['video_link'].present?

      begin
        design = Design.find(design_id)
        current_account = Account.where(email: @email).first
        if current_account.admin? || (current_account.designer? && design.designer == current_account.designer)
          if video_link.present?
            if %w[.mp4 .webm .mov].any? { |ext| video_link.include?(ext) } || video_link.include?("drive.google.com")
              SingleVideoUploadJob.perform_async(current_account.email, design.id, video_link)
              successful_designs << { design_id: design_id, link: video_link }
            else
              missing_designs << { design_id: design_id, reason: 'Missing video link OR format is invalid' }
            end
          else
            missing_designs << { design_id: design_id, reason: 'Missing video link OR format is invalid' }
          end
        else
          missing_designs << { design_id: design_id, reason: 'Unauthorized access' }
        end
      rescue ActiveRecord::RecordNotFound
        missing_designs << { design_id: design_id, reason: 'Design ID not found' }
      end
    end
    DesignerMailer.success_notification_for_video(successful_designs, @email).deliver_now if successful_designs.any?
    DesignerMailer.missing_designs_email(missing_designs, @email).deliver_now if missing_designs.any?
  end
end
