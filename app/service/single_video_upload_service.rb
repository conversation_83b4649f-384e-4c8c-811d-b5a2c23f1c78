class SingleVideoUploadService

    def initialize(email, design_id, link)
      @email = email
      @design_id = design_id
      @link = link
    end
  
    def run
      begin
        directory = Fog::Storage.new(
          provider: 'AWS',
          aws_access_key_id: ENV.fetch('AWS_ACCESS_KEY_ID'),
          aws_secret_access_key: ENV.fetch('AWS_SECRET_ACCESS_KEY'),
          region: ENV.fetch('AWS_REGION')
        ).directories.new(key: ENV.fetch('S3_BUCKET'))
      
        input_url = @link.gsub(/dl=0/, 'dl=1')
        if @link.include?("drive.google.com")
          original_url = input_url
          file_id = original_url[/\/d\/(.+?)\//, 1]
          input_url = "https://drive.google.com/uc?export=download&id=#{file_id}"
        end
      
        # Download the file
        downloaded_file = Down.download(input_url)
        
        # Verify it's a video file using multiple checks
        movie = FFMPEG::Movie.new(downloaded_file.path)
        
        # Check 1: Basic FFMPEG validation
        unless movie.valid?
          raise "File is not a valid video (FFMPEG validation failed)"
        end
      
        # Check 2: Has video stream
        unless movie.video_codec.present?
          raise "File has no video stream (might be audio-only or image)"
        end
      
        # Check 3: Minimum duration (images will have 0 duration)
        if movie.duration <= 0.1 # Less than 0.1 seconds
          raise "Video duration too short (likely an image)"
        end
      
        # Check 4: MIME type verification
        mime_type = `file --b --mime-type "#{downloaded_file.path}"`.strip rescue 'unknown'
        unless mime_type.start_with?('video/')
          raise "MIME type (#{mime_type}) indicates this is not a video file"
        end
      
        # Only proceed if all checks pass
        output_path = Rails.root.join('tmp', "compressed_#{SecureRandom.uuid}.mp4")
      
        min_size = 6 * 1024 * 1024   # 6 MB in bytes
        max_size = 10 * 1024 * 1024  # 10 MB in bytes
        current_size = Float::INFINITY
        crf_value = 28
      
        begin
          movie.transcode(output_path.to_s, {
            video_codec: 'libx264',
            audio_codec: 'aac',
            custom: %W[
              -crf #{crf_value}
              -preset fast
              -vf scale=1280:-2
              -movflags +faststart
              -f mp4
            ]
          })
        
          current_size = File.size(output_path)
      
          if current_size > max_size
            crf_value += 2  # Increase compression to reduce size
          elsif current_size < min_size
            crf_value -= 2  # Decrease compression to increase quality/size
          end
        end until current_size.between?(min_size, max_size) || crf_value >= 51 || crf_value <= 18
      
        @design = Design.find(@design_id)
        @design.update_columns(has_video: true, video_link: @link)
        
        filename = "product-video/#{@design.id}.mp4"
        existing_file = directory.files.get(filename)
        existing_file.destroy if existing_file.present?
        
        directory.files.create(
          key: filename,
          body: File.open(output_path, 'rb'),
          public: true,
          content_type: 'video/mp4'
        )
      
        File.delete(downloaded_file.path) if downloaded_file && File.exist?(downloaded_file.path)
        File.delete(output_path) if output_path && File.exist?(output_path)
      rescue Down::Error => e
        error_message = "Failed to download the file from URL: #{e.message}"
        DesignerMailer.send_progress_notification("Design Video Download Failed for design #{@design_id}: #{error_message}", @email).deliver_now!
        raise error_message
      rescue RuntimeError => e
        error_message = "Video validation failed for design #{@design_id}: #{e.message}"
        DesignerMailer.send_progress_notification(error_message, @email).deliver_now!
        raise error_message
      rescue FFMPEG::Error => e
        error_message = "Video processing failed for design #{@design_id}: #{e.message}"
        DesignerMailer.send_progress_notification(error_message, @email).deliver_now!
        raise error_message
      rescue StandardError => e
        error_message = "Unexpected error processing video for design #{@design_id}: #{e.message}"
        DesignerMailer.send_progress_notification(error_message, @email).deliver_now!
        raise error_message
      
      rescue => e
        Rails.logger.error "Failed to clean up temporary files: #{e.message}"
      end
    end
end