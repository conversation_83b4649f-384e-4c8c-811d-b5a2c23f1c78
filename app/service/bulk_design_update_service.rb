class BulkDesignUpdateService
  def initialize(file_path)
    @file_path = file_path
    @data = fetch_valid_properties
    @design_attributes = BULK_DESIGN_EDITABLE_FIELDS
    @update_status = {:successful_designs=>[],:failed_designs=>[], :missing_designs=>[], :already_updated=>[]}
  end

  def run
    process_file
  end

  def any_restrictions?(design)
    restrictions = { default_attributes: {quantity:design.variants.present?}}
    restrictions.any? do |section_key, fields|
      @design_to_update[section_key].to_h.any? do |field_key, _|
        return field_key if fields[field_key.to_sym] == true
      end
    end
  end


  def extract_rows_from_file(file_path)
    extension = URI.parse(file_path).path.split('.').last.downcase rescue nil
    case extension
    when 'csv'
      CSV.new(open(file_path), headers: true)
    when 'xls', 'xlsx'
      spreadsheet = Roo::Spreadsheet.open(file_path)
      headers = spreadsheet.row(1).map(&:to_s)
      (2..spreadsheet.last_row).map do |i|
        row_values = spreadsheet.row(i)
        CSV::Row.new(headers, row_values, true)
      end
    else
      raise "Unsupported file format: #{extension}"
    end
  end


  def process_file
    rows = extract_rows_from_file(@file_path)
    rows.each do |row|
      next if row.to_h.values[1..-1].all?(&:nil?) || row[0].to_s.strip.empty?
      begin
        design = Design.find_by(id:row[0])  
        design.present? ? prepare_design_updates(row, design) : @update_status[:missing_designs] << row[0].to_s.strip
      rescue => e
        Rails.logger.error "Bulk Design Update Failed due to #{e.message}"
      end
    end
    get_designs_update_status
  end

  def prepare_design_updates(row,design)  
    designable_type,existing_property_values,valid_designable_attributes,valid_property_values = initialize_attributes(design,design.designable_type)
    row.to_a[1..-1].each do |header, cell_value|
      next if header.to_s.strip.empty? || cell_value.to_s.strip.empty?  
      header,cell_value = header.to_s.downcase,cell_value.to_s.downcase
      populate_update_attributes(header,design,cell_value,existing_property_values,valid_designable_attributes,valid_property_values)
    end
    check_failed_design(design) && return
    @design_to_update.values.all? { |v| v.empty? } ? @update_status[:already_updated] << design.id : update_design_attributes(design)
  end

  def initialize_attributes(design,designable_type)
    @design_to_update = {:default_attributes=> {}, :designable=> {}, :property_values=> []}
    @failed_attributes = {properties:[],values:[]}
    designable_type = design.designable_type
    valid_property_values = get_valid_properties_for_designable(design)
    existing_property_values = design.property_values.joins(:property).pluck('properties.name', :property_value_id).to_h
    valid_designable_attributes = design.designable.attribute_names - %w(id created_at updated_at)
    return designable_type,existing_property_values,valid_designable_attributes,valid_property_values
  end

  def populate_update_attributes(header,design,cell_value,existing_property_values,valid_designable_attributes,valid_property_values)
    if @design_attributes.include?(header)
      design[header] != cell_value && set_default_attribute(header,cell_value)  
    elsif valid_property_values[header.to_sym].present? && header.to_s.downcase != "stitching"
      new_property_value_id = valid_property_values[header.to_sym][cell_value.gsub('-', '_').to_sym]
      new_property_value_id.present? ? existing_property_values[header] != new_property_value_id && set_property_value(header,existing_property_values,new_property_value_id) : @failed_attributes[:values] << "#{header} => #{cell_value} "
    elsif valid_designable_attributes.include?(header)
      design.designable[header]!=cell_value && set_designable_attribute(header,cell_value)
    else
      @failed_attributes[:properties] << header
    end
  end

  def set_default_attribute(header,cell_value)
    @design_to_update[:default_attributes][header] = cell_value
  end

  def set_property_value(header,existing_property_values,new_property_value_id)
    @design_to_update[:property_values] << {:existing_id=>existing_property_values[header],:new_id=>new_property_value_id}
  end

  def set_designable_attribute(header,cell_value)
    @design_to_update[:designable][header] = cell_value
  end

  def get_designs_update_status
    MirrawAdminMailer.design_update_status_email(@update_status).deliver_now
  end

  def get_valid_properties_for_designable(design)
    types = design.designable_type.to_sym == :Other ? ["WesternWear", "Footwear", "BottomWear"] : [design.designable_type]
    types.map(&:to_sym) .select { |type| @data.key?(type) } .inject({}) { |val, des_type| val.merge(@data[des_type] || {}) }    
  end

  def fetch_valid_properties
    data = {}
    BulkUpload.meta_data.keys.each do |designable_type|
      data[designable_type] = Property.get_property_value_mapping(BulkUpload.meta_data[designable_type][:designs_property_values].to_a)
      data[designable_type] = data[designable_type].merge(OptionType.get_option_type_values(BulkUpload.meta_data[designable_type][:variants].to_a))
    end
    data[:Kurta] = data[:Men] #since Men is mapped to kurta
    data
  end

  def update_design_attributes(design)
    begin
      ActiveRecord::Base.transaction do
        design.update!(@design_to_update[:default_attributes]) if @design_to_update[:default_attributes].any?
        design.designable.update!(@design_to_update[:designable]) if @design_to_update[:designable].any?
        if @design_to_update[:property_values].any?
          @design_to_update[:property_values].each do |field|
            field[:existing_id].nil? ? DesignsPropertyValue.import([DesignsPropertyValue.new(design_id: design.id, property_value_id: field[:new_id])]) : DesignsPropertyValue.where(design_id: design.id, property_value_id: field[:existing_id]).update_all(property_value_id: field[:new_id])
          end
        end
        @update_status[:successful_designs] << design.id if @design_to_update.values.any?(&:present?)
      end
    rescue => e
      @update_status[:failed_designs] << { id: design.id, error: "An unexpected error occured" }
    end
  end

  def check_failed_design(design)
    violated_key = any_restrictions?(design)
    if @failed_attributes.values.any?(&:any?) || violated_key.present?
      error_parts = []
      error_parts << "Invalid Properties: #{@failed_attributes[:properties].join(', ')}" if @failed_attributes[:properties].any?
      error_parts << "Invalid Values: #{@failed_attributes[:values].join(', ')}" if @failed_attributes[:values].any?
      error_parts << "Cannot change #{violated_key}" if violated_key.present?
      error_message = error_parts.join('  | ')
      @update_status[:failed_designs] << { id: design.id, error: error_message }
      true
    end
  end
end
