class DocumentUpdater

    TEMPLATE_URL = "https://mirraw.s3.ap-southeast-1.amazonaws.com/new_doc.docx"
    PREVIOUS_TRACKING = "1ZA373X40400000008"
    PREVIOUS_HSN_CODE = "61140499"

    def initialize(hsn_codes, master_tracking_number)
        @hsn_codes = hsn_codes.uniq.join(',')
        @master_tracking_number = master_tracking_number
    end
    
    def call
        download_path = Rails.root.join("tmp", "sli_doc_#{SecureRandom.hex}.docx")
        output_path = Rails.root.join("tmp", "SLI-GST-Updated-#{SecureRandom.hex}.docx")
    
        open(TEMPLATE_URL) do |file|
          File.open(download_path, "wb") { |f| f.write(file.read) }
        end
    
        doc = DocxReplace::Doc.new(download_path.to_s, Rails.root.join("tmp").to_s)
        doc.replace(PREVIOUS_TRACKING, @master_tracking_number)
        doc.replace(PREVIOUS_HSN_CODE, @hsn_codes)
        doc.commit(output_path.to_s)
        
        File.delete(download_path) if File.exist?(download_path)
        output_path
    end
end