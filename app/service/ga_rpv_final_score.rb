class GaRpvFinalScore
  PERIOD_WEIGHTS = {
    7 => 0.4,
    30 => 0.3,
    90 => 0.2,
    365 => 0.1
  }

  def perform
    grouped_data = {}

    GaRpvDesignAnalytics.find_each do |record|
      key = [record.design_id, record.country]
      grouped_data[key] ||= {}
      grouped_data[key][record.period] = record
    end

    grouped_data.each do |(design_id, country), records_by_period|
      valid_periods = []
    
      PERIOD_WEIGHTS.each do |period, weight|
        record = records_by_period[period]
        next unless record
    
        rpv = compute_rpv(record)
        sales = record.sales.to_f
        next if rpv.nil? || rpv < 0 || sales < 0
    
        valid_periods << { rpv: rpv, sales: sales, weight: weight }
      end
    
      if valid_periods.size < 2
        puts "[SKIP] Not enough data to normalize: Design #{design_id}, Country #{country}"
        next
      end
    
      raw_rpvs = valid_periods.map { |p| p[:rpv] }
      raw_sales = valid_periods.map { |p| p[:sales] }
    
      total_weight = valid_periods.sum { |p| p[:weight] }.to_f
      next if total_weight <= 0
    
      weighted_rpv = valid_periods.sum { |p| p[:rpv] * (p[:weight] / total_weight) }
      weighted_sales = valid_periods.sum { |p| p[:sales] * (p[:weight] / total_weight) }
    
      min_rpv = raw_rpvs.min.to_f
      max_rpv = raw_rpvs.max.to_f
      min_sales = raw_sales.min.to_f
      max_sales = raw_sales.max.to_f
    
      normalized_rpv = (max_rpv != min_rpv) ? (weighted_rpv - min_rpv) / (max_rpv - min_rpv) : 0.0
      normalized_sales = (max_sales != min_sales) ? (weighted_sales - min_sales) / (max_sales - min_sales) : 0.0
    
      if !normalized_rpv.finite? || !normalized_sales.finite? || normalized_rpv < 0 || normalized_sales < 0
        puts "[BAD SCORE] Design: #{design_id}, Country: #{country}, RPV: #{normalized_rpv}, Sales: #{normalized_sales}"
        final_score = 0.0
      else
        final_score = ((normalized_rpv * 0.5) + (normalized_sales * 0.5)).round(5)
      end
    
      score = GaRpvDesignScore.where(design_id: design_id, country: country).first_or_initialize
      score.final_score = final_score
      score.save!
    end
  end

  private
  def compute_rpv(record)
    return 0.0 if record.views.to_i == 0
    record.revenue.to_f / record.views.to_f
  end
end
