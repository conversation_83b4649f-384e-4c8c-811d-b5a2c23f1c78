class ShipconsoleCsvGenerator
    def initialize(shipconsole_orders)
      @shipconsole_orders = shipconsole_orders
      @printed_order_ids = Set.new
    end
  
    def generate
      CSV.generate(headers: true) do |csv|
        csv << headers
        @shipconsole_orders.each do |s_order|
          csv << build_row(s_order)
        end
      end
    end
  
    private
  
    def headers
      [
        "Company", "Weight", "Service", "address1", "address2", "address3", "city", "state", "postcode",
        "CountryOrTerritory", "Phone", "mail", "value", "tracking", "ref1", "quantity", "goodsdesc",
        "unitofmeasure", "tarifcode", "CountryOrTerritoryOfOrigin", "Ponumber", "Attention", "PKGType",
        "SHIPToTax", "DutyTo", "DeclaredVal", "DeclaredValAmount", "TransportationTo", "ShipmentInformation",
        "INVCurrency", "INVDeclarationStatement", "INVPaperless", "INVPrintPaperless", "INVCreateInv",
        "INVTermsOfSale", "INVReasonForExport", "GDSPartNumber", "FSISpecialInstructionsForPick",
        "PKGLength", "PKGWidth", "PKGHeight", "PKGDimUOM", "DiffSoldTO", "IORCompanyorName", "IORAddress1",
        "IORAddress2", "IORAddress3", "IORCityorTown", "IORStateProvinceCounty", "IORPostalCode",
        "IORCountryTerritory", "IORAttention", "IORTelephone", "IORUPSAcctNo", "IORTaxID", "TPCompanyorName",
        "TPAddress1", "TPAddress2", "TPAddress3", "TPCityorTown", "TPStateProvinceCounty", "TPPostalCode",
        "TPCountryTerritory", "TPAttention", "TPTelephone", "TPUPSAcctNo", "TPTaxID", "ShipmentOption",
        "OrderID", "SplitDuty_Tax_VAT", "DescAwb", "PaymentTransnRef", "IGSTValue", "PerPCWgt", "PCKCount",
        "PCKGBoxNo", "ShipToAccessPoint", "APNotificationType", "APEmailAddress", "APLanguage",
        "APFailedEmailAddress", "APCompanyName", "APAttention", "APAdr1", "APAdr2", "APAdr3", "APcity",
        "APstate", "APpostcode", "APcountryTerriority", "APPhone", "APID", "APUpsAccount",
        "DeliveryConfirmation", "AdultSignature", "GSTPer", "IOSS_num_Vendor_Collect_ID", "IOSS_Exempt",
        "Shipping_To_Business_Consumer", "Value_Less_than_eur150", "Profile"
      ]
    end
  
    def build_row(s_order)
      state = State.get_state_code(s_order.order.buyer_state, s_order.order.country)
      weight = @printed_order_ids.include?(s_order.order.id) ? 0 : s_order.order.actual_weight
      @printed_order_ids.add(s_order.order.id)
  
      [
        s_order.order.name, weight, 'SV', s_order.order.street.to_s.gsub(',', '').gsub(/\s+/, ' ').strip, "", "", s_order.order.city,
        state, s_order.order.pincode, s_order.order.country_code, s_order.order.phone, s_order.order.email,
        s_order.line_item.fetch_invoice_details, "", "", s_order.line_item.quantity,
        s_order.line_item.design.title, "PCS", s_order.line_item.design.categories.hsn_code, 'india',
        s_order.order.get_invoice_number, s_order.order.name, "CP", "", "REC", "N", "", "SHP", "",
        s_order.order.currency_code,
        "I hereby certify that the information on this invoice is true and correct and the contents and value of this shipment is as stated above.",
        "Y", "N", "Y", "FOB", "SALE", "", "", "", "", "", "", "N", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "","","","", s_order.order.number, "N",
        s_order.line_item.design.designable_type, "", "", weight, 1, 1, "N", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "N", "N", "", "", "", "", "", ""
      ]
    end
  end
  