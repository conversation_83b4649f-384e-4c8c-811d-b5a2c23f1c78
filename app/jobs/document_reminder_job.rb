class DocumentReminderJob
  include Sidekiq::Worker
  sidekiq_options retry: false, lock: :until_executed, on_conflict: :reject
  def perform
    orders_scope = Order.where(country: 'South Africa', state:['sane','ready_for_dispatch','partial_dispatch'])
    user_ids_with_national_id = KycDocument.where(name: 'National ID').distinct.pluck(:user_id)
    orders_scope.where.not(user_id:user_ids_with_national_id).includes(:user).find_each(batch_size: 100) do |order|
      next unless order.user.present?
      OrderMailer.send_document_reminder_mail(order.id).deliver_now
    end
  end
end
