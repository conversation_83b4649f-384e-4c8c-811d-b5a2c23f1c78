class BulkStateMarkCompleteJob
  include Sidekiq::Worker
  sidekiq_options queue: 'critical', retry: false

  def perform(csv_content, selected_ids)
    selected_ids = selected_ids.map(&:to_i)
    summary = {
      completed_orders: [],
      not_found_orders: [],
      not_dispatched_orders: [],
      no_matching_designer_orders: [],
      designer_orders_not_dispatched: []
    }

    csv = CSV.parse(csv_content, headers: false)
    order_numbers = csv.map { |row| row[0].to_s.strip }.reject(&:blank?)
    return if order_numbers.blank?

    orders = Order.where(number: order_numbers).includes(designer_orders: :designer)
    summary[:not_found_orders] = order_numbers - orders.map(&:number)

    orders.each do |order|
      if order.state != 'dispatched'
        summary[:not_dispatched_orders] << order.number
        next
      end

      valid_d_orders = get_valid_designer_orders(order, selected_ids)

      if valid_d_orders.blank?
        summary[:no_matching_designer_orders] << order.number
        next
      end

      d_orders_dispatched = get_dispatched_designer_orders(valid_d_orders)

      if d_orders_dispatched.blank?
        summary[:designer_orders_not_dispatched] << order.number
        next
      end

      update_designer_orders(d_orders_dispatched)
      summary[:completed_orders] << order.number
    end
    send_summary_report(summary)
  end

  private
  def get_valid_designer_orders(order, selected_ids)
    order.designer_orders.select { |d_order| selected_ids.include?(d_order.designer_id) }
  end

  def get_dispatched_designer_orders(valid_designer_orders)
    valid_designer_orders.select { |d_order| d_order.state == 'dispatched' }
  end

  def update_designer_orders(designer_orders_dispatched)
    DesignerOrder.where(id: designer_orders_dispatched.map(&:id)).update_all(state: 'completed')
  end

    def send_summary_report(summary)
    MirrawAdminMailer.designer_order_state_mark_email(summary).deliver_now
  end
end
