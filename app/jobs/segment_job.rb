# app/jobs/segment_job.rb
class SegmentJob
    include Sidekiq::Worker
    sidekiq_options retry: 5
  
    def perform(user_id)
      user = User.find_by(id: user_id)
      
      return if user.nil? || !user.orders.exists?
      begin
        UserSegmentService.new(user).evaluate
      rescue StandardError => e
        ExceptionNotify.sidekiq_delay.notify_exceptions("[SegmentJob] Failed to evaluate segment for user_id: #{user_id}", e.message, {error: e})
      end
    end
end
  