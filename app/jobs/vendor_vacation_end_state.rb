class VendorVacationEndState
    include Sidekiq::Worker
    sidekiq_options retry: 2
    def perform
        Designer.vacation.where("DATE(vacation_end_date) = ?", Date.today).find_each do |designer|
            designer.dissatisfied
        end
        Designer.vacation.where("vacation_end_date BETWEEN ? AND ?", Date.today, Date.today + 7.days).find_each do |designer|
            designer.designs.on_hold.update_all(state: 'in_stock')
        end
          
    end
end