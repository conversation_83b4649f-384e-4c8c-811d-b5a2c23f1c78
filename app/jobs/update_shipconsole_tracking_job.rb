class UpdateShipconsoleTrackingJob
    include Sidekiq::Worker
    sidekiq_options retry: false
    def perform(email, csv_file_path)
        begin

            service = ShipconsoleTrackingService.new(email, csv_file_path)
            service.run
        rescue => error
            ExceptionNotify.sidekiq_delay.notify_exceptions('Upload Tracking Error', 'Tracking issue', { error: error.inspect })
        end
    
    end
end