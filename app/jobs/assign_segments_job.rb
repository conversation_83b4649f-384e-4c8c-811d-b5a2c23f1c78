# app/jobs/assign_segments_job.rb
class AssignSegmentsJob 
  include Sidekiq::Worker
  sidekiq_options queue: 'default', retry: false
  
    def perform
      User.find_in_batches(batch_size: 100) do |users|
        ids = users.map(&:id)
        Sidekiq::Client.push_bulk('class' => SegmentJob, 'args' => ids.map { |id| [id] })
      end
    rescue StandardError => e
      ExceptionNotify.sidekiq_delay.notify_exceptions('AssignSegmentsJob Error', e.message, {error: e})
    end
  end
  