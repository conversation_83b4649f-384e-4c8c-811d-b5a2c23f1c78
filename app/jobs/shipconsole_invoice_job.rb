class ShipconsoleInvoiceJob
    include Sidekiq::Worker
    sidekiq_options retry: false
    def perform(email, e_invoice_file_path)
        begin
            service = ProcessShipconsoleInvoice.new(email, e_invoice_file_path)
            service.run
        rescue => error
            ExceptionNotify.sidekiq_delay.notify_exceptions('Upload Invoice Error', 'Invoice issue', { error: error.inspect })
        end
    
    end
end