class SendoutBackInStockNotificationJob
    include Sidekiq::Worker
    sidekiq_options retry: false
    def perform
        pending_requests = Reqlist.get_pending_reqlist.valid_reqlist_emails
        
        pending_requests.each do |request|
            design = Design.includes(:variants).find_by(id: request.design)
            if design.present? && design.in_stock?
                if (design.variants.blank? && design.quantity.to_i > 0) || design.variants_quantity > 0
                    FollowMailer.sidekiq_delay.sendout_back_in_stock_notification(request.email, design.id)
                    request.notified = 'Y'
                    request.notification_count += 1
                    request.notified_at = DateTime.now
                    request.save
                end
            end
        end
    end
end
