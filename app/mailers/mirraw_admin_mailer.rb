class MirrawAdminMailer < ActionMailer::Base
    default from: '<EMAIL>'
    def failed_order_notification(body)
        @content = body 
        mail_params = {
            to: ACCESSIBLE_EMAIL_ID['cod_orders_panel'],
            subject: "Re <> Failed orders while update"
        }
        mail(mail_params)
    end

    def send_updated_data_status(subject,body)
        @content = body 
        mail_params = {
            to: ACCESSIBLE_EMAIL_ID['discount_update_notifier'],
            subject: "Re <> #{subject}"
        }
        mail(mail_params)
    end

    def help_center_query_notification_email(subject, body, from_email)
        @content = body
        mail_params = {
          to: '<EMAIL>', 
          subject: subject,
          from: from_email 
        }
        mail(mail_params)
    end
    
    def send_payload_and_res(payload,response)
        @payload = payload
        @response = response
        mail_params = {
            to: ACCESSIBLE_EMAIL_ID['mail_payload'],
            subject: "Re <>  payload and response"
        }
        mail(mail_params) 
    end

    def processing_skus_notification(deletion_info)
        @deletion_info = deletion_info
        mail_params = {
            to: deletion_info[:deleted_by],
            subject: "Re <> Processing SKUs for batch #{deletion_info[:batch_id]}"
        }
        mail(mail_params)
    end

    def send_progress_notification(body)
        @content = body 
        mail_params = {
            to: ACCESSIBLE_EMAIL_ID['manual_grading_panel_access'],
            subject: "Re <> Design Grading Status"
        }
        mail(mail_params)
    end
    
    def design_update_status_email(update_status)
        @update_status = update_status
        mail(to: ACCESSIBLE_EMAIL_ID['designs_bulk_update_notifier'], subject: "Design Update Report")
    end

    def designer_order_state_mark_email(summary)
        @summary = summary
        mail(to: ACCESSIBLE_EMAIL_ID['designer_order_state_mark_mails'], subject: "Designer Order State Mark Update Report")
    end

    def failed_shipconsole_order_update(missed_orders, email, subject)
        @orders = missed_orders
        @subject = subject
        mail_params = {
            to: email,
            subject: @subject
        }
    end

    def send_updated_doc(email, doc_path, subject)
        filename = File.basename(doc_path)
        attachments[filename] = File.read(doc_path)
        mail(to: email, subject: subject)
    end
end
