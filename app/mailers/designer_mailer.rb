class DesignerMailer < ActionMailer::Base
  track user: lambda { Account.find_by email: message.to.try(:first) }

  def not_logged_in_since_week(designer)
    @designer = designer

    subject = "We Haven't seen you recently. Please update your stock."

    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"

    mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject)
  end

  def applied_additional_discount(designer)
    @designer = designer
    subject = "Congratulations! Your discount is active"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"
    @design_data = (@designer.designs.where('discount_percent > 5').first || @designer.designs.first || Design.first).generate_additional_discount_data(@designer[:additional_discount_percent])
    mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject, :bcc => "<EMAIL>")
  end

  def create_new_coupon_code(designer)
    @designer = designer

    subject = "Start using Coupon Codes to get more sales."

    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"

    mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject)
  end

  def faq_mail_to_designer(designer,faq)
    @designer = designer
    @faq      = faq
    mail(from: "Mirraw.com <<EMAIL>>",to: designer.email, subject: faq.question)
  end

  def welcome_to_mirraw(designer)
    @designer = designer

    subject = "Welcome Onboard - Mirraw.com"

    from_email_with_name = "Mirraw.com <<EMAIL>>"

    bcc = '<EMAIL>'

    mail(:from => from_email_with_name, bcc: bcc, :to => @designer.email, :subject => subject)
  end

  def only_few_designs_uploaded(designer_id, design_count)
    @designer = Designer.find designer_id
    if design_count == 0
      subject = 'Your boutique does not have any published designs.'
    else
      subject = "Only #{design_count} designs published in your Mirraw boutique. "
    end

    from_email_with_name = "Mirraw.com <<EMAIL>>"

    mail(:from => from_email_with_name, :to => @designer.email, :subject => subject)

  end
  def alert_profile_incomplete(designer_id)
    @designer = Designer.find designer_id
    subject = "Please complete your profile"

    from_email_with_name = "Mirraw.com <<EMAIL>>"

    mail(:from => from_email_with_name, :to => @designer.email, :subject => subject)

  end
  
  def return_count_above_10(designer, array)
    @designer = designer
    @array = array
    subject = "High return leads to low revenue"

    from_email_with_name = "Mirraw.com <<EMAIL>>"

    mail(:from => from_email_with_name, :to => @designer.email, :subject => subject)
  end

  def revenue_products_less(designer, design_count)
    @designer = designer
    @designs = design_count
    subject = "More the products, better the sales."

    from_email_with_name = "Mirraw.com <<EMAIL>>"

    mail(:from => from_email_with_name, :to => @designer.email, :subject => subject)
  end

  def revenue_not_uploaded_months(designer)
    @designer = designer

    subject = "Upload whats NEW"

    from_email_with_name = "Mirraw.com <<EMAIL>>"

    mail(:from => from_email_with_name, :to => @designer.email, :subject => subject)
  end
  
  def best_sellrs_oos_los(designer, designs)
    @designer = designer
    @designs = designs
    subject = "Best Seller Products of Mirraw"

    from_email_with_name = "Mirraw.com <<EMAIL>>"

    mail(:from => from_email_with_name, :to => @designer.email, :subject => subject)
  end

  def sold_out_notification(designer_id,design_ids,variant_ids)
    @designer = Designer.find_by_id designer_id
    @designs = @designer.designs.where(id: design_ids).preload(:images)
    @variants = @designer.variants.where(id: variant_ids).preload(option_type_values: :option_type,design: :images)
    if @designs.present? || @variants.present?
      subject = "Out of stock products list"
      from_email_with_name = "Mirraw.com <<EMAIL>>"
      mail(from: from_email_with_name, to: @designer.email, subject: subject)
    end
  end

  def in_stock_notification(designer_id, design_ids)
    @designer = Designer.find_by_id designer_id
    @designs = @designer.designs.where(id: design_ids).preload(:master_img)
    mail(from: "Mirraw.com <<EMAIL>>", to: @designer.email, subject: 'In Stock Product List')
  end


  def explain_gharpay(designer)
    @designer = designer
    subject = "Cash Before Delivery via Gharpay"

    from_email_with_name = "Mirraw.com <<EMAIL>>"

    mail(:from => from_email_with_name, :to => @designer.email, :subject => subject)

  end

  def inventory_report(designer_email, designer_name, received, not_received)
    subject = 'Inventory Report'
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = designer_name + " <" + designer_email + ">"

    @received = received
    @not_received = not_received
    mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject)
  end

  def product_received_issue(item, issue, rtv_reason = nil)
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    @order_number = item.designer_order.order.number
    @design = item.design
    @designer = @design.designer
    @issue = issue
    @rtv_reason = rtv_reason
    @qc_fail = item.quality_check_values.last
    to_email_with_name = @designer.name + " <" + @designer.email + ">"
    case issue
    when 'damaged'
      subject = 'Notification regarding a damaged delivery'
    when 'wrong_received'
      subject = 'Notification regarding a wrong received product'
    when 'QC Failed'
      subject = 'Notification regarding a quality check failed in received product'
    end
    mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject)
  end

  def sendout_design_error_notification(email, design_errors, subject = "Stock Update Failed. Designs could not be saved !", body = nil)
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    @design_errors = design_errors
    @body          = body || 'Following designs could not be saved due the reasons mentioned below :'
    mail(:from => from_email_with_name,
         :to => email,
         :subject => subject)
  end

  def monthly_orders_report(designer)
  @designer = designer

    subject = "Monthly Report"

    from_email_with_name = "Mirraw.com <<EMAIL>>"

    mail(:from => from_email_with_name, :to => @designer.email, :subject => subject)
  end

  def monthly_payout_invoice(commission_invoice, filename)    
    
    @designer = commission_invoice.designer
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    subject = "Commission Invoice"
    attachments[filename] = open(commission_invoice.invoice_url).read
    
    mail(from:  from_email_with_name,
     to:   @designer.email,
     subject:  subject
    )

  end

  def paid_payout_report(email, file_url)
    @file_url = file_url
    subject = "Paid Payout Report"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    mail(to: email, subject: subject, from: from_email_with_name)
  end
  
  def purchase_report_to_vendor(email, file_url, purchase_month)
    attachments["Purchase_Report_#{purchase_month}.csv"] = open(file_url).read
    @purchase_month = purchase_month
    subject = "Purchase Report"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    mail(to: email, subject: subject, from: from_email_with_name)
  end 

  def missing_designs_email(missing_designs, user_email)
    @missing_designs = missing_designs
    mail(from: '<EMAIL>', to: user_email, subject: 'Error while video uploading')
  end

  def send_progress_notification(body,email)
    @content = body 
    mail_params = {
      from: '<EMAIL>',
      to: email,
      subject: "Re <> Design Video Upload Status"
    }
    mail(mail_params)
  end
  
  def success_notification_for_video(successful_designs, user_email)
    @designs = successful_designs
    mail(from: '<EMAIL>', to: user_email, subject: 'Video Uploaded Successfully')
  end
  
  def mail_label_to_designer(designer_order)
    to_with_name = "#{designer_order.designer.email}"
    order_no = designer_order.order.number
    @shipment = designer_order.shipment
    subject = "Invoice and Label for Order No. #{order_no}"
    if @shipment.present?
      if @shipment.invoice_updated_at.present?
        attachments["#{order_no}_invoice.pdf"] = open(@shipment.invoice.url).read
      end
      if @shipment.label_updated_at.present?
        attachments["#{order_no}_label.pdf"] = open(@shipment.label.url).read
      elsif @shipment.tofrom_label.updated_at.present?
        attachments["#{order_no}_tofrom_label.pdf"] = open(@shipment.tofrom_label.url).read
      end
      if @shipment.return_label_updated_at.present?
        attachments["#{order_no}_return_label.pdf"] = open(@shipment.return_label.url).read
      end
      from_email_with_name = "Mirraw.com <<EMAIL>>"
      mail(from: from_email_with_name,to: to_with_name,subject: subject)
    end
  end

  def alert_invoice_rejected(designer_id,invoice_id)
    @designer = Designer.find_by_id designer_id
    @invoice  = DesignerInvoice.find_by_id invoice_id
    subject = "Important Notification :- Vendor Payout Export Order Invoice Rejected"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"
    mail(:from => from_email_with_name, :to => @designer.email, :subject => subject)
  end

  def send_qc_images_to_designer(designer_issue_id, order_id, account_id, uploaded_images)
    order = Order.where(id: order_id).first
    designer_issue = DesignerIssue.where(id: designer_issue_id).first
    account = Account.where(id: account_id).first
    designer_order = designer_issue.designer_order.presence || order.designer_orders.where(designer_id: designer_issue.designer_id).first
    designer_order.add_notes_without_callback('Sent QC fail images','other',account)
    @designer = designer_order.designer
    @notes = designer_issue.notes
    designer_order.add_notes_without_callback("RTV Reason : #{@notes}", 'QC', account)
    subject = "QC fail images for Order No. #{order.number}"
    to_email_with_name = "\"#{@designer.name}\" <#{@designer.email}>"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    attachments["QC_Fail_image1.#{designer_issue.qc_issue_image1.content_type[6..-1]}"] = HTTParty.get(designer_issue.qc_issue_image1.url).body if designer_issue.qc_issue_image1.present?
    attachments["QC_Fail_image2.#{designer_issue.qc_issue_image2.content_type[6..-1]}"] = HTTParty.get(designer_issue.qc_issue_image2.url).body if uploaded_images[0]
    attachments["QC_Fail_image3.#{designer_issue.qc_issue_image3.content_type[6..-1]}"] = HTTParty.get(designer_issue.qc_issue_image3.url).body if uploaded_images[1]
    mail(from: from_email_with_name, to: @designer.email, subject: subject,cc: '<EMAIL>')
  end

  def send_nps_feedback_to_designer(designer_id, design_id)
    @designer = Designer.where(id: designer_id).first
    @design = Design.where(id: design_id).first
    subject = "Low Rating :- Design has been rated low"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"
    mail(from: from_email_with_name, to: @designer.email, subject: subject)
  end

  def send_nps_feedback_to_designer_weekly(designer_id)
    @designer = Designer.where(id: designer_id).first
    @quality_products = @designer.quality_of_designs
    @result = @designer.get_reviews_of_design(Date.today - 7.day, Date.today)
    subject = "Check your weekly review report"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"
    mail(from: from_email_with_name, to: @designer.email, subject: subject)
  end

  def designer_order_critical_mail_to_vendor(designer_id, order_number)
    @designer = Designer.where(id: designer_id).first
    @order_number = order_number
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"
    subject = "Order #{@order_number} marked as critical"
    mail(from: from_email_with_name, to: @designer.email, cc: '<EMAIL>' ,subject: subject)
  end

  def mail_no_pickup_done_report_to_operation(current_account, csv)
    @current_account = current_account
    mail_recipient = ACCESSIBLE_EMAIL_ID['no_pickup_done_mail_recipient']
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = mail_recipient
    subject = "No pickup done report - #{current_account.email} - #{current_account.name}"
    attachments["#{@current_account.name}_no_pickup_done.csv"] = csv
    mail(from: from_email_with_name, to: to_email_with_name, subject: subject)
  end

  def designer_order_vendor_cancel_to_vendor(designer_id, order_number)
    @designer = Designer.where(id: designer_id).first
    @order_number = order_number
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"
    subject = "Delayed Dispatch - #{@order_number}"
    mail(from: from_email_with_name, to: @designer.email, cc: '<EMAIL>' ,subject: subject)
  end 

  def designer_first_order(designer)
    @designer = designer
    subject = 'Mirraw.com : Order processing guidelines.'
    mail(from: "Mirraw.com <<EMAIL>>",to: designer.email, subject: subject, bcc:'<EMAIL>')
  end

  def mail_banned_warning_to_designer(designer, banned = false)
    @designer = designer
    subject = banned ? 'Account Suspended' : "High Order Defect Rate (ODR) Alert. Current ODR: #{(@designer.current_metric_values.designer_visible.dashboard.current_odr.value * 100).round(2)}"
    from_email_with_name = 'Mirraw.com <<EMAIL>>'
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"
    bcc_emails = ['<EMAIL>', '<EMAIL>'] + ACCESSIBLE_EMAIL_ID['business_stakeholders_emails']
    template = banned ? 'mail_banned_message_to_designer' : 'mail_banned_warning_to_designer'
    mail(from: from_email_with_name, to: to_email_with_name, bcc: bcc_emails, subject: subject, template_name: template)  
  end

  def send_alert_on_vendors_going_vacation(vendors)
    @vendors           = vendors
    mail(to: Account.joins(:role).where(role: {name: ['vendor_team','senior_vendor_team','listing']}).pluck(:email),
         from: "Mirraw.com <<EMAIL>>",
         cc: '<EMAIL>',
         subject: "List of Vendors went on vacation on #{Date.yesterday.strftime('%d %b %Y')}")
  end

  def send_alert_on_bestsellers_oos(file, design_count)
    @design_count           = design_count
    attachments["List of Bestsellers went oos on #{Date.yesterday}.csv"] = File.read(file)
    mail(to: Account.joins(:role).where(role: {name: ['vendor_team','senior_vendor_team','listing']}).pluck(:email),
         from: "Mirraw.com <<EMAIL>>",
         cc: '<EMAIL>',
         subject: "List of Bestsellers went out of stock on #{Date.yesterday.strftime('%d %b %Y')}")
  end

  def send_alert_on_reducing_inventory(file, vendor_count)
    @vendor_count           = vendor_count
    attachments["List of Vendors with reduced inventory #{Date.yesterday}.csv"] = File.read(file)
    mail(to: Account.joins(:role).where(role: {name: ['vendor_team','senior_vendor_team','listing']}).pluck(:email),
         from: "Mirraw.com <<EMAIL>>",
         cc: '<EMAIL>',
         subject: "List of Vendors whose inventory got reduced on #{Date.yesterday}")
  end

  def send_alert_on_new_inventory_upload(file, count_designable_wise)
    @count_designable_wise = count_designable_wise
    attachments["List of New Designs uploaded #{Date.yesterday}.csv"] = File.read(file)
    mail(to: Account.joins(:role).where(role: {name: ['vendor_team','senior_vendor_team','listing']}).pluck(:email),
         from: "Mirraw.com <<EMAIL>>",
         cc: '<EMAIL>',
         subject: "List of New Designs that were uploaded and came in_stock yesterday on #{Date.yesterday}")
  end

  def send_alert_on_new_vendor_approval(new_vendors)
    @vendors           = new_vendors
    mail(to: Account.joins(:role).where(role: {name: ['vendor_team','senior_vendor_team','listing']}).pluck(:email),
         from: "Mirraw.com <<EMAIL>>",
         cc: '<EMAIL>',
         subject: "List of New Vendors approved on #{Date.yesterday.strftime('%d %b %Y')}")
  end

  def send_rtv_product_dispatched_notification(rtv_shipment, designer, order_number, rtv_product_ids)
    @designer, @order_number, @rtv_product_ids, @rtv_shipment = designer, order_number, rtv_product_ids, rtv_shipment
    subject = "QC Failed Product Update For #{order_number}"
    from_email_with_name = 'Mirraw.com <<EMAIL>>'
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"    
    mail(from: from_email_with_name, to: to_email_with_name, cc: '<EMAIL>', subject: subject)  
  end

  def inactive_designer_alert(designer)
    @designer = designer
    @last_signed_in = Date.current.mjd - designer.last_signed_in.to_date.mjd
    subject = 'Inactive Alert'
    from_email_with_name = 'Mirraw.com <<EMAIL>>'
    to_email_with_name = "#{@designer.name} <#{@designer.email}>"
    bcc = ['<EMAIL>'] + ACCESSIBLE_EMAIL_ID['business_stakeholders_emails']
    mail(from: from_email_with_name, to: to_email_with_name, bcc: bcc , subject: subject)
  end

  def send_low_rating_rejected_design_notification(mail_data)
    @mail_data = mail_data
    subject = 'Design rejected due to low rating'
    from_email_with_name = 'Mirraw.com <<EMAIL>>'
    to_email_with_name = "#{mail_data[:name]} <#{mail_data[:email]}>"
    bcc_emails = ['<EMAIL>', '<EMAIL>'] + ACCESSIBLE_EMAIL_ID['business_stakeholders_emails']
    mail(from: from_email_with_name, to: to_email_with_name, bcc: bcc_emails, subject: subject)
  end

  def mail_pending_orders_followup(designers_pending_data)
    @designers_pending_data = designers_pending_data
    subject = "Pending Orders For More Than #{THRESHOLD_PENDING_DAYS} Days"
    from_email_with_name = 'Mirraw.com <<EMAIL>>'
    to_email_with_name = "#{designers_pending_data.designer_name} <#{designers_pending_data.designer_email}>"
    mail(from: from_email_with_name, to: to_email_with_name, cc: '<EMAIL>', subject: subject)
  end

  def designers_commission_rate_update_status(data,failed_designers,email,failed_designs_id=nil)
    @updated_designers = data.keys - failed_designers.keys
    @failed_designers = failed_designers
    @failed_designs_id = failed_designs_id
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    subject = 'Commission Report Status'
    mail(from: from_email_with_name, to:email, cc: ['<EMAIL>'], subject: subject)
  end

  def sla_violation_issue_mail(designer_order_id)
    @designer_order = DesignerOrder.find_by(id: designer_order_id)
    @designer = @designer_order.designer
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    @order_number = @designer_order.order.number
    to_email_with_name = @designer.name + " <" + @designer.email + ">"
    subject = "Notification regarding #{@order_number} order cancel due to SLA violation"
    mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject)
  end

  def pick_up_confusion(designer_orders)
    mail({
      from: 'Mirraw.com <<EMAIL>>',
      to: designer_orders.first["designer_email"],
      cc: LATE_PICKED_UP_DOMESTIC_ORDERS['cc_emails'],
      subject: 'Orders picked up but not dispatched.'
    }) do |format|
      format.html do
        render locals: {
          designer_orders: designer_orders
        }
      end
    end
  end

  def rate_criteria_suspension(designer_id, rate_type:, consequence:, subject:, metric_date:, locals: {})
    designer = Designer.eager_load(metric_values: :metric_definition).where("metric_values.generated_on" => metric_date).find(designer_id)
    metric_value = designer.metric_values.first
    metric_definition = metric_value.metric_definition
    rate_threshold = DESIGNER_METRIC_CONFIG[rate_type][consequence]["thresholds"]["metric_value"]

    mail({
      to: designer.email,
      from: '<EMAIL>',
      subject: subject
    }) do |format|
      format.html do
        render("designer_mailer/status_info/#{rate_type}_rate_criteria_#{consequence}", {
          layout: 'designer_mailer/status_info',
          locals: {
            name: designer.name,
            "#{rate_type}_rate_threshold".to_sym => rate_threshold,
            "current_#{rate_type}_rate".to_sym => metric_value.value.round(2)
          }.merge(locals.each_with_object({}) { |(local, calling), memo| memo[local] = metric_definition.public_send(calling) })
        })
      end
    end
  end

  def warehouse_order_recieved(warehouse_order_id)
    if @warehouse_order = WarehouseOrder.find_by_id(warehouse_order_id)
      @designer = @warehouse_order.designer
      from_email_with_name = "Mirraw.com <<EMAIL>>"
      to_email_with_name = @designer.name + " <" + @designer.email + ">"
      subject = "Warehouse Order #{@warehouse_order.number} has been received"
      mail(:from => from_email_with_name, :to => to_email_with_name, :subject => subject)
    end
  end

  def dedup_report(success_designs, failed_designs)
    @success_designs = DesignCluster.where(design_id: success_designs).pluck(:design_id, :winner_design_id).to_h
    @failed_designs  = failed_designs
    mail(from: '<EMAIL>', to: Account.joins(:role).where(role: {name: ['senior_vendor_team','listing']}).pluck(:email), subject: "Dedup Report - #{Date.today}")
  end

  def e_invoice_report(public_url, designer)
    @public_url = public_url
    @designer = designer
    self.ahoy_options = AhoyEmail.default_options.merge({click: false, message: false})
    mail(from: "Mirraw.com <<EMAIL>>", to: designer.email, subject: "E-Invoice Report for #{Date.yesterday}")
    #mail(from: "Mirraw.com <<EMAIL>>", to: "<EMAIL>", cc: "<EMAIL>", subject: "E-Invoice Report for #{Date.yesterday}")
  end

  def send_design_updates_to_designer(designer,order_number,note)
    @designer,@order_number,@note = designer,order_number,note
    return unless @designer.try(:email)
    mail(from: NO_REPLY_EMAIL, to: @designer.email, subject: "Order changes Update: Order #{@order_number}")
  end
  
  def missing_designs_for_campaign(missing_designs, user_email)
    @missing_designs = missing_designs
    mail(from: '<EMAIL>', to: user_email, subject: 'Error While Uploading Campaign Discount')
  end
  
end

