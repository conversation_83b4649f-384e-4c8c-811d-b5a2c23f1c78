copyToClipboard = (elem) ->
  elem = elem[0]
  isInput = elem.tagName == 'INPUT' or elem.tagName == 'TEXTAREA'
  origSelectionStart = undefined
  origSelectionEnd = undefined
  target = elem
  origSelectionStart = elem.selectionStart
  origSelectionEnd = elem.selectionEnd
  # select the content
  currentFocus = document.activeElement
  target.focus()
  endLength = target.value.length
  target.setSelectionRange 0, endLength
  # copy the selection
  succeed = undefined
  try
    succeed = document.execCommand('copy')
  catch e
    succeed = false
  # restore original focus
  if currentFocus and typeof currentFocus.focus == 'function'
    currentFocus.focus()
  # restore prior selection
  elem.setSelectionRange origSelectionStart, origSelectionEnd
  succeed

$ ->
  $('.copy-coupon-button').on 'click',(e) ->
    couponCode = $(this).data('code')
    succeed = copyToClipboard($('#coupon-'+couponCode))
    msg = ''
    if !succeed
      msg = 'Copy not supported or blocked.  Press Ctrl+c to copy.'
      $('#cc-'+couponCode).css({'display':'inline-block','opacity':'1'})
      target = $('#cc-'+couponCode)[0]
      target.setSelectionRange 0, target.value.length
      $('#msg-'+couponCode).css('color',' #e85e68')
    else
      msg = 'Coupon Code copied to the clipboard.'
      $('#msg-'+couponCode).css('color','#47984a')
    $('#msg-'+couponCode).html(msg).fadeIn()
    setTimeout (->
      $('#msg-'+couponCode).html('').fadeOut()
    ), 3000

$('#coupon_coupon_type').change ->
  if $(this).val() is 'FOFF'
    $('.flat_off').show()
    $('.percent_off').hide()
  else if $(this).val() is 'POFF'
    $('.flat_off').hide()
    $('.percent_off').show()
    # Show max_discount field for percent_off
    $('.percent_off .field.form-group.row:has(input[name="coupon[max_discount]"])').show()
  else
    # Hide max_discount field for other types
    $('.percent_off .field.form-group.row:has(input[name="coupon[max_discount]"])').hide()

# On page load, trigger change to set correct visibility
$(document).ready ->
  $('#coupon_coupon_type').trigger('change')

$ ->
  $('.coupon_card').on 'click',(e) ->
    data = $(this).attr('id')
    $('#coupon_type').val(data)
    $('#coupon_type_form').submit()
