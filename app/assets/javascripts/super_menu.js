$(document).ready(function() {
  $('.super-menu-link').on('click', function(e) {
    e.preventDefault();
    var superMenuId = $(this).data('id');
    
    $.ajax({
      url: '/menus/' + superMenuId + '/show_super_menu',
      type: 'GET',
      dataType: 'html',
      success: function(data) {
        $('#menu-list-container').html(data);
      },
      error: function(jqXHR, textStatus, errorThrown) {
        console.log('Error: ' + textStatus + ' ' + errorThrown);
      }
    });
  });
}); 