.search_container {
  display: flex;
  flex-direction: row;
}

.search_params {
  flex: 1;
  margin: 0 10px;
}

.centered-form {
  text-align: center;
  margin: 0 auto;
  width: 50%;
}

#order_numbers {
  width: 50%;
  height: 50px;
  color: black;
}

.custom-submit-button {
  width: 50%;
}

.csv_upload {
  margin-left: 43%;
}

.custom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-container .left-side-btn form {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
}

.custom-container .left-side-btn form input {
  margin: 0 10px;
  height: 40px;
}

.custom-container .left-side-btn form select {
  margin-right: 20px;
}

.custom-container .left-side-btn form .btn.btn-primary {
  width: 100%;
  max-width: 100px;
}

.custom-container .right-side-btn form {
  display: flex;
}

.export-btn.btn.btn-primary {
  width: 100%;
  max-width: 117px;
  margin: 15px 0 0 auto;
  height: 35px;
  position: absolute;
  right: 0;
  top: 0;
}

.right-side-btn .btn.btn-primary:nth-child(1) {
  width: 100%;
  max-width: 110px;
}

.custom-data-table {
  width: 100%;
  margin: 30px 0 50px;
  border-top: 1px solid aliceblue;
  padding: 0;
  position: relative;
  z-index: 1;

}

.custom-data-table thead th {
  padding: 10px 8px;
  font-size: 14px;
  font-weight: bold;
  border: 1px solid;
  position: sticky;
  top: 0;
  background: #000;
}
.custom-data-table thead .img-box {
  width: 7%;
}
.custom-data-table thead .list-item {
  width: 10%;
}
.custom-data-table tbody td {
  font-size: 16px;
  padding: 10px;
  border: 1px solid;
}

.custom-data-table tr {
  border: 1px solid;
}

.custom-table {
  border-top: 1px solid #fff;
  margin-top: 20px;
  position: relative;
  ul {
    padding: 0;
    list-style: none;
    font-size: 13px;
  }
}

.custom-data-table tbody td img {
  width: 100%;
  height: 105px;
}

.custom-table .pagination {
  position: absolute;
  bottom: -54px;
  right: 0;
}

.custom-table .pagination .previous_page.disabled {
  color: #777;
  padding: 5px 10px;
  font-size: 16px;
  font-weight: 600;
}

.custom-table .pagination .current {
  background: #428bca;
  padding: 5px 10px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 100px;
}

.custom-table .pagination a {
  color: #fff;
  font-size: 16px;
  padding: 5px 10px;
}

.div-input-field1 {
  display: flex;
  justify-content: space-between;
  width: 80%;
}

.div-input-field2 {
  display: flex;
  margin-top: 25px;
  align-items: center;
}

.div-input-field2 .input1 {
  margin-right: 30px;
}

.leftside-btn {
  width: 100%;
}

.div-input-field {
  position: relative;
}

input.btn.btn-primary.submit-data {
  width: 100%;
  max-width: 165px;
  padding: 10px 0;
  height: 10%;
}

.div-input-field label {
  margin-right: 5px;
}

input#designer_id,
input#design_id,
input#end_date,
input#start_date,
input#designer_name {
  height: 35px;
  padding: 0 10px;
  color: #000
}
.design_block {
  display: flex;
  width: 100%;
  align-items: center;
  padding: 20px 0;
}
.design_block textarea#design_ids {
  width: 100%;
  max-width: 400px;
  height: 35px;
}
.design_block label {
  font-size: 16px;
  margin-right: 20px;
}
.design_block .btn-success {
  padding: 10px 50px;
  font-size: 16px;
  font-weight: 600;
}
@media screen and (max-width:1499px) {
  .custom-data-table thead th {
    padding: 8px 50px;
  }
  
}
.input1 {
  display: flex;
  flex-direction: column;
}

span.next_page.disabled {
  color: #777;
}
.main-class {
  display: flex;
  justify-content: space-between;
}

form.main-form {
  width: 30%;
}
/* campaign page css */
.campaign_page
.new_seller_button {
  display: flex;
  justify-content: end;
  margin: 20px 0 35px;
}
.campaign_page .participation {
  margin-bottom: 60px;
}
.campaign_page
.table-responsive,th,td{
  border-bottom: none;
}
.campaign_page
.table-responsive td{
  border: 1px solid;
  font-size: 16px;
  padding: 15px 10px;
}
.campaign_page .btn-success {
  padding: 10px 25px;
  font-size: 16px;
}
.campaign_page .table-responsive td:last-child {
  text-align: center;
}
.campaign_page
.table-responsive th {
  font-size: 18px;
  text-align: center;
  border: 1px solid;
}
.campaign_page .btn-sm {
  padding: 5px 15px;
  font-size: 13px;
  line-height: 1.5;
  border-radius: 3px;
  font-weight: bold;
}
.campaign_page
.table-responsive table{
  margin-bottom: 0;
}
.campaign_page
.table-responsive table{
  border: 1px solid #fff;
}
.campaign_page .pagination {
  display: flex;
  justify-content: center;
}
.campaign_page .pagination a{
  padding: 0 5px;
  color: #fff;
}
.campaign_page .pagination a .current {
  color: #fff;
  padding-left: 5px;
  font-weight: 600;
}
.campaign_page .campaign_header {
  font-size: 40px;
  font-weight: bold;
  position: relative;
}
.campaign_page em.current {
  color: #428bca;
  padding: 0 5px;
}
.campaign_page .campaign_header:after{
  position: absolute;
  content: "";
  height: 1px;
  width: 250px;
  background: #fff;
  bottom: -8px;
  left: 0;
}
.campaign_participants {
  text-align: center;
}
.campaign_page 
.table-responsive th:first-child {
  width: 10%;
}
.campaign_page .filters {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 40px 0;
}
.seller_admin_portal .pagination {
  display: flex;
  justify-content: center;
  a {
    padding: 0 5px;
}
em.current {
  color: maroon;
  padding: 0 5px;
  font-weight: 600;
}
}
.date_filter {
  margin-bottom: 25px;
  text-align: end;
  .form-group {
    margin-right: 10px;
}
input.btn.btn-primary {
  width: 100%;
  max-width: 100px;
  margin: 0;
}
 input#end_date, input#start_date {
  width: 100%;
  max-width: 125px;
}
}
.page-title .title_left h4 {
  font-size: 24px;
  padding: 20px 0;
  margin: 0 0 0 30px;
}
.particate_msg p {
  font-size: 16px;
  text-align: center;
}
.qc-rate-form {
  #designer_ids {
    resize: vertical;
  }

  .qc-rate-container {
    display: flex;
    flex-wrap: wrap;
  }

  .radio-group {
    display: flex;
    flex-direction: row;
    
    .radio-option {
      display: flex;
      align-items: center;
      
      input[type="radio"] {
        display: none;
        
        &:checked + label {
          background-color: #0056b3;
          border: 1px solid #dadada;
        }
      }
      
      label {
        display: inline-block;
        background-color: #4ca3ff;
        color: white;
        padding: 0.75rem 1.5rem;
        border: 1px solid #151515;
        cursor: pointer;
        font-size: 1.25rem;
        text-align: center;
        min-width: 70px;
      }
    }
  }
  
  .qc-rate-submit {
    background-color: #4ca3ff;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #0056b3;
    }
  }

  @media (max-width: 992px) {
    .qc-rate-selector {
      padding-right: 0px;
      width: auto;
    }

    .qc-rate-container {
      margin-top: 5px;
    }

    .radio-group {
      display: flex;
      flex-wrap: wrap;
    }
  }
}

.designer-table {
  .designer-search {
    margin-bottom: 5px;
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .designer-input-container {
      max-width: 50em;
      min-width: 50%;
    }

    .qc-option-container {
      min-width: 20%;
    }

    .designer-submit-container {
      .designer-submit {
        border-radius: 0px;
      }
    }
  }

  @media (max-width: 992px) {
    .designer-search {
      margin-top: 5px;
    }
  }

  @media (max-width: 768px) {
    .qc-option-container {
      min-width: 30%;
    }
  }

  @media (max-width: 580px) {
    .designer-search div {
      width: 100%;
      margin-top: 5px;
    }
  }
}

.campaign_columns {
  font-family: 'Segoe UI', Roboto, sans-serif;
}
.card-campaign {
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  background: white;
  transition: all 0.3s ease;
}
.card-campaign:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}
.card-body-campaign {
  padding: 24px;
}
.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #3b82f6;
}
.card-text {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 16px;
}
.campaign_portal_date_format {
  margin-top: 12px;
}
.date-item {
  padding: 4px 0;
}
.date-item i {
  font-size: 1rem;
  width: 20px;
  text-align: center;
}
.date-item p {
  font-size: 0.875rem;
  color: #334155;
}
.date-item strong {
  font-weight: 500;
  color: #1e293b;
}
.upload-area {
  border: 2px dashed #cbd5e1;
  background-color: #f8fafc;
  border-radius: 10px;
  transition: all 0.3s ease;
}
.upload-area:hover {
  border-color: #93c5fd;
  background-color: #f0f9ff;
}
.upload-instruction {
  font-size: 0.875rem;
  color: #1e293b;
  margin-bottom: 4px;
}
.upload-alternative {
  font-size: 0.75rem;
  margin-bottom: 8px;
}
.upload-button {
  padding: 6px 16px;
  font-size: 0.8125rem;
  border-radius: 6px;
  border: 1px solid #3b82f6;
  color: #3b82f6;
  background: white;
  transition: all 0.2s ease;
}
.upload-button:hover {
  background: #eff6ff;
}
.upload-note {
  margin-top: 12px !important;
  margin-bottom: 8px;
}
.demo-link {
  font-size: 0.75rem;
  text-decoration: none;
  transition: color 0.2s ease;
}
.demo-link:hover {
  color: #2563eb !important;
  text-decoration: underline;
}
.submit-btn, .participated-btn, .cancel-btn {
  padding: 10px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  transition: all 0.2s ease;
  text-transform: none;
  letter-spacing: normal;
}
.submit-btn {
  background: #3b82f6;
}
.submit-btn:hover {
  background: #2563eb;
}
.participated-btn {
  background: #10b981;
  font-size: 16px;
}
.participated-btn:hover {
  background: #059669;
}
.cancel-btn {
  background: #ef4444;
}
.cancel-btn:hover {
  background: #dc2626;
}
hr {
  margin: 16px 0;
  border: none;
  border-top: 1px solid #e2e8f0;
}
.date-item i.fa-calendar-alt,
.date-item i.fa-clock {
  font-size: 0.875rem;
  color: #94a3b8;
  width: 18px;
  text-align: center;
}
.date-item p.card-text {
  margin-left: 4px;
}
.date-item i:first-child {
  margin-right: 8px;
}
.date-item i:not(:first-child) {
  margin-right: 8px;
}
.calendar-icon,
.clock-icon {
  flex-shrink: 0;
}
.calendar-icon {
  width: 16px;
  height: 16px;
}
.clock-icon {
  width: 16px;
  height: 16px;
}
.date-item p.card-text {
  margin-left: 4px;
}
.date-item i:first-child {
  margin-right: 8px;
}
.date-item svg {
  margin-right: 8px;
}
.card-body-campaign {
  align-items: center;
  text-align: center;
}
.campaign_portal_card_info {
  width: 100%;
}
.campaign_portal_date_format {
  text-align: left;
  display: inline-block;
}
.date-item {
  justify-content: center;
}
.upload-area {
  margin-left: auto;
  margin-right: auto;
}
hr {
  display: none;
}
.text-center.mb-3 {
  margin-bottom: 1rem;
}
.demo-link {
  display: inline-block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  text-decoration: none;
  transition: color 0.2s ease;
}
.demo-link:hover {
  color: #2563eb !important;
  text-decoration: underline;
}
.upload-area {
  margin-bottom: 0.5rem !important;
}
.upload-area.dragover {
  background-color: #e9ecef;
  border-color: #007bff;
}
.campaign-upload-wrapper {
  width: 400px;
  margin-left: auto;
  margin-right: auto;
}
#csv_file{
  width: 180px;
  margin-bottom: 10px;
}
.upload-csv{
  display: grid;
  place-content: center;
}
.container-fluid{
  font-size: 12px;
}
.font-medium{
  margin-top: 10px;
}
.participate-campaign{
  margin-top: 10px;
  font-size: 16px;
}
.demo-link{
  font-size: 12px;
  margin-left: 250px;
}

.designer-order-bulk-state-change-form{
  .input-row {
    display: flex;
    align-items: flex-start;
    gap: 30px;
    margin-top: 5rem;
    justify-content: space-evenly;
  }

  .file-input {
    display: block;
  }

  .dropdown-box {
    position: relative;
    user-select: none;
    display: flex;
    flex-direction: column;

    p {
      margin: 0 0 0.5rem 0;
      font-weight: bold;
    }

    .dropdown-btn {
      border: 1px solid #ccc;
      background-color: #f0f0f0;
      cursor: pointer;
      height: 2rem;
      padding: 0 10px;
      line-height: 2rem;
      color: black;
      min-width: 180px;
    }

    .dropdown-content {
      color: black;
      position: absolute;
      top: 100%;
      left: 0;
      background-color: white;
      border: 1px solid #ccc;
      min-width: 180px;
      z-index: 1000;
      padding: 8px 10px;
      box-shadow: 0 2px 6px rgba(0,0,0,0.2);
      max-height: 200px;
      overflow-y: auto;

      label {
        display: flex;
        align-items: center;
        padding: 6px 0;
        cursor: pointer;
        font-size: 14px;

        input[type="checkbox"] {
          margin-right: 8px;
        }
      }
    }
  }
}
.national-id-list {
  .kyc-table {
    width: 100%;
    border-collapse: collapse;
    font-family: Arial, sans-serif;
    font-size: 14px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    th,
    td {
      border: 1px solid #ccc;
      padding: 12px 15px;
      text-align: left;
    }

    a {
      color: #007bff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
.card-body-campaign.d-flex.flex-column.text-center form{
  width: 100%;
}