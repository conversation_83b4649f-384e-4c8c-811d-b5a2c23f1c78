class LineItemsController < ApplicationController
  after_filter :update_cart_total_items
  # POST /line_items
  # POST /line_items.json
  def create
    design_ids = params[:line_items].present? ? params[:line_items].map{|index,d| d['design_id']} : params[:design_id]
    designs = Design.includes(variants: [option_type_values: :option_type]).where(id: design_ids)
    @integration_status = "new"
    @ga_line_item_array = []
    breadcrumbs_array = params[:breadcrumb].present? ? JSON.parse(params[:breadcrumb]) : []
    if designs.present?
      line_item_added, @line_items = true, []
      if params[:line_items].present?
        designs = designs.group_by(&:id)
        multi_line_item_params["line_items"].each do |index,item_param|
          design = designs[item_param['design_id'].to_i].try(:first)
          @all_addons = design.design_addons.where(design_id: design.id, addon_product_id: design_ids) if !item_param[:pair_product] && designs.length > 1
          create_line_item(design, item_param, index.to_i, breadcrumbs_array) or return
        end
      else
        design = designs.first
        index = 0
        create_line_item(design, params, index, breadcrumbs_array) or return
      end

      respond_to do |format|
        @add_to_cart = true
        if @line_items.present?
          ###############lazy load and cache_fetch
          @free_stitching_text = @cart.free_stitching_text(@country_code, @conversion_rate, @symbol)
          format.js do
            if params[:quantity].present? && params[:quantity].to_i > 1
              params[:id] = @line_items.first.id
              save_quantity and return
            else
              #total_price = @line_items.first.snapshot_price * @line_items.first.quantity
              price = @ga_line_item_array.first[:price] || 0
              #total_price = calculate_total_price(@ga_line_item_array.first, @line_items.first.quantity).round(2) if @ga_line_item_array.present?

              rate = (@line_items.first.snapshot_currency_rate).to_f
              coupon_discounts = (@line_items.first.cart.coupon_discounts/rate).to_f.round(2) if @line_items.first.cart.present? && @line_items.first.cart.coupon.present?
              item_total = (@line_items.first.cart.items_total_without_addons(rate) - @line_items.first.cart.bmgnx_discounts/rate).round(2) if @line_items.first.cart.present?
              coupon_percent = coupon_discounts.present? ? ((coupon_discounts / item_total) * 100).to_f.round(2) : 0
              price = @ga_line_item_array.first[:price] || 0
              item_customization = @ga_line_item_array.first[:item_customization] || 0
              price_without_addon = price - item_customization
              price_without_coupon = (price_without_addon /(1-(coupon_percent/100))).round(2)
              price = (price_without_coupon + item_customization).round(2)
              @ga_line_item_array.first[:price] = price
              @googe_add_hash_new = google_ads_data(@ga_line_item_array.first)
              @google_tag_params = {event: "ga4_add_to_cart", ecommerce: {  currency: "INR", value: price,country_code: @country_code, items: @ga_line_item_array, item_ids: @googe_add_hash_new[:item_ids]}}

              render :file => '/line_items/update.js', :content_type => 'text/javascript'
            end
          end
          format.html { redirect_to @cart }
          format.json { render json: @line_items, status: :created, location: @line_items.first, ga_data: @ga_line_item_array }
        else
          render nothing: true
        end
      end
    elsif @cart.line_items.present?
      respond_to do |format|
        format.js { render :file => '/line_items/update.js', :content_type => 'text/javascript'}
      end
    else
      render :nothing => true
    end
  end

  def google_ads_data(item)
    @googe_add_hash_new = {
    "item_ids":
    [{
        "id": "#{item[:item_id]}",
        "google_business_vertical": "retail"
      }],
    "content_ids": ["#{item[:item_id]}"],
    "contents": [{"id": "#{item[:item_id]}","quantity": item[:quantity]}]
    }
  end

  def create_line_item(design,item_param, index= 0,breadcrumbs_array=[])
    variant_present = item_param[:variant].present? && item_param[:variant] != 'NV'
    combo_variant_present = item_param[:combo_variant].present? && item_param[:combo_variant] != 'NV'
    addon_present = item_param[:addons].present? && item_param[:addons] != 'NV'
    variants_applicable_not_present = design.variants.present? && !variant_present
    addon_applicable_not_present = (STITCHING_ENABLE == 'true' && design.get_design_addons(@actual_country, @symbol).present?) && !addon_present
    unless (variants_applicable_not_present || addon_applicable_not_present)
      if @cart.id <= 0 # We had a dummy cart. Create cart now.
        if account_signed_in?
          email = current_account.email
        elsif session[:email].present?
          email = session[:email]
        else
          email = ''
        end
        @cart = Cart.all_rel.create(:email => email, :hash1 => SecureRandom.hex(16))
        session[:cart_id] = @cart.id
        set_user_for_cart(@cart)
        current_pe_subscriber.update_cart(@cart) if current_pe_subscriber
      end
      design.pair_product_discount = @all_addons.find{|addon| addon.addon_product_id == design.id}.try(:discount_percent).to_i if item_param[:pair_product] && @all_addons.present?
      variant = design.variants.find{|v| v.id == item_param[:variant].to_i} if variant_present#where(id: item_param[:variant]).first
      combo_variant = design.combo_variants.find{|v| v.id == item_param[:combo_variant].to_i} if combo_variant_present#where(id: item_param[:variant]).first
      line_item = @cart.add_product(design: design, variant: variant, pair_product: item_param[:pair_product], combo_variant: combo_variant)
      @cart.cart_cod_serviceable?(cookies[:pincode]) if false#cookies[:pincode].present?
      if RAKHI_SALE && item_param[:rakhi_note].present? && item_param[:rakhi_note] == 'true'
        line_item.add_note('Rakhi Schedule Delivery')
      end
      line_item.add_variant(variant) if variant.present? && line_item.variant_id.blank?
      line_item.combo_variant = combo_variant if combo_variant.present? && line_item.combo_variant_id.blank?
      line_item.add_addons(design, item_param[:addons], item_param[:standard_size]) if addon_present
      line_item.app_source = 'Desktop'.freeze
      line_item.snapshot_country_code = session[:country][:country_code]
      line_item.snapshot_currency_rate = @conversion_rate
      line_item.buy_get_free = design.buy_get_free  if PromotionPipeLine.bmgnx_hash.present?
      line_item.designable_type = design.designable_type
      line_item.category_id = design.categories.try(:first).try(:id)
      if (free_shipping_rate = Promotions.get_shipping_rate_promotion(line_item.snapshot_currency_rate.to_f, line_item.snapshot_country_code, true).to_f) > 0 && (line_item.snapshot_price / line_item.snapshot_currency_rate.to_f) > free_shipping_rate
        line_item.item_details['promotion'] = 'Free Shipping'
      end
      if line_item.save!
        @line_items.push(line_item)
        @ga_line_item_array << line_item.line_item_wise_ga_data(index, breadcrumbs_array, @country_code)
        current_pe_subscriber.product_added_to_cart if current_pe_subscriber
        gtm_data_layer.push({event: 'addToCart', productId: design.id, quantity: line_item.quantity}.merge!(@cart.details_for_gtm))
      else
        return true
      end
    else
      respond_to do |format|
        format.js { render :file => '/line_items/variants_blank.js', :content_type => 'text/javascript', locals: {design: design} and return false}
      end
    end
    return true
  end
  
  # DELETE /line_items/1
  # DELETE /line_items/1.json
  def destroy
    @line_item = @cart.line_items.find{|li| li.id == params[:id].to_i}
    if @line_item.present?
      session[:cart_upsell].sub!("--contexio-#{@line_item.design_id}", '') if session[:cart_upsell].present?
      @cart.line_items.delete(@line_item)
      if current_account.try(:user?) && params[:move_to_wishlist]
        wishlist = current_account.user.wishlists.where(design_id: @line_item.design_id).first_or_initialize
        wishlist.assign_attributes(conversion_rate: @rate, price: @line_item.design.try(:effective_price),
          country_code: @country_code, wish: true, state: 'added', app_source: 'Desktop'
        )
        wishlist.save
      end
      @remove_from_cart = true
      # @cart = Cart.all_rel.find_by_id(session[:cart_id])
      @free_stitching_text = @cart.free_stitching_text(@country_code, @conversion_rate, @symbol)
      unless @cart.new_record? && @cart.id?
        @cart.cart_cod_serviceable?(cookies[:pincode]) if cookies[:pincode].present?
        if @cart.line_items.length < 1
          @cart.cod_available = nil
          @cart.save
        end
      end
      gtm_data_layer.push({event: 'removeFromCart', productId: @line_item.design.id, quantity: @line_item.quantity}.merge!(@cart.details_for_gtm))
      # @cart_recommendations_designs = @cart.recommendations(@conversion_rate, @actual_country)
      total_price = @line_item.snapshot_price * @line_item.quantity
      line_item_hash = @line_item.line_item_wise_ga_data(0, @line_item.get_breadcrumb, @country_code)
      @ga_line_item_array = [line_item_hash]
      #total_price = calculate_total_price(@ga_line_item_array.first, @line_item.quantity) if @ga_line_item_array.present?
      price = line_item_hash[:price] || 0

      rate = (@line_item.snapshot_currency_rate).to_f
      coupon_discounts = (@line_item.cart.coupon_discounts/rate).to_f.round(2) if @line_item.cart.present? && @line_item.cart.coupon.present?
      item_total = (@line_item.cart.items_total_without_addons(rate) - @line_item.cart.bmgnx_discounts/rate).round(2) if @line_item.cart.present?
      coupon_percent = coupon_discounts.present? && item_total !=0 ? ((coupon_discounts / item_total) * 100).to_f.round(2) : 0
      price = @ga_line_item_array.first[:price] || 0
      item_customization = @ga_line_item_array.first[:item_customization] || 0
      price_without_addon = price - item_customization
      price_without_coupon = (price_without_addon /(1-(coupon_percent/100))).round(2)
      price = (price_without_coupon + item_customization).round(2)
      @ga_line_item_array.first[:price] = price
      @google_tag_params = {event: "ga4_remove_from_cart", ecommerce: {  currency: "INR", country_code: @country_code, value: price, items: @ga_line_item_array}}
      render :file => '/line_items/update.js', :content_type => 'text/javascript'
    else
      render :nothing => true
    end
  end
  
  def calculate_total_price(ga_line_item_hash, quantity)
    if ga_line_item_hash[:price].present? && ga_line_item_hash[:discount].present?
      ((ga_line_item_hash[:price].round(2) - ga_line_item_hash[:discount].round(2))).round(2)
    end
  end
    
  def save_note
    # make sure to check if this line_item belongs to current cart. malicious user can update someone else cart here.
    @line_item = LineItem.find_by_id(params[:id])
    if @line_item.present?
      @line_item.add_note(params[:note])
      @line_item.save!
      render :file => '/line_items/save_note.js', :content_type => 'text/javascript'
    else
      render :nothing => true
    end
  end

  def add_addon_on_current_cart
    id = params[:id]
    line_item_addon= LineItemAddon.find_by_id id
    if line_item_addon.present?
      design_id = nil 
      design_id = line_item_addon.addon_type_value.design.design_check if line_item_addon.addon_type_value.design.present?
      @line_item = line_item_addon.line_item 
      old_size_chart_id = line_item_addon.size_chart_id
      required_addon = line_item_addon.addon_type_value.addon_type.addon_type_values.where(design_id: nil).where(published: 'true').where(addon_type_value_group_id: [1,2]).where(position: 2).order('addon_type_value_group_id').last
      line_item_addon.line_item.line_item_addon_ids -= [id.to_i]
      @line_item.line_item_addons << LineItemAddon.create(line_item_id: @line_item.id,addon_type_value_id: required_addon.id,snapshot_price: required_addon.effective_price(@line_item.design), size_chart_id: old_size_chart_id)
      @line_item.save!
      @free_stitching_text = @cart.free_stitching_text(@country_code, @conversion_rate, @symbol)
      session[:note] = "upselling"
      # @cart = Cart.all_rel.find_by_id(session[:cart_id])
      # @cart_recommendations_designs = @cart.recommendations(@conversion_rate, @actual_country)
    end
    render :file => '/line_items/update.js', :content_type => 'text/javascript'
  end
  
  def save_quantity
    @line_item = @cart.line_items.find{|li| li.id == params[:id].to_i}
    if @line_item.present? and RESPECT_QUANTITY
      req_quantity = params[:quantity].to_i
      unless req_quantity <= 0
        @previous_quantity = @line_item.quantity
        if @line_item.check_out_of_stock?
          render :nothing => true
          return
        end
        if @line_item.variant_id.present?
          @line_item.update_quantity_variant(req_quantity)
        else
          @line_item.update_quantity(req_quantity)
        end
        
        if @line_item.quantity != @previous_quantity
          @line_item.save!
          @update_quantity = true
          total_price = params[:total_price].present? ? params[:total_price].to_f : 0
          ga_line_item_array = []
          ga_line_item_hash = @line_item.line_item_wise_ga_data(0, @line_item.get_breadcrumb, @country_code)
          #value = ga_line_item_hash[:price] - ga_line_item_hash[:discount]
          ga_line_item_array << ga_line_item_hash
          rate = (@line_item.snapshot_currency_rate).to_f
          coupon_discounts = (@line_item.cart.coupon_discounts/rate).to_f.round(2) if @line_item.cart.present? && @line_item.cart.coupon.present?
          item_total = (@line_item.cart.items_total_without_addons(rate) - @line_item.cart.bmgnx_discounts/rate).round(2) if @line_item.cart.present?
          coupon_percent = coupon_discounts.present? ? ((coupon_discounts / item_total) * 100).to_f.round(2) : 0
          if @line_item.quantity > @previous_quantity
            ga_line_item_array.each do |line_item|
              line_item[:quantity] = @line_item.quantity - @previous_quantity
            end
            @googe_add_hash_new = google_ads_data(ga_line_item_array.first)
            item_ids = @googe_add_hash_new[:item_ids]
            event_name = "ga4_add_to_cart"
          else
            ga_line_item_array.each do |line_item|
              line_item[:quantity] = @previous_quantity - @line_item.quantity
              item_ids = ''
            end
            event_name = "ga4_remove_from_cart"
          end
          @google_tag_params = {event: event_name, ecommerce: {currency: "INR", value: ga_line_item_hash[:price].round(2),country_code: @country_code, items: ga_line_item_array,item_ids: item_ids}}
          # @cart = Cart.all_rel.find_by_id(session[:cart_id])
          diff_quantity = @previous_quantity - @line_item.quantity
          item_event = diff_quantity < 0 ? 'addToCart' : 'removeFromCart'
          gtm_data_layer.push({event: item_event, productId: @line_item.design.id, quantity: diff_quantity.abs}.merge!(@cart.details_for_gtm))
        end
      end
      @cart.apply_shipping_coupon
      @free_stitching_text = @cart.free_stitching_text(@country_code, @conversion_rate, @symbol)
      # @cart_recommendations_designs = @cart.recommendations(@conversion_rate, @actual_country)
      render :file => '/line_items/update.js', :content_type => 'text/javascript'
    else
      render :nothing => true
    end
  end

  def get_addon_type_value_form
    addon_type_value = params[:item_data]
    addon_form = render_to_string(partial: "orders/addon_type_value_form", locals: {item_id: params[:item_id], order_number: params[:order_num], order_id: params[:order_id], stitch_addon: nil, addon_type_value: addon_type_value}, :formats => [:html]).html_safe
    respond_to do |format|
      format.js { render json: {form: addon_form}, status: :ok }
    end
  end

  def create_stitching_addon
    addon_type_value = params[:addon_type_value]
    atv_hash = {"shapewear" => SHAPEWEAR_FORM_DATA.merge('extra_params' => 'size'), "petticoat" => PETTICOAT_FORM_DATA.merge('extra_params' => 'materials')}
    atv_data = atv_hash[addon_type_value.downcase]
    extra_hash_param = atv_data['extra_params']
  
    filters = {
      line_item_id: params[:item_id],
      addon_type: params[:addon_type_value],
      color: params[:addon_color],
      material: params[:material],
      size: params[:size]
    }
    if params[:commit] == 'Add'
      stitching_addon = StitchingAddon.where(filters).first_or_initialize
      if stitching_addon.new_record?
        stitching_addon.assign_attributes({quantity: params[:p_quantity].to_i, order_id: params[:order_id], details_added_by: current_account.id, details_added_on: DateTime.now})
        stitching_addon.save
        notice = 'Successfully Created.'
      else
        notice = 'This combination already exist. Please update the existing one.'
      end
    else
      if StitchingAddon.where('id <> ?',params[:stitching_addon_id]).where(filters).first
        notice = 'This combination already exist. Please update the existing one.'
      else
        filters = filters.merge(quantity: params[:p_quantity].to_i, details_added_by: current_account.id, details_added_on: DateTime.now)
        StitchingAddon.find_by(id: params[:stitching_addon_id]).update(filters.except(:line_item_id, :addon_type))
        notice = 'Successfully Updated.' 
      end
    end
    redirect_to order_order_detail_path(params[:order_number]), notice: notice
  end

  def add_vendor_notes    
    if request.post? 
      if params[:item_id].present? && params[:note_issue_type].present?              
        add_event_note(params[:item_id], params[:note_issue_type], params[:note_reason])
        notice = { message: 'Followup Successfully Added.'}
      else
        notice = { error: 'Some Details are missing.' }
      end
      render json: notice
    end
  end

  def add_stylist_notes
    response = {error: true, message: 'Wrong Details Provided.'}
    if params[:item_id].present? && params[:notes].present? && params[:topic].present?
      full_notes = "#{params[:topic]} - #{params[:notes]}"
      note = add_event_note(params[:item_id], 'stylist', full_notes)
      LineItem.find_by_id(params[:item_id]).try(:create_communication_topic, topic: params[:topic], message: params[:notes], done_by_account: current_account)
      response = {error: false, message: 'Successfully Added.', note: note}
    end
    render json: response
  end

  def add_custom_stitching
    @line_items = LineItem.includes(:designer_order).where(id: params['ids'])

    @order = @line_items.first.order
    (@order.order_notification['require_custom_stitching_item'] ||= []).push(*@line_items.ids).uniq!
    @order.skip_before_filter = true
    @order.save!

    render js: "window.location = #{post_order_stitching_payment_redirect_link.to_json}"
  rescue
    flash[:error] = "Something went wrong. We're looking into it."
    render js: "window.location = #{root_path.to_json}"
  end

  def mark_no_intervention
    response = {error: true, message: 'Something Went Worng.'}
    if params[:item_id].present? && (item = LineItem.find_by_id(params[:item_id])).present?
      item.store_item_details(detail_name: 'no_stylist_intervention', detail_value: 'true')
      item.stitching_measurements.each{|sm| sm.measurement_approved! }
      response = {error: false, message: 'Successfully Updated.'}    
    else
      response[:message] = 'Worng details provided.'
    end
    render json: response
  end

  def line_item_label
    if (item = LineItem.find_by_id(params[:item_id])) && (template_details = get_template_details(item, params[:label_type]))     
      respond_to do |format|
        format.html
        format.pdf do
          render layout: false,
                 pdf: item.id.to_s,
                 template: template_details[:template_name],
                 locals: template_details[:args],
                 orientation: params[:orientation].presence || 'Portrait'
        end
      end
    end
  end

def remove_warehouse_tag
  response  = {errors: true, error_message: 'Something Went Wrong'}
  line_item = LineItem.find(params[:item_id])
  item = line_item.variant_id.present? ? line_item.variant  : line_item.design
  if (item_id = params[:item_id]).present? && item.quantity - item.in_stock_warehouse >= line_item.quantity
    mark_lost = params[:mark_lost].present? && params[:mark_lost] == "true" ? true : false
    account_id = params[:account_id]
    LineItem.remove_sor_tag(params[:item_id], mark_lost, account_id)
    response = {error: false, error_message: ''}
  end
  render json: response
end

def received_warehouse_product
  line_item = LineItem.find params[:item_id]
  line_item.reverse_product_received_by = current_account.email
  line_item.reverse_product_received_on = Time.now
  line_item.save
  respond_to do |format|
    format.html { head :ok }
    format.js
  end
end

def add_warehouse_tag
  if (item_id = params[:item_id]).present? && (item = LineItem.find(item_id)).present? && (item.design.in_stock_warehouse.to_i >= item.quantity || (item.variant.present? && item.variant.in_stock_warehouse.present?)) && item.quantity_exceeds?(item.design.id, item.quantity)
    service = SorTagService.new(item)
    service.process_item
    render json: { error: false, error_message: '' }
  else
    render json: { errors: true, error_message: 'Enough stock is not available in warehouse' }
  end
end

private

  def get_template_details(item, label_type)
    case label_type
    when 'stitching'
      {template_name: 'designer_orders/stitching_label.html', args: {delayed_attempt: false, line_item: item}}
    when 'unpacking'
      {template_name: 'line_items/unpacking_label.html', args: {order: item.order, line_item_id: item.id, order_number: item.order.number, design_id: item.design_id, from_order_page: params[:from_order_page], designable_type: item.designable_type}}
    else
      {}
    end
  end

  def add_event_note(item_id, issue_type, notes)
    done_by = (current_account.try(:designer?) ? 'Designer' : current_account.try(:name))
    event = Event.create(eventable_id: item_id, eventable_type: 'LineItem', note_type: issue_type, notes: notes, done_by: done_by, event_timestamp: Time.current, account_id: current_account.try(:id))    
    event.attributes.values_at(*['event_timestamp', 'note_type', 'notes']).join(' => ').concat("\r\n")  
  end

  def post_order_stitching_payment_redirect_link
    additional_payment = @order.additional_payments.create(charge_type: 'post_order_stitching')
    design_ids = []
    total = 0
    coupon_applied = @order.coupon.try(:is_stitching?)

    country = @order.country
    country_code = @order.country_code

    @line_items.each do |line_item|
      addon_type_ids = line_item.get_present_addon_types
      line_item.line_item_addons.each{ |lia| lia.delete if addon_type_ids.include?(lia.addon_type_value.try(:addon_type).try(:id)) }

      design = line_item.design
      addons = design.get_design_addons(country, country_code)

      addon_type_value = addons.map(&:addon_type_values).flatten.find do |addon_type_value|
        next if Design::STITCHING_WITH_VARIANT_DESIGNABLE_TYPES.include?(design.designable_type) && ((!design.unstitched_variant.present? && addon_type_value.description == 'addons_with_variants') || (design.unstitched_variant.present? && addon_type_value.description != 'addons_with_variants'))
        addon_type_value.addon_type_value_group.name == 'custom_stitching'
      end
      
      if addon_type_value.present?
        effective_price = addon_type_value.effective_price(design, country_code).to_f
        snapshot_price = coupon_applied ? 0 : effective_price
        total += effective_price * line_item.quantity
        line_item.line_item_addons << LineItemAddon.new(addon_type_value_id: addon_type_value.id, snapshot_price: snapshot_price, status: 'unpaid', additional_payment_id: additional_payment.id)
        line_item.stitching_required = 'Y'
        design_ids << line_item.design_id
      end
      line_item.save
    end

    @addon_post = true
    design_ids.uniq!
    if @order.save && design_ids.present?
      additional_payment.total = total
      additional_payment.currency_rate_market_value = CurrencyConvert.countries_marketrate[@order.country_code]
      additional_payment.save
      if total > 0 && @order.state == 'sane'
        @order.add_notes_without_callback("Payment Link Sent. Addons added to #{design_ids.join(',')}", 'payment', current_account)
        redirect_link = if @order.international? || @order.billing_international?
          pay_by_paypal_url(number: @order.number, addons: additional_payment.id)
        else
          @order.payu_billing_page_url_non_seamless(order_payu_response_url, false, additional_payment.id)
        end
      end
      # @order.sidekiq_delay(queue: 'high')
      #       .update_cost_estimation if @order.international?
      SidekiqDelayGenericJob.set({queue: 'high'}).perform_async(@order.class.to_s, @order.id, "update_cost_estimation") if @order.international?
      # @order.sidekiq_delay.update_expected_dates
      SidekiqDelayGenericJob.perform_async(@order.class.to_s, @order.id, "update_expected_dates")
    else
      additional_payment.delete
      redirect_link = root_path
    end

    redirect_link
  end

  def multi_line_item_params
    params.permit(line_items: [:design_id, :pair_product, :variant, :combo_variant, :cart_upsell, :rakhi_note, :standard_size, :quick_cod_request, addons: [:addon_type_value_id, addon_option_value_id: []]])
  end

  def update_cart_total_items
    prev_count = session[:cart_total_items].to_i
    session[:cart_total_items] = @cart.try(:total_items).to_i
    if prev_count == 0 || session[:cart_total_items] == 0
      if session[:cart_total_items].to_i > 0 || account_signed_in?
        cookies[:dynamic_user] = 't'
      else
        cookies.delete :dynamic_user
      end
    end
  end

end
