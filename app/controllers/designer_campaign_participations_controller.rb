class DesignerCampaignParticipationsController < ApplicationController

    def create
        csv_file = params[:csv_file]
        seller_campaign_id = params[:seller_campaign_id]
        designer_id = params[:designer_id]
        expected_headers = ['design_id', 'discount_percent']
        if csv_file.present? && csv_file.content_type == 'text/csv'
          csv = CSV.read(csv_file.path, headers: true)
          headers = csv.headers.compact.map(&:strip)
      
          missing_headers = expected_headers - headers
      
          if missing_headers.any?
            flash[:error] = "Invalid CSV headers. Missing: #{missing_headers.join(', ')}"
            redirect_to all_campaign_path(params[:designer_id]) and return
          end
      
          filename = "design-csv-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
          directories = AwsOperations.get_directory(bucket: 'ticket-images-new', new_connection: true)
          fog_object = directories.files.create(
            key: filename,
            body: csv_file.read,
            public: false
          )
          @csv_file_path = fog_object.url(Time.now + 7200)
      
          UploadCampaignDiscountJob.perform_async(@csv_file_path, current_account.email, seller_campaign_id, designer_id)
        end
      
        @seller_campaign = SellerCampaign.find(seller_campaign_id)
        @designer = Designer.find(designer_id)
        @designer_campaign_participation = DesignerCampaignParticipation.create(seller_campaign: @seller_campaign, designer: @designer)
        respond_to do |format|
            format.html { redirect_to all_campaign_path(@designer)}
        end
    end

    def cancel
        @designer_campaign_participations = DesignerCampaignParticipation.find params[:id]
        @designer = Designer.find @designer_campaign_participations.designer_id
        if @designer_campaign_participations.present?
            @designer_campaign_participations.seller_campaign.design_campaign_discounts.destroy_all
            @designer_campaign_participations.destroy
        else
            message = 'Cannot find Campaign'
        end
        redirect_to all_campaign_path(@designer), notice: message
    end
end
  