class OrdersController < ApplicationController  
  
  NON_SERVICABLE = LOCKDOWN_NON_SERVICABLE_PINCODES
  # Make sure you authenticate that this is admin account.
  before_filter :authenticate_account!, :except => [:new, :create, :retry, :retry_cod, :show, :autopost, :res, :paypal_res, :gharpay_push_notification, :new2, :pay_by_paypal, :pay_by_card, :get_states, :cod_verify,:get_pincode_format, :ccavenue_mpcg_response, :payu_response, :amazon_return, :amazon_response, :razorpay_submission, :paytm_response, :get_order_details, :send_order_ack_email,:update_landmark,:update_google_map_query_limit_status,:g2a_response, :paypal_create, :paypal_execute, :paypal_response_handling, :get_state_dial_code, :update_shipping_address, :stripe_response]
  before_filter :validate_shipping_amount, :only => [:create]
  before_filter :ensure_cart_not_empty, :only => [:new, :new2, :create]
  before_filter :is_valid_user, only: [:combined_sticker]
  before_filter :set_smart_pay_visiblity, only: [:new, :retry]
  before_filter :set_tax_rate, only: [:show, :create, :new]
  load_and_authorize_resource find_by: :number, only: :send_order_sms, id_param: :number
  authorize_resource :except => [:show, :retry, :retry_cod, :delete_line_item, :update_line_item, :autopost, :res, :paypal_res, :gharpay_push_notification, :new2, :pay_by_paypal, :pay_by_card, :get_states, :get_pincode_format,:fedex_invoice, :cod_verify, :ccavenue_mpcg_response, :payu_response, :check_order_label, :amazon_return, :amazon_response, :razorpay_submission, :paytm_response,:get_order_details,:send_order_ack_email,:update_landmark,:update_google_map_query_limit_status,:g2a_response, :paypal_create, :paypal_execute, :paypal_response_handling, :get_state_dial_code, :update_shipping_address, :stripe_response]
  skip_before_filter :verify_authenticity_token#, :only => [:ccavenue_mpcg_response, :paypal_res, :payu_response, :send_paypal_invoice]
  layout 'admin', :only => [:index, :order_detail, :edit, :invoice, :tracking_details, :order_check_page,'xpressbees_ups_invoice', 'xindus_invoice', 'fedex_invoice', 'dhl_invoice', 'delhivery_invoice', 'skynet_invoice', 'dhl_ecom_invoice', 'atlantic_invoice', 'aramex_international_invoice', 'get_epst_lpst_details', :unpack_to_order_detail, 'ups_invoice','clickpost_shippers']
  require 'dhl/dhl_api'
  require 'fedex/fedex_api'
  require 'atlantic/atlantic_api'
  require 'xindus/xindus_api'
  require 'xpressbees_aggregators/ups_api'
  # GET /orders
  # GET /orders.json
  def index
    if params[:order_ids].present? && params[:air] == 't'
      Order.unscoped.where(id: params[:order_ids]).update_all(state: 'ready_for_dispatch',ready_for_dispatch_at: DateTime.now)
      Order.sidekiq_delay(queue: 'critical')
           .post_rfd_changes_in_bulk(params[:order_ids])
    end
    @start_date = params[:start_date].present? ? (DateTime.parse(params[:start_date]).to_date).strftime("%A, %d %B, %Y") : (DateTime.now.beginning_of_day-2.months).strftime("%A, %d %B, %Y")
    @end_date = params[:end_date].present? ? (DateTime.parse(params[:end_date]).to_date + 1.day).strftime("%A, %d %B, %Y")  : (DateTime.now.end_of_day + 1.day).strftime("%A, %d %B, %Y")
    @sort_options = ['created_at', 'confirmed_at', 'pickup', 'updated_at', 'pending_at', 'total']
    @app_source = ['Mobile', 'Desktop', 'iOS', 'Android']
    @sort = params[:sort].present? ? params[:sort] : 'created_at'
    @order_by = params[:order].present? ? params[:order] : 'DESC'

    if current_account.role?(:outsourced_support) #Out sourced support role can only search orders upto 60 days old
      @start_date = @start_date < (DateTime.now - 6.months) ?  (DateTime.now - 6.months).strftime("%A, %d %B, %Y") : @start_date
      @end_date = @start_date < @end_date ? @end_date : (DateTime.now.end_of_day + 1.day).strftime("%A, %d %B, %Y")
    end

    request_items = ['search','email','phone','number','name','air','region',
      'tags','state','view','cod','bank','pay_type','gharpay','wait','d0','d1',
      'itags','etags','sort','type']

    if (request_items & params.keys).present?
      if params[:search].present?
        if params[:search].include?('@')
          params[:email] = params[:search]
        elsif params[:search].starts_with?('M')
          params[:number] = params[:search]
        end
      end

      if params[:inter].present?
        @dispatch_start_date = params[:dispatch_start_date].present? ? (DateTime.parse(params[:dispatch_start_date]).to_date).strftime("%A, %d %B, %Y") : (DateTime.now.beginning_of_day).strftime("%A, %d %B, %Y")
        @dispatch_end_date = params[:dispatch_end_date].present? ? (DateTime.parse(params[:dispatch_end_date]).to_date + 1.day).strftime("%A, %d %B, %Y")  : (DateTime.now.end_of_day + 1.day).strftime("%A, %d %B, %Y")
        if params[:inter] == 'e'
          shipper_id = Shipper.where('name IN (?)', ["Fedex", "Aramex", "DHL", "Speedpost"]).pluck(:id)
          if params[:shipment_state].nil? || (params[:shipment_state].present? && params[:shipment_state] == 'processing')
            @orders = Order.unscoped.joins(:shipments).where('tracking_number = shipments.number').
            where('shipments.shipper_id IN (?) AND shipments.shipment_state =? AND shipments.created_at <?', shipper_id,'processing',params[:days_passed].to_i.days.ago).where(state: 'dispatched').where('orders.pickup >= ? AND orders.pickup <= ?', @dispatch_start_date, @dispatch_end_date).order('orders.pickup DESC')
            @days_passed = params[:days_passed]
          elsif params[:shipment_state].present? && params[:shipment_state] == 'in_transit'
            @orders = Order.unscoped.joins(:shipments).where('tracking_number = shipments.number').where('shipments.shipper_id IN (?) AND shipments.shipment_state =? AND shipments.in_transit_datetime <?',shipper_id,'in_transit',params[:days_passed].to_i.days.ago).where(state: 'dispatched').where('orders.pickup >= ? AND orders.pickup <= ?', @dispatch_start_date, @dispatch_end_date).order('orders.pickup DESC')
            @state_selected = params[:shipment_state]
            @days_passed = params[:days_passed]
          elsif params[:shipment_state].present? && (params[:shipment_state] == 'out_for_delivery' || params[:shipment_state] =='delivery_exception')
            @orders = Order.unscoped.joins(:shipments).where('tracking_number = shipments.number').where('shipments.shipper_id IN (?) AND shipments.shipment_state =? AND shipments.last_event_timestamp <?',shipper_id,params[:shipment_state],params[:days_passed].to_i.days.ago).where(state: 'dispatched').where('orders.pickup >= ? AND orders.pickup <= ?', @dispatch_start_date, @dispatch_end_date).order('orders.pickup DESC')
            @state_selected = params[:shipment_state]
            @days_passed = params[:days_passed]
          end
        else
          @orders = Order.unscoped.where(state: 'dispatched').where('orders.pickup >= ? AND orders.pickup <= ?', @dispatch_start_date, @dispatch_end_date).order('orders.pickup DESC')
          @shipper_count = Order.unscoped.where(state: 'dispatched').where('orders.pickup >= ? AND orders.pickup <= ?', @dispatch_start_date , @dispatch_end_date).group('lower(courier_company)').count
        end
        @orders = @orders.preload(:shipments) unless params[:csv_data] == true
      elsif params[:codpanel].present?
        @orders = Order.unscoped.preload(:order_issue).where(:pay_type => COD).where(state: ['confirmed', 'pending'], confirmed_at: nil).where('created_at > ?', 90.days.ago).order("orders.#{@sort} #{@order_by}")
      else
        @orders = Order.orders_between(@start_date, @end_date).order("orders.#{@sort} #{@order_by}")
        if params[:duplicate] == 't'
          @og_order = @orders.by_number(params[:number]).first
          email =  @og_order.try(:email)
          @orders = @orders.where(billing_email: email)
        else
          @orders = @orders.by_email(params[:email]).by_name(params[:name]).by_number(params[:number]).by_phone(params[:phone])
        end
        @orders = @orders.where('lower(orders.country) <> ?', 'india') if params[:region].present? && params[:region] == 'inter' && !current_account.role?(:outsourced_support)
        @orders = @orders.where('lower(orders.country) = ?', 'india') if current_account.role?(:outsourced_support) && !ACCESSIBLE_EMAIL_ID['international_visible_outsource_support'].to_a.include?(current_account.email)
        @orders = @orders.where(:items_received_status => true).order('items_received_on DESC') if params[:air].present? && params[:air] == 't'
        @orders = @orders.tagged_with('wait,fraud', :exclude => true) if !params[:wait].present? && params[:air].present?
      end

      if params[:tags].present?
        if params[:op].blank? || params[:op] == "any"
          params[:itags] = params[:tags]
        else
          params[:etags] = params[:tags]
        end
      end
      
      @orders = @orders.tagged_with(params[:itags], any: true) if params[:itags].present?
      @orders = @orders.tagged_with(params[:etags], exclude: true ) if params[:etags].present?

      if params[:state].present? && params[:inter].blank?
        if params[:type].present? && params[:type] == 'designer_order'
          @orders = @orders.where(:designer_orders => {:state => params[:state]}).select("distinct(orders.*)")
        else
          @orders = @orders.where(:state => params[:state])
        end
      end

      if params[:pay_type].present?
        @orders = @orders.where(:pay_type => params[:pay_type])
      elsif params[:bank].present?
        @orders = @orders.where(:pay_type => BANK_DEPOSIT)
      elsif params[:gharpay].present?
        @orders = params[:gharpay] == '0' ? @orders.where('pay_type <> ?', GHARPAY) : @orders.where(:pay_type => GHARPAY)
      elsif params[:cod].present?
        @orders = params[:cod] == '0' ? @orders.where('pay_type <> ?', COD) : @orders.where(:pay_type => COD)
      end

      if params[:app_source].present?
        @orders = @orders.where("app_source like ?", "%#{params[:app_source]}%") 
      end
      if params[:country].present?
        @orders = @orders.where(:country => params[:country])
      end

      if params[:paypal_txn_id].present? 
        @orders = Order.where(:paypal_txn_id => params[:paypal_txn_id])
      end

      if params[:view].present? && params[:view] == 'list' && params[:inter].blank?        
        @orders_greater_than = {}
        [10, 15, 25].each {|i| @orders_greater_than[i] = @orders.where('confirmed_at <= ?', i.days.ago).count(:all)}

        if params[:tags].blank?
          @tags_count = {'oos' => '', 'issue' => '', 'wait' => '', 'addon' => '', 'stitching' => '', 'tailoring' => '', 'fnp' => ''}
          options = {conditions: {tags: {name: @tags_count.keys}}}
          tags = @orders.tag_counts_on(:tags, options)      
          tags.each do |tag|
            @tags_count[tag.name] = tag.taggings_count
          end
        end
      end

      if params[:d0].present? && params[:d1].present?
        @orders = @orders.where(confirmed_at: params[:d1].to_i.days.ago..params[:d0].to_i.days.ago)
      end

      # sort_keys = Order.new.attributes.keys.collect(&:to_s)
      # if params[:sort].present? && sort_keys.include?(params[:sort]) && params[:order].present? && ['ASC', 'DESC'].include?(params[:order])
      #   @sort = params[:sort]
      #   @order_by = params[:order]
      # end

      # @orders = @orders.order("orders.#{@sort} #{@order_by}")

      if params[:view].present? && params[:view] == 'list'
        line_item_preload_array = [:designer, :stitching_measurements, :ipending_related_scans, tailoring_info: [:tailoring_inscan_bags]]
        if params[:csv_data].present?
          if params[:inter].present?
            begin
              dir = "tmp/reports/"
              FileUtils.mkdir_p(dir) unless File.directory?(dir)
              filepath = dir +"Total_Dispatched.csv"
              header = ['Order_Number', 'Type', 'Order_Created_At', 'Total', 'Customer_Name', 'Dispatched_Date', 'Actual_Weight', 'Volumetric_Weight', 'Best_Shipper', 'Sent_By','Tracking_Number']
              CSV.open(filepath, "wb", {:col_sep => ","}) do |csv|
                csv << header
                @orders.each do |order|
                  csv << [order.number,order.international? ? 'International' : 'Domestic',order.created_at.strftime('%d/%m/%Y %H:%M'),order.total,order.name,order.pickup.present? ? order.pickup.strftime('%d/%m/%Y %H:%M') : '',order.actual_weight,order.volumetric_weight,order.best_shipper,order.courier_company.try(:titleize),order.tracking_number]
                end
              end
              send_file filepath and return
            rescue
              flash[:notice] = "Error Downloading File."
            end
          else
            order_ids = @orders.pluck('orders.id')
            OrdersController.sidekiq_delay.pending_shipping(order_ids, PER_PAGE, "pending_shipping", current_account.email, params[:qc], params[:stitching])
            flash[:notice] = "Download report link has been sent to #{current_account.email} successfully."
          end
        end
        if params[:codpanel] == 't'
          per_page_value = 30
        else
          per_page_value = 20
        end        
        if params[:ipending_report].present?
          Order.sidekiq_delay(queue: 'high')
               .mail_ipending_report(
                 @orders.pluck('orders.id'),
                 current_account.email
               )
          flash[:notice] = 'Report Will be Mailed To you shortly.'
        end
        (line_item_preload_array << :designer_issues) if params[:tags] == 'oos'
        @orders = @orders.preload(:events, :tags, :delivery_nps_info, line_items: line_item_preload_array).paginate(:page => params[:page], :per_page => per_page_value)
      else
        @orders = @orders.preload(:events, line_items: :designer).paginate(:page => params[:page], :per_page => 40)
      end
    else
      @orders = nil
    end
    unless user_role 
      text_message_support
    end

    @coupon = Coupon.new
    respond_to do |format|
      format.html # index.html.erb
      format.json {render json: 'success'}
      format.js
    end   
  end

  def text_message_support
    @text = ''
    text = SupportText.where('order_for_category is null').where(visible_to_designer: false).order('random()').first
    @text = text.description if text.present? && text.description.present?
  end

  def self.pending_shipping(order_ids, per_page, filename, email, qc, stitching)
    orders = Order.where(id: order_ids) 
    @orders = orders.list_orders.paginate(page: 1, per_page: per_page )
    html_string = self.new.render_to_string(partial: 'orders/pending_shipping/pending_shipping.html.haml', locals:{orders: @orders, qc: qc, stitching: stitching}, layout: false)
    csv_string = TableCSV.new
    csv_string.attach(html_string)
    file_data = csv_string.content
    UploadReport.export_csv_data(file_data, filename, email)
  end

  def get_order_details
    if params[:order_number].present?
      order = Order.unscoped.where(number: params[:order_number], email: params[:email]).first
      render json: order.present? ? {number: order.number,country: order.country} : {error: true}
    else
      unless current_account.present? && current_account.user?
        render json: {error: true}
        return
      end
      orders = Order.unscoped.
                    includes(:designs,:shipments,:designer_orders).
                    where(user_id: current_account.user.id).
                    where('created_at >= ?', 3.month.ago.to_date).
                    order('created_at DESC')
      order_details = {}
      recent_order = {id: nil,number: nil,total: nil, created_at: 2.year.ago,country: nil}
      orders.each do |order|
        next if order.designs.blank?
        validity = true
        if (['cancel','cancel_complete'].include? order.state)
          validity = 'cancel'
        elsif (['new','pending','confirmed'].include? order.state)
          validity = 'new'
        elsif order.country.downcase == 'india'
          delivery_date = order.designer_orders.map(&:pickup).compact.max
          if delivery_date.present? && (delivery_date + 12.day < Date.today)
            validity = false
          end
        else
          shipments     = order.shipments.select{|s| s.designer_order_id.blank?}
          delivery_date = shipments.map(&:delivered_on).compact.max
          if delivery_date.present? && ((delivery_date + 7.days) < Date.today)
            validity = false
          elsif order.pickup.present? && ((order.pickup + 14.day) < Date.today)
            validity = false
          end
        end
        total = "Rs. #{order.total}"
        total = ApplicationController.helpers.get_actual_price_in_currency_with_symbol(order.total,order.currency_rate, order.currency_code) if order.country_code.present?
        order_details[order.number] = {id: order.id,items: order.designs.collect(&:title).join(', '), total: total, state: order.state.titleize, created_at: order.created_at.strftime('%d %b %Y'), valid: validity,country: order.country}
        if %w(new pending cancel cancel_complete).exclude?(order.state) && recent_order[:created_at].to_date < order.created_at.to_date
          recent_order = {id: order.id, number: order.number, total: total, created_at: order.created_at, state: order.state.titleize, valid: validity,country: order.country}
        end
      end
      order_details = nil unless order_details.present?
      render json: {orders_list: order_details, recent_order: recent_order}
    end
  end

  def order_detail
    @order = Order.all_rel.find_by_number(params[:order_number].upcase)
    @order_risk = GokwikData.find_by_order_id @order.id if @order.present?
    unless @order.present?
      redirect_to '/404'
      return
    end
    @returns = @order.returns.to_a.select{|x| (x.state == 'payment_complete') && (x.return_invoice_url.present?)  }
    @all_adjustments = Adjustment.where(order_id: @order.id).group_by(&:designer_order_id)
    @assign_tailors, @receiving_tailors = [Tailor.get_tailor, Tailor.get_tailor(false)]
    if STYLIST_TAILOR_MAPPING['enable'] == 1 && (tailor_ids = STYLIST_TAILOR_MAPPING['mapping'][@order.stylist_id.to_s]).present?
      @assign_tailors = @assign_tailors.select{|data| tailor_ids.to_a.include?(data[1][1])}
      @receiving_tailors = @receiving_tailors.select{|data| tailor_ids.to_a.include?(data[1][1])}
    end
    @order_count = Order.where(:state => 'sane', :email => @order.email).where('id <> ?', @order.id).length
    @location = Courier.where(:pincode => @order._billing_pincode).first
    @designer_orders = @order.designer_orders.where(designer_id: params[:designer_id]) if params[:designer_id] && params[:designer_id] != 'All' && @order
    @shipping_states = Country.get_states(@order.country)if @order.country.present? && !@order.international?
    @account_details = @order.used_account_ids
    @cod_designer_shippers = Hash.new()
    DesignerShipper.where(enabled: true, cod: true, designer_id: @order.designer_orders.map(&:designer_id).compact).to_a.map{|ds| (@cod_designer_shippers[ds.designer_id] ||= []) << ds.shipper_id}
    @events = @order.events
    @order_dispute = @order.order_dispute
    gon.order_number = @order.number
    if (@wallet = @order.user.try(:wallet)).present? && @order.wallet_transactions.present?
      @old_wallet_transactions =
        WalletTransaction.where(wallet_id: @wallet.id).preload(:order).order('created_at desc').paginate(page: params[:page], per_page: 10)
    end

    user_role
    order_stats = Order.user_order_stats(@order.user_id)
    @total_order_count_number = order_stats[:total_count]
    @last_order = order_stats[:last_order_number]
  end

  def user_role
    if current_account.role.present? && ['support'].include?(current_account.role.name)
      @view_flag = false
    else
      @view_flag = true
    end
  end

  def direct_orders
    if(params[:start_date].present?)
      @start_date = Date.new(params[:start_date][:year].to_i,
                             params[:start_date][:month].to_i,
                             params[:start_date][:day].to_i)
      @start_datetime = @start_date.beginning_of_day
    else
      @start_date = Date.today.beginning_of_month
      @start_datetime = @start_date.beginning_of_day
    end


    if params[:end_date].present?
      @end_date = Date.new(params[:end_date][:year].to_i,
                           params[:end_date][:month].to_i,
                           params[:end_date][:day].to_i)
      @end_datetime = @end_date.beginning_of_day
    elsif @start_date >= Date.today.beginning_of_month && @start_date <= Date.today
      @end_date = Date.today
      @end_datetime = @end_date.end_of_day
    else
      @end_date = @start_date.end_of_month
      @end_datetime = @end_date.end_of_day
    end
    
    @orders = Order.where('lower(country) <> ?', 'india').where('total > ?', 0).where('state = ? OR state = ? OR state = ? OR state = ?', 'sane', 'pickedup', 'dispatched', 'complete').where('created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).preload(:events)
    @direct_orders = @orders.collect {|o| o if o.designer_orders.where('designer_orders.state = ?', 'completed').count == 1 && o.designer_orders.where('designer_orders.state = ? OR designer_orders.state = ?', 'pending', 'dispatched').count == 0}
    @direct_orders.reject! {|o| o.blank?}
    @direct_orders.sort_by! {|o| o.designer_orders.first.designer.name}
  end


  # GET /orders/1
  # GET /orders/1.json
  def show
    if account_signed_in? && current_account.present? && current_account.accountable_type.downcase.eql?("designer")
      redirect_to "/404" and return
    end
    order_number = params[:number].upcase.gsub(/[^A-Z0-9]/,'')
    @order = Order.includes(:designer_issues, line_items: :design,designer_orders: :designer).where(:number => order_number).first if order_number.present?
    @hide_menu = true
    @trackGA = false
    # @order.send_cod_verification if @order && @order.cod? && @order.pending? && @order.cod_verification_attempt_pending?
    # @order.send_cod_verification(orders_cod_verify_url) if @order && @order.cod? && @order.pending? && @order.cod_verification_attempt_pending?
    if @order.present?
      city = @order.billing_city
      @city = city.present? ? Digest::SHA256.hexdigest(city) : nil
      country = @order.billing_country
      @country = country.present? ? Digest::SHA256.hexdigest(country) : nil
      email = @order.email
      @email = email.present? ? Digest::SHA256.hexdigest(email) : nil
      cleaned_phone_number = @order.billing_phone.gsub(/\D/, '').sub(/^0+/, '')
      @phone_number = cleaned_phone_number.present? ? Digest::SHA256.hexdigest(cleaned_phone_number) : nil
      pincode = @order.billing_pincode
      @pincode = pincode.present? ? Digest::SHA256.hexdigest(pincode) : nil
      state = @order.billing_state
      @state = state.present? ? Digest::SHA256.hexdigest(state) : nil
      name = @order.name
      @name = name.present? ? Digest::SHA256.hexdigest(name) : nil
      cc = CurrencyConvert.find_by(country_code: @country_code)
      rate = cc.rate
      market_rate = cc.market_rate
      @total = ((@order.total/rate).round(2) * market_rate).round(2)
      
      if @order.country_code.present? && @order.country_code != 'IN'
        order_currency = CurrencyConvert.currency_convert_memcached.select{|c| c.country_code == @order.country_code}.last
        @adwords_total = ((@order.total/order_currency.rate) * (order_currency.market_rate)).round(2)
      else
        @adwords_total = @order.total
      end
      @all_line_items = @order.line_items.sane_items.where('designer_orders.state not in (?)',['canceled','vendor_canceled'])
      designer_ids = DesignerOrder.where(order_id: @order.id).pluck(:designer_id)
      if designer_ids.uniq.count <= ESSENTIAL_DESIGNERS['designer_ids'].count && (ESSENTIAL_DESIGNERS['designer_ids'].map(&:to_i) & designer_ids.uniq) == designer_ids.uniq
        @essentials_delivery_date = @order.created_at.advance(days: ESSENTIAL_DESIGNERS['max_eta']).strftime('%d %b %y')
        @essentials_delivery_show = true
      end
      if @order.confirmed_at.present?
        if (dns = @order.delivery_nps_info).present?
          @delivery_date = dns.promised_delivery_date.to_date
          @revised_delivery_date = dns.revised_delivery_date.present? ? dns.revised_delivery_date.to_date : nil
        else
          delivery_time = @order.delivery_days
          @delivery_date = @order.confirmed_at.advance(:days => delivery_time).to_date
          advance_days =
            if @all_line_items.present?
              @order.calculate_advance_days(@all_line_items, delivery_time, delivery_time/6)
            else
              0
            end
          @revised_delivery_date = advance_days != 0 ?  @delivery_date.advance(days: advance_days ) : nil
        end
        @expected_dispatch_date = @order.confirmed_at.advance(days: (@order.express_delivery.present? ? 2 : SHIPPING_TIME)).to_date
      end

      @order.line_items.not_canceled.each do |item|
        @rakhi_pre_delivery = true if item.note.present? && item.note.match('Rakhi Schedule Delivery')
      end
      
      if ENABLE_MAP_LANDMARK
        gon.pincode = @order.pincode
        gon.order_number_for_landmark = @order.number
        @limit_not_exceeded = SystemConstant.get('GOOGLE_MAP_API_LIMIT_EXCEEDED') == 'true'
        gon.googlemap_api = Mirraw::Application.config.googlemap_api 
      end  

      # If order hasn't been tracked in GA, track it now.
      if @order && @order.track == 0 && @order.state != 'cancel'
        @trackGA = true
        @order.update_column(:track, 1)
        session[:utm_source].clear if session[:utm_source].present?
        session[:utm_medium].clear if session[:utm_medium].present?
        session[:utm_campaign].clear if session[:utm_campaign].present?
        product_ids = @order.line_items.collect(&:design_id)
        # gtm_data_layer.push(event: 'transaction', orderProductIds: product_ids, 
          # orderTotal: (@order.total/@order.currency_rate * @order.currency_rate_market_value).round(2), orderTransactionId: @order.number
        # )
      end
      respond_to do |format|
        @integration_status = "new"
        format.html { render "orders/show"}
      end
    else
      respond_to do |format|
        format.html { redirect_to(store_catalog_url, :notice => 'Order number could not be found.') }
      end
    end
  end

  def set_tax_rate
    @tax_rate = @cart.get_tax_rate(@country_code)
  end

  def disable_cod_for_domestic_stitching( cart_id )
    line_item = LineItem.where(cart_id:cart_id)
    
    line_item.each do |item|
      item_addon = LineItemAddon.where(line_item_id: item.id)
      item_addon.each do |addons|
        if addons.notes.include?("Standard Stitching") 
          return true
        end
      end
    end
    return false 
  end

  
  # GET /orders/new
  # GET /orders/new.json
  def new
    @integration_status = "new"
    @razor_payment = false
    @stripe_payment = false
    gon.paypal_client_id = ENV['PAYPAL_CLIENT_ID']
    gon.env_name = Mirraw::Application.config.paypal_smart_pay[:env]
    gon.currency_symbol = @symbol
    # razorpay payment gateway
    unless session[:razorpay].blank? || session[:order_number].blank?
      @order = Order.where('number = ?', session[:order_number]).first
      @razor_payment = true
      session[:razorpay] = nil
      @razorpay_order = RazorpayGateway.create_order(@order.total, @order.number, @order.currency_code)
      return
    end
    set_gon_variables
    gon.delivery_time, addon_delivery_time, available_in_warehouse, india_delivery_time, vendor_pickup_locations = calculate_delivery_time
    # disable cod if the addon is standard stitching in domestic market 
    
    @disable_cod = disable_cod_for_domestic_stitching( @cart.id )

    # OldCode Controlling Traffic Diversion To Payment Gateway
    # OldCode if params[:payu].present? && params[:payu] == '1'
    # OldCode   session[:payment_gateway] = 'PAYU'
    # OldCode else
    # OldCode   if session[:payment_gateway].blank? || ENV['FORCED_PG'].to_i == 1
    # OldCode     if rand(10) <= ENV['PAYU_TRAFFIC_PERCENT'].to_i
    # OldCode       session[:payment_gateway] = 'PAYU'
    # OldCode     else
    # OldCode       session[:payment_gateway] = 'CCAVENUE'
    # OldCode     end 
    # OldCode   end
    # OldCode end

    if @cart.check_out_of_stock?
      cart_id = @cart.hash1.presence || @cart.id
      redirect_to cart_url(:id => cart_id)
    else
      @ship_to_same_address = true
      @hide_menu = true
      @order ||= Order.new
      # Calculate possible international shipping cost
      if RAKHI_PRE_ORDER[0] == 'true'
        gon.rakhi_pre_order = true
        gon.rakhi_pre_order_date = RAKHI_PRE_ORDER[1]
        count, rakhi_all_schedule = 0, 0
        @cart.line_items.each do |item|
          rakhi_all_schedule += 1 if item.note.present? && item.note.match('Rakhi Schedule Delivery')
          count += 1
        end
        if count == rakhi_all_schedule
          gon.rakhi_all_schedule = true
        elsif rakhi_all_schedule != 0 && count > rakhi_all_schedule
          gon.rakhi_with_other_designs = true
        end
      end
      @order.billing_country = session[:country][:name] if session[:country][:name].present?
      if available_in_warehouse  #### same product both SOR and Express case pending old method @cart.warehouse_available_designs?
        gon.rts_delivery_time = (gon.country_wise_shipping_time[@order.billing_country.try(:downcase)].to_f + addon_delivery_time).round.to_s
      elsif @cart.ready_to_ship_designs? && EXPRESS_DELIVERY.to_f >= 0
        gon.express_delivery_time = (READY_TO_SHIP_DESIGNS.to_i + @cart.vacation_day_count).to_s
      end
      gon.shipping_time = gon.delivery_time.round
      
      gon.items = @cart.line_items.collect do |item|
        item.ga_data(experiment_id: item.design.experiment_id(@tester, :grading))
      end
      gon.auto_select_cod = true if params['auto_select_retry_cod']

      gon.remaining_wallet_credit = 0
      gon.max_phone_length = 10
      if account_signed_in? && current_account.user?
        @user = current_account.user
        gon.addresses = @user.addresses.collect do |adr|
          adr.attributes.merge({first_name: adr.first_name, last_name: adr.last_name})
        end
        address = current_account.user.default_address
        if address.present?
          @order.billing_name = address.name
          @order.billing_phone = address.phone
          @order.billing_pincode = address.pincode
          @order.billing_street = address.street_address
          @order.billing_street_line_1 = address.street_address_line_1
          @order.billing_street_line_2 = address.street_address_line_2
          @order.billing_country = address.country
          @order.billing_state = address.state
          @order.billing_city = address.city
          @order.billing_email = current_account.user.email
          cod_charge_order = Order.domestic_cod_charge(@order.billing_pincode,@cart) || 0
          gon.cod_charges   = (cod_charge_order/@conversion_rate).round(2)
        end
        cart_return_amount = @cart.wallet_discounts(:return)
        gon.remaining_wallet_credit = cart_return_amount > 0 ? @user.wallet_amounts.return_amount - cart_return_amount : 0
      end

      if session[:order_number].present?
        old_order = Order.where(:number => session[:order_number]).first
        if old_order.present?
          @order.billing_name = old_order.billing_name
          @order.billing_phone = old_order.billing_phone
          @order.billing_pincode = old_order.billing_pincode
          @order.billing_street = old_order.billing_street
          @order.billing_country = old_order.billing_country
          @order.billing_state = old_order.billing_state
          @order.billing_city = old_order.billing_city
          @order.billing_email = old_order.billing_email
          @order.pay_type = old_order.pay_type
          gon.cod_charges   =  old_order.cod_charge #(Order.domestic_cod_charge(old_order.billing_pincode,cart)/@conversion_rate).round(2)||0
        end
      end

      gtm_data_layer.push({pageType: 'orderNew'}.merge!(@cart.details_for_gtm))

      unless session[:stripe].blank? || session[:order_number].blank?
        @order = Order.where('number = ?', session[:order_number]).first
        @stripe_payment = true
        intent = @order.create_intent
        session[:intent] = intent.id
        session[:stripe] = nil
        gon.clientSecret = intent.client_secret
        gon.session = true
        gon.key = ENV['STRIPE_PUBLISHABLE_KEY']
        @amount = @order.total
        return
      end
      if !account_signed_in?
        @seamless_account = Hash.new
        @seamless_account[:seamless] = false
        if Account.find_by(email: @order.billing_email)
          @seamless_account[:exists] = true
        end
        @seamless_account[:password] = nil
      end
      @show_dial_code = @order.billing_country == "India" && @order.billing_phone.present?
      gon.max_phone_length = @show_dial_code? 12 : 10
      respond_to do |format|
        format.html # new.html.erb
        format.json { render json: @order }
      end
    end
  end

  def validate_shipping_amount
    shipping_country =  params[:ship_to_same_address].present? && params[:ship_to_same_address] == "1" ? params[:order][:billing_country] : params[:order][:country]
    if shipping_country.downcase != 'india' && params[:order][:pay_type] != COD
      total_price = @cart.try(:items_total_price) - @cart.additional_discounts
      if total_price.to_i < INTERNATIONAL_MIN_CART_VAL.to_i
        min_val = @symbol+' '+(CurrencyConvert.convert_to(@symbol,INTERNATIONAL_MIN_CART_VAL.to_i,@country_code).round(2)).to_s 
        redirect_to :back, :notice => 'International Order value must be at least '+min_val
      end
    end
  end

  def set_gon_variables
    gon.enable_cod_countries = ENABLE_COD_COUNTRIES
    gon.enable_cod_with_range_and_countries = ENABLE_COD_WITH_RANGE_AND_COUNTRIES
    gon.enable_cod_basis_products = @cart.all_products_in_warehouse?
    gon.items_data = @cart.line_items.map{|item| item.line_item_wise_ga_data(0, item.get_breadcrumb, @country_code)}
    gon.international_shipping_cost = @cart.get_international_shipping_cost(@conversion_rate, @actual_country)
    gon.international_shipping_cost_currency = @cart.get_international_shipping_cost_in_currency_with_symbol(@conversion_rate,@symbol, @actual_country)
    gon.weight = @cart.get_weight
    gon.total = @cart.total_price_currency(@conversion_rate)
    gon.domestic_shipping_cost = ((Country.shipping_cost_for(nil,'India',gon.total, @cart.line_items).to_f / @conversion_rate).to_f).round(2)
    gon.wallet_discounts = @cart.wallet_discounts
    gon.international_minimum_order_value = (5000/@conversion_rate).ceil
    gon.international_minimum_order_value_currency = @symbol + '.' + gon.international_minimum_order_value.to_s
    # gon.cod_charges   = @order.get_currency_cod_charges(@conversion_rate)
    gon.gift_wrap_price = session[:gift_wrapped] ? (GIFT_WRAP_PRICE.to_f/@conversion_rate).round(2) : 0
    gon.express_delivery_charge = (Country.get_express_delivery_charge(session[:country][:name])/@conversion_rate).round(2)
    gon.express_delivery_charge_with_currency = "#{@symbol} #{gon.international_shipping_cost + gon.express_delivery_charge}"
    gon.express_delivery_countries = Country.get_express_delivery_countries
    gon.country_wise_shipping_time = Country.get_country_shipping_time
    gon.country_code = @country_code
    addon_delivery = true
    gon.delivery_time, addon_delivery_time, available_in_warehouse, india_delivery_time, vendor_pickup_locations = calculate_delivery_time
    gon.tax_rate = @tax_rate
    gon.total_amt = @country_code == 'IN'? (gon.total + gon.domestic_shipping_cost) : (gon.total + gon.international_shipping_cost)
    cc = CurrencyConvert.find_by(country_code: @country_code)
    market_rate = cc.present? ? cc.market_rate : 1
    gon.rate = cc.present? ? cc.rate : 1
    gon.tax_amount = ((@tax_rate * gon.total_amt) * market_rate).to_f.round(2) if @tax_rate.present?  && gon.total_amt.present?
    gon.bmgn_discount =  @cart.present? && @cart.additional_discounts.present? ? ((@cart.additional_discounts/gon.rate).round(2) * market_rate) : 0
    gon.total_price = @cart.items_total_without_addons(@conversion_rate) if @cart.present?
    gon.item_total = ((gon.total_price * market_rate) - gon.bmgn_discount).round(2) if gon.total_price.present?
    gon.coupon_price = ((@cart.coupon_discounts/(gon.rate)).round(2) * market_rate).to_f.round(2) if @cart.present? && @cart.coupon.present?
    gon.coupon_percent = gon.coupon_price.present? && gon.item_total.present? ? ((gon.coupon_price/gon.item_total)*100).round(2) : 0
    gon.item_total_price = (gon.item_total - (gon.coupon_percent * gon.item_total / 100)).round(2)
    gon.coupon_code = @cart.coupon.code if @cart.coupon.present?
    gon.addon_charges = (@cart.present? && @cart.addons_total.present?) ? (@cart.addons_total(@conversion_rate) * market_rate).to_f.round(2) : 0
    gon.gift_wrap = gon.gift_wrap_price.present? ? (gon.gift_wrap_price * market_rate).to_f.round(2) : 0
    gon.item_total_price = (gon.item_total_price + gon.addon_charges + gon.gift_wrap).round(2)
    gon.bmgn_discount_rounded_val = gon.bmgn_discount.present? ? gon.bmgn_discount.round(2) : 0
    gon.custom_duty_country = CUSTOM_DUTY_COUNTRY.except('enable')
  end

  def new2
    if @cart.check_out_of_stock?
      redirect_to cart_url(:id => @cart.hash1)
      return true
    end
    
    @ship_to_same_address = true
    @hide_menu = true
    @order = Order.new
    
    # Calculate possible international shipping cost
    gon.international_shipping_cost = @cart.get_international_shipping_cost(@conversion_rate)
    gon.international_shipping_cost_currency = @cart.get_international_shipping_cost_in_currency_with_symbol(@conversion_rate,@symbol)
    gon.wallet_discounts = @cart.wallet_discounts
    gon.weight = @cart.get_weight
    gon.total = @cart.total_price_currency(@conversion_rate)
    gon.international_minimum_order_value = (5000/@conversion_rate).ceil
    gon.international_minimum_order_value_currency = @symbol + '.' + gon.international_minimum_order_value.to_s
    @total_cod_charges = Order.domestic_cod_charge(@order.pincode, @cart)
    gon.cod_charges   = (@total_cod_charges/@conversion_rate).ceil
    #gon.cod_charges   = (COD_CHARGES/@conversion_rate).ceil
    gon.gift_wrap_price = session[:gift_wrapped] ? (GIFT_WRAP_PRICE.to_f/@conversion_rate).round(2) : 0
    gon.cbd = 'N'
    gon.remaining_wallet_credit = 0
    if account_signed_in? && current_account.user?
      @user = current_account.user
      gon.addresses = @user.addresses

      address = current_account.user.default_address
      if address.present?
        @order.billing_name = address.name
        @order.billing_phone = address.phone
        @order.billing_pincode = address.pincode
        @order.billing_street = address.street_address
        @order.billing_country = address.country
        @order.billing_state = address.state
        @order.billing_city = address.city
        @order.billing_email = current_account.user.email
        location = Courier.where(:pincode => address.pincode).first

        if location && location.cbd == 'Y'
          gon.cbd = 'Y'
        else
          gon.cbd = 'N'
        end
      end
      cart_return_amount = @cart.wallet_discounts(:return)
      gon.remaining_wallet_credit = cart_return_amount > 0 ? @user.wallet_amounts.return_amount - cart_return_amount : 0
    end
    
    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @order }
    end
  end

  # GET /orders/1/edit
  def edit
    #@order = Order.find(params[:id])
    redirect_to root_url, :notice => "Not allow to edit order."
  end

  # GET /orders/retry/:id
  def retry
    @integration_status = "new"
    @canceled_order = Order.unscoped.find_by_number(params[:id].upcase)
    if @canceled_order && @canceled_order.state == 'cancel'
      email = account_signed_in? ? current_account.email : @canceled_order.email
      @cart = Cart.create(:email => email, :hash1 => SecureRandom.hex(16))
      session[:cart_id] = @cart.id

      @hide_menu = true
      @canceled_order.line_items.each do |item|
        @line_item = @cart.add_product(design: item.design, variant: item.variant)
        @line_item.quantity = item.quantity
        @line_item.add_variant(item.variant) if item.variant.present?
        @line_item.line_item_addons << item.line_item_addons
        @line_item.save
      end
      
      if @cart.check_out_of_stock?
        redirect_to cart_url(:id => @cart.hash1)
        return true
      end

      gon.items = @cart.line_items.collect do |item|
        item.ga_data(experiment_id: item.design.experiment_id(@tester, :grading))
      end

      @hide_menu = true
      @order = Order.new
    
      @order.billing_name = @canceled_order.billing_name
      @order.billing_phone = @canceled_order.billing_phone
      @order.billing_pincode = @canceled_order.billing_pincode
      @order.billing_street = @canceled_order.billing_street
      @order.billing_country = @canceled_order.billing_country
      @order.billing_state = @canceled_order.billing_state
      @order.billing_city = @canceled_order.billing_city
      @order.billing_email = @canceled_order.billing_email

      @order.name = @canceled_order.name
      @order.phone = @canceled_order.phone
      @order.pincode = @canceled_order.pincode
      @order.street = @canceled_order.street
      @order.country = @canceled_order.country
      @order.buyer_state = @canceled_order.buyer_state
      @order.city = @canceled_order.city
      @order.email = @canceled_order.email
      @order.pay_type = @canceled_order.cod_available_on_order? ? COD : @canceled_order.pay_type

      gon.international_shipping_cost = @order_retry_shipping_cost = @cart.get_international_shipping_cost(@conversion_rate, @canceled_order.country)

      gon.delivery_time, addon_delivery_time, available_in_warehouse, india_delivery_time, vendor_pickup_locations = calculate_delivery_time
      gon.custom_duty_country = CUSTOM_DUTY_COUNTRY.except('enable')
      cart_return_amount = @cart.wallet_discounts(:return)
      gon.remaining_wallet_credit = cart_return_amount > 0 ? current_account.user.wallet_amounts.return_amount - cart_return_amount : 0
      gon.wallet_discounts = @cart.wallet_discounts
      gon.eu_countries = Order::EU_COUNTRIES    
      gon.express_delivery_countries = Country.get_express_delivery_countries
      gon.express_delivery_charge = (Country.get_express_delivery_charge(@order.country)/@conversion_rate).round(2)
      gon.express_delivery_charge_with_currency = "#{@symbol} #{gon.international_shipping_cost + gon.express_delivery_charge}"
      gon.country_wise_shipping_time = Country.get_country_shipping_time
      gon.gift_wrap_price =  (gift_wrap = @canceled_order.order_addon.try(:gift_wrap_price)) ? (GIFT_WRAP_PRICE.to_f/@conversion_rate).round(2) : 0
      session[:gift_wrapped] = !gift_wrap.nil?
      if available_in_warehouse  #### same product both SOR and Express case pending
        gon.rts_delivery_time = (gon.country_wise_shipping_time[@order.country.try(:downcase)].to_f + addon_delivery_time).round.to_s
      elsif @cart.ready_to_ship_designs? && EXPRESS_DELIVERY.to_f >= 0
        gon.express_delivery_time = (READY_TO_SHIP_DESIGNS.to_i +  @cart.vacation_day_count).to_s
      end
      gon.shipping_time = gon.delivery_time
      @total_cod_charges = Order.domestic_cod_charge(@order.pincode, @cart)||0
      gon.cod_charges   = (@total_cod_charges/@conversion_rate).ceil
      #gon.cod_charges = (COD_CHARGES/@conversion_rate).ceil
      gon.enable_cod_countries = ENABLE_COD_COUNTRIES
      gon.paypal_client_id = ENV['PAYPAL_CLIENT_ID']
      gon.env_name = Mirraw::Application.config.paypal_smart_pay[:env]
      gon.currency_symbol = @symbol

      gon.weight = @cart.get_weight
      gon.total = @cart.total_price_currency(@conversion_rate)
      gon.domestic_shipping_cost = (Country.shipping_cost_for(nil,'India',gon.total,@cart.line_items).to_f / @conversion_rate).to_f
      respond_to do |format|
        format.html {render "new"}
      end
    else
      redirect_to root_url
    end
  end

  def calculate_delivery_time
    delivery_time, india_delivery_time, line_items_rts, addon_time, available_in_warehouse, vendor_pickup_locations, designer_ids ,designer_pincodes= 0, 0, [], 0, [], [], [],[]
    @cart.designer_stores.each do |stores|
      stores[:items].each do |item|
        designer_pincodes << (item.design.designer.business_pincode.presence || item.design.designer.pincode)
        vendor_pickup_locations << (item.design.sor_available? ? 'bhwandi' : item.design.designer.pickup_location.try(:downcase))
        designer_ids << item.design.designer_id
        if item.variant || item.line_item_addons.blank?
          in_warehouse = session[:country][:name].try(:downcase) != 'india'&& (item.variant_id.present? ? item.variant.sor_available_for_customer? : item.design.sor_available_for_customer?)
          line_items_rts << in_warehouse
          available_in_warehouse << in_warehouse
        end
        designer_eta = item.design.designer_shipping_time('international')
        item.line_item_addons.each do |addon|
          prod_time  = addon.addon_type_value.prod_time
          design_eta = item.design.sor_available_for_customer? ? 0 : designer_eta
          if addon.size_chart_id.present?
            if addon.open_addon?
              in_warehouse = item.design.size_in_warehouse?(quantity: item.quantity)
              line_items_rts << in_warehouse
              available_in_warehouse << in_warehouse
              design_eta = designer_eta if !in_warehouse
            elsif addon.standard_addon?
              if item.design.size_in_warehouse?(addon.size_chart.size_bucket_id, quantity: item.quantity)
                prod_time = 0
                line_items_rts << true
                available_in_warehouse << true
              elsif item.design.size_in_warehouse?(quantity: item.quantity)
                line_items_rts << false
                available_in_warehouse << true
              else
                line_items_rts << false
                available_in_warehouse << false
                design_eta = designer_eta
              end
            elsif addon.custom_addon?
              in_warehouse = item.design.size_in_warehouse?(quantity: item.quantity)
              line_items_rts << false
              available_in_warehouse << in_warehouse
              design_eta = designer_eta if !in_warehouse
            end
          elsif addon.standard_addon?
            line_items_rts << false
          end
          if delivery_time < design_eta + prod_time
            delivery_time = design_eta + prod_time
          end
          addon_time = prod_time if addon_time < prod_time
        end
        delivery_time = designer_eta if (delivery_time < designer_eta) && item.line_item_addons.blank? && !item.design.sor_available_for_customer?
        india_delivery_time = item.design.designer_shipping_time if india_delivery_time < item.design.designer_shipping_time
      end
    end
    @display_rts_msg = line_items_rts.uniq.length > 1
    vendor_pickup_locations = vendor_pickup_locations.compact.uniq
    gon.max_vendor_eta = india_delivery_time
    if @delivery_city.present?
      if !DISABLE_ADMIN_FUCTIONALITY['pdd_from_cp']
        click_post = ClickPostAutomation.new()
        promise_days = click_post.get_eta_from_clickpost(@delivery_city, designer_pincodes).to_i
        if promise_days > 0
          promise_days += india_delivery_time.to_i
          gon.indian_shipping_time = promise_days
          gon.cp_eta = promise_days
          gon.can_use_cp_pdd = !DISABLE_ADMIN_FUCTIONALITY['pdd_from_cp']
          gon.pickup_pincodes = designer_pincodes if gon.can_use_cp_pdd
        end
      end
      if promise_days.to_i == 0
        gon.lane_functionality = LANE_FUNCTIONALITY
        promise_days = DeliveryNpsInfo.get_city_based_pdd(@delivery_city, vendor_pickup_locations)
        promise_days += india_delivery_time unless vendor_pickup_locations.count == 1 && vendor_pickup_locations.include?('bhwandi')
      end
      if designer_ids.uniq.count <= ESSENTIAL_DESIGNERS['designer_ids'].count && (ESSENTIAL_DESIGNERS['designer_ids'].map(&:to_i) & designer_ids.uniq) == designer_ids.uniq
        promise_days = ESSENTIAL_DESIGNERS['max_eta']
        gon.can_exclude_designers = false
      else
        gon.can_exclude_designers = true
      end
      promise_days += (DESIGNER_LSR.hours.to_f/1.days).round.to_i unless vendor_pickup_locations.count == 1 && vendor_pickup_locations.include?('bhwandi')
      promise_days += 1 if Time.current.advance(days: promise_days).sunday?
      gon.indian_shipping_time = promise_days
    else
      if false # very fishy line, dont know y default address is needed for this
        new_eta = DeliveryNpsInfo.get_city_based_pdd(default_address.city.to_s.downcase, vendor_pickup_locations).round
        new_eta += india_delivery_time unless vendor_pickup_locations.count == 1 && vendor_pickup_locations.include?('bhwandi')
      else
        new_eta = Lane.get_max_range_eta_from_lane(vendor_pickup_locations) 
        new_eta += india_delivery_time unless vendor_pickup_locations.count == 1 && vendor_pickup_locations.include?('bhwandi')
      end
      design_eta = new_eta > 0 ? new_eta : (Country.country_wise_delivery_time('india') + india_delivery_time).round
      if design_eta.to_i > 0
        design_eta += (DESIGNER_LSR.hours.to_f/1.days).round.to_i unless vendor_pickup_locations.count == 1 && vendor_pickup_locations.include?('bhwandi')
        design_eta += 1 if Time.current.advance(days: design_eta).sunday?
      end
      gon.indian_shipping_time =  design_eta
    end
    gon.vendor_pickup_locations = vendor_pickup_locations
    gon.metro_cities = METRO_CITIES
    gon.city_based_ship_time = CITY_BASED_SHIP_TIME
    [(delivery_time + Designer::SLA_CONFIG[:non_stitching_preparation_days]), (addon_time + Designer::SLA_CONFIG[:non_stitching_preparation_days]), (available_in_warehouse.uniq.length == 1 && available_in_warehouse.first), india_delivery_time, vendor_pickup_locations]
  end

  # POST /orders
  # POST /orders.json
  def retry_cod
    # Order Creation from canceled orders
    @canceled_order = Order.unscoped.find_by_number(params[:id].upcase)
    if @canceled_order.present? && (@canceled_order.cancel? || @canceled_order.cancel_complete? || @canceled_order.pending?)
      @canceled_order.state = 'new'
      @canceled_order.pay_type = COD
      @canceled_order.payment_state = 'pending_payment'
      @canceled_order.utm_source = 'Redirect from mail, order updated to COD, Desktop'
      #@canceled_order.cod_charge = COD_CHARGES
      if @canceled_order.billing_address != @canceled_order.shipping_address
        @canceled_order.copy_shipping_details_to_billing
      end
      respond_to do |format|
        if @canceled_order.save
          @canceled_order.attempt_auto_confirm if @canceled_order.cod?
          @canceled_order.post_order_confirmation_work
          total_cod_charges = Order.post_domestic_cod_charge(@canceled_order.pincode, @canceled_order.designer_orders)
          if total_cod_charges
            @canceled_order.cod_charge = total_cod_charges
            @canceled_order.save!
            format.html { redirect_to(order_url(@canceled_order.number), :notice => 'Thank you for your order.') }
          else
            @canceled_order.cancel!
            format.html { redirect_to(root_url, :notice => 'COD not available on this order.') }
          end
        end
      end
    else
      redirect_to root_url
    end
  end

  def paypal_create(order, country_code, symbol)
    return_url = "/orders/#{order.number}"
    currency_symbol = symbol
    rate = order.currency_rate
    order.add_notes_without_callback("paypal: waiting", 'payment', current_account)
    payapal_api = Payment::Paypal::PaypalV2RestApi.new(order)
    auth_token = payapal_api.get_authentication_token
    render json: {"has_error" => true, "errors" => "Some Error Accoured Please Try Again In Some Time"} and return if !auth_token

    paypal_request = Payment::Paypal::PaypalApiData.new(order, country_code, symbol)
    payload = paypal_request.get_checkout_v2_request_payload
    response_hash = payapal_api.create_payment(payload, auth_token)
    if !response_hash.key?("has_error")
      session[:order_number] = order.number
      if response_hash['id'].present?
        order.token = response_hash['id']
      else
        ExceptionNotifier.notify_exception(
          Exception.new("Paypal Smartpay empty ID/Token Error-"),
          data: {payload: paypal_request, response: response_hash, order_number: order.number}
        )
        ExceptionNotify.sidekiq_delay.notify_exceptions('Paypal Smartpay empty ID/Token Error', response_hash, { params: response_hash, order_number: order.number})
      end
    end
    order.skip_before_filter = true
    order.save!
    return response_hash.merge({:return_url => return_url})
  end

  def paypal_execute
    begin
      order = Order.find_by_token params[:orderID]
      payapal_api = Payment::Paypal::PaypalV2RestApi.new(order)
      payment_response = payapal_api.capture_payment
      if payment_response.key?("has_error") 
          order.update_column("paypal_error", [payment_response['name'], payment_response['message'], payment_response['errors']].compact.join(' || '))
      else
        Order.sidekiq_delay.store_paypal_smartpay_v2_api_details(payment_response, order.number)
      end
      render json: payment_response
    rescue => error
      ExceptionNotifier.notify_exception(
        Exception.new("Paypal Rutime Exception"),
        data: {error: error.inspect}
      ) 
      ExceptionNotify.sidekiq_delay.notify_exceptions('Paypal Rutime Exception', error.inspect, {})
      Order.where(number: session[:order_number]).update_all(paypal_error: error.inspect)
      render json: {has_error: true, message: "Some Exception Occured Please Try Again"}
    end
  end

  def paypal_response_handling
    return_url = params[:error].present? ? (params[:redirect_root].present? && params[:redirect_root] != 'undefined' ? root_url : new_order_url) : params[:returnUrl]
    notice = params[:error].present? ? (params[:messages].present? && params[:messages] != 'undefined' ? params[:messages] : 'Oops, something went wrong. Please try a different payment method.') : 'Thank you for your order.'
    redirect_to return_url, notice: notice
  end

  def create
    # check if quantity is avalable
    @hide_menu = true
    #gon.cod_charges   = (COD_CHARGES/@conversion_rate).ceil
    cart_categories = @cart.line_items.map{|li| li.categories.pluck(:name)}.flatten.uniq
    current_cart_hash = @cart
    if ( BANNED_COUNTRIES['countries'] & [order_params[:country].try(:downcase), order_params[:billing_country].try(:downcase), @actual_country.try(:downcase)]).present?
      respond_to do |format|
        format.html { redirect_to(root_url, :notice => BANNED_COUNTRIES['reason']) }
        format.json {render json: {name: 'VALIDATION_ERROR', errors: BANNED_COUNTRIES['reason'], redirect_root: 'true'}}
      end
    elsif (NON_SERVICABLE.include?(order_params[:billing_pincode].presence))
      respond_to do |format|
        format.html { redirect_to(new_order_url, :notice => 'We are currently not delivering to this location.') }
        format.json {render json: {name: 'VALIDATION_ERROR', errors: 'We are currently not delivering to this location.', redirect_root: 'true'}}
      end
    elsif (cart_categories & PINCODE_BASED_CATEGORY_DELIVERY['categories']).presence && PINCODE_BASED_CATEGORY_DELIVERY['pincodes'].exclude?(order_params[:pincode].presence || order_params[:billing_pincode])
      respond_to do |format|
        format.html { redirect_to(new_order_url, :notice => PINCODE_BASED_CATEGORY_DELIVERY['reason']) }
        format.json {render json: {name: 'VALIDATION_ERROR', errors: PINCODE_BASED_CATEGORY_DELIVERY['reason'], redirect_root: 'true'}}
      end
    elsif current_cart_hash.check_out_of_stock?((params[:order][:pay_type].to_s == COD || ((order_params[:country].presence || order_params[:billing_country]).try(:downcase) == @actual_country.try(:downcase) && @actual_country.try(:downcase) == 'india')) ? 'india' : 'international')
      respond_to do |format|
        format.html { redirect_to(root_url, :notice => 'Design is out of stock. So order couldn''t be placed.') }
        format.json {render json: {name: 'VALIDATION_ERROR', errors: 'Design is out of stock. So order couldn''t be placed.', redirect_root: 'true'}}
      end
    else
      @order = Order.new(order_params.except(:paypal_inline_cc, :paypal_smart_pay))
      if @country_code == 'IN' && @order.billing_country.try(:downcase) == 'india'
        phone_number = Order.get_mobile_num(@order.billing_phone)
        @order.billing_phone = phone_number != 0 ? phone_number : @order.billing_phone
      end
      @order.cart = current_cart_hash
      @order.track = 0
      @order.currency_rate = @conversion_rate
      @order.currency_code = @symbol
      @order.currency_rate_market_value = CurrencyConvert.countries_marketrate[@country_code]
      @order.actual_country_code = Design.country_code
      @order.ccavenue_customer_identifier = @theme_tester.try(:current_theme)
      #check if cart was verified for cod order
      is_cod_order = @order.cod?
      @order.validate_cod_otp = is_cod_order
      # ship_to_same_address = false
      if @order.billing_email.present?
        @order.billing_email.strip!
        @order.email = @order.billing_email
      end
      if @ship_to_same_address = (is_cod_order || (params[:ship_to_same_address].present? && params[:ship_to_same_address] == "1"))
        @order.copy_billing_details_to_shipping
      end
      @order.app_source = 'Desktop'.freeze
      @order.set_order_number
      if session[:cart_upsell].present?
        @order.add_notes(session[:cart_upsell], false)
        session[:cart_upsell] = nil
      end
      @order.country_code = @country_code
      if (country = Country.get_country_group[@order.country.to_s]).present?#Country.find_by_name(@order.country)
        if @order.buyer_state.present?
          @order.state_code = country.try(:[],:states).try(:[], @order.buyer_state)#State.where(:country_id => country.id, :name => @order.buyer_state).pluck(:iso3166_alpha2).first
        end
      else
        @order.country = nil
      end

      if (gift_wrap = session[:gift_wrapped] && GIFT_WRAP_PRICE.to_f >= 0)
        @order.add_order_addons(gift_wrap_price: gift_wrap)
      end

      if params[:delivery_type] == 'express'.freeze && country[:express_delivery_charge] > 0 && EXPRESS_DELIVERY.to_f >= 0 && current_cart_hash.ready_to_ship_designs?
        @order.add_notes('Ready_To_Ship_Order', false, current_account)
        @order.express_delivery = country[:express_delivery_charge]
      end
      #prepare for delay things
      @order.delay_queue(priority: -10, queue: 'high_priority')
      # From this point on you are on a mission to create an order.
      # You need to make sure world beneath you doesn't change.
      # If buyer wants X quantity to order and if the world has changed
      # such that you can't. Then fail the order, alert the buyer.
      # You have to take control of the world. Check that you can
      # fulfill the wish of a buyer and only then move forward.
      # Oh and now release control of the world, so that someone
      # else can take control.

      # What are the model changes you are doing ?
      #
      # Order - creating new order
      # Buyer - Possibly creating new buyer
      # Cart - Destroying cart
      # Design - decrementing design quantity
      # Designer Order - Creating designer orders from an order
      # 
      # No transaction for now!. We will take orders.
      #

      #
      #  user = User.find_by_email(:email => params[:email])
      #  if user
      #     @order.user = user
      #  else
      #     Createuser;
      #     CreateAccount;
      #     Save Address_In_User_Profile
      #     
      #   end
      #   @order.user = user
      #

      # Save Referral Info
      copy_from_session
      session[:order_number] = @order.number

      # user details update
      send_newsletter = params[:newsletter_confirmation] == 'send_newsletter'
      if account_signed_in? && current_account.user? && current_account.user
        user = current_account.user
        @order.user = user
      end
      @order.delay_queue.save_user_details(
        send_newsletter: send_newsletter,
        ip_address: (request.headers['True-Client-IP'] || request.ip),
        source_url: request.env["HTTP_REFERER"] || request.referrer,
        country_code: @country_code,
        password: params[:password]
      )
      # cod related stuff
      @cod = nil
      total_cod_charges = 0
      conversion_rate = @conversion_rate
      if is_cod_order
        current_cart_hash.designer_stores.each do |d|
          order_country = @order.country.downcase
          if (order_country!='india' && ENABLE_COD_COUNTRIES.include?(order_country) && @cart.all_products_in_warehouse?)
            break
          end
          unless d[:cod]
            @order.pay_type = nil 
            @cod = false
            break
          end
        end
        total_cod_charges = Order.domestic_cod_charge(params[:order][:billing_pincode], current_cart_hash)||0
        @order.cod_charge = total_cod_charges
        #@order.cod_charge = COD_CHARGES
        gon.cod_charges   = (total_cod_charges/conversion_rate).ceil
        @order.add_notes('quick_cod_order', false) if params[:quick_cod_request] == 'true'
      end
      if total_cod_charges != 0
        @order.delay_queue.cod_available_on_create_order
      else
        @order.cod_available = false
      end
      coupon = current_cart_hash.coupon
      if coupon.present? && coupon.is_shipping? && (current_cart_hash.total_price_currency(1, :referral) >= coupon.min_amount)
        @order.coupon = coupon
        # can be delayed
        coupon.increment!(:use_count)
      end
      @order.add_line_items_from_cart(current_cart_hash)
      @order.assign_attempted_payment_gateway
      @order.attempted_payment_gateway = params[:order][:attempted_payment_gateway] if params[:order][:attempted_payment_gateway].present?
      @order.assign_warehouse_addresses_to_designer_orders
      if (@order.billing_international? && @order.online_payment? && rand(1..ENV['REDIRECT_TO_STRIPE'].to_i) == 1) && @order.stripe_currency_allowed?
        @order.payment_gateway = 'stripe'
        @order.attempted_payment_gateway = 'stripe'
      end

      if @order.paypal? || (@order.billing_international?)
        if @order.domestic_paypal?(@order.country_code, @order.currency_code)
          @order.paid_currency_code = 'INR'
          @order.paid_currency_rate= 1
          @order.payment_gateway = 'paypal'
          @order.attempted_payment_gateway = 'domestic_paypal'
        else
          @order.paid_currency_rate = @order.get_paypal_rate
          @order.paid_currency_code = Order::PAYPAL_ALLOWED_CURRENCIES.include?(@order.currency_code) ? @order.currency_code : 'USD'
        end
        @order.delay_queue.update_paypal_rate #if (Order::ATLANTIC_SUPPORTED_CURRENCIES & [@order.paid_currency_code, @order.currency_code]).blank?
      elsif @order.international?
        @order.delay_queue.update_paypal_rate
      end
      if SOR_READY_TO_SHIP && current_cart_hash.warehouse_available_designs?
        deliver_time, addon_time, available_in_warehouse, india_delivery_time, vendor_pickup_locations = calculate_delivery_time
        if available_in_warehouse
          @order.add_notes_without_callback('Ready_To_Ship_Order_SOR', 'delivery')
          @order.express_delivery = 0
        end
      end
      #@order.delay_queue.reduce_quantity_from_warehouse unless @order.bank_deposit?

      current_bmgn_offer = PromotionPipeLine.current_bmgn_offer
      @order.other_details['bmgn_promotion'] = current_bmgn_offer unless current_bmgn_offer.nil?

      if @order.post_paid?
        @order.delay_queue.post_order_confirmation_work
      else
        @order.add_notes_without_callback("Waiting On Online Payment", 'payment', current_account)
      end
      respond_to do |format|
        if @order.save
          @order.adjust_combo_variant_quantity
          cookies['cart_reload'] = nil

          # moved to before_save to call in transaction
          # if account_signed_in? && current_account.user? && current_account.user
          #   if current_cart_hash.wallet.present? && !is_cod_order && current_cart_hash.wallet.currency_convert.country_code == @order.actual_country_code
          #     # shipping till now has not been set which is required for calculation below
          #     ## because already calculated
          #     # @order[:shipping] = @order.get_shipping_cost(@order.country) if @order.international?
          #     @order.referral_discount = current_cart_hash.wallet_discounts(:referral).round(2)
          #     cart_return_amount = current_cart_hash.wallet_discounts(:return)
          #     @order.refund_discount = cart_return_amount
          #     @order.refund_discount += @order.deduct_shipping_from_wallet(cart_return_amount).to_f.round(2) if cart_return_amount > 0
          #     wallet_transaction_event = @order.bank_deposit? ? :wait : nil
          #     @order.do_wallet_transaction(wallet_transaction_event)
          #     @order.set_total_order_value
          #     @order.skip_before_filter = true
          #     @order.save!
          #   end
          # end
          # this must be called after wallet amounts have been set above.

          # I doubt if it is required now
          # @order.set_total_order_value

          # this is the last save thats why skip before filter is used make it false again if we need to save the order again
          # @order.skip_before_filter = true
          # @order.save!
          if is_cod_order
            Order.post_confirmation_postpaid_cod(@order)
          end
          # Destroy cart now
          if DESTROY_CART
            Cart.find_by_id(session[:cart_id]).update_attribute(:used, true)
            session[:cart_id] = nil
          end

          if !is_cod_order && @order.wallet_discount > 0 && @order.total <= 0
            @order.delay_queue.update_columns(pay_type: WALLET, attempted_payment_gateway: nil)
            @order.delay_queue.paid_via_wallet
          elsif @order.total <= 0
            @order.delay_queue.good_data! if @order.can_good_data?
          end

          @order.delay_queue.execute
          if @order.pay_type == WALLET || @order.post_paid? || @order.total <= 0
            format.html { redirect_to(order_url(@order.number), :notice => 'Thank you for your order.')}
            format.json {render json: {redirect_to: order_path(@order), reason: "Payment Already Completed"} }
          else
            # @order.sidekiq_delay_until(30.minutes.from_now, queue: 'high')
            #       .cancel_unless_confirmed
            SidekiqDelayGenericJob.set({queue: 'high'}).perform_in(30.minutes.from_now,@order.class.to_s, @order.id,"cancel_unless_confirmed")
            case @order.attempted_payment_gateway
            when 'paypal_smartpay'
              res = paypal_create(@order, @country_code, @symbol)
              format.json {render json: res}
            when  'paypal', 'domestic_paypal'
              format.html { redirect_to @order.paypal_payments_standard_url(order_url(@order.number), @symbol || session[:country][:symbol], @country_code)}
            when 'payu_money'
              @values = @order.payu_billing_page_url_non_seamless(order_payu_response_url, true)
              format.html {render 'payu_request', layout: false}
            when PAYTM
              @values = @order.paytm_non_seamless_parameters
              format.html {render 'paytm_request', layout: false}
            when 'razorpay'
              session[:razorpay] = 'razorpay_gateway'
              #session[:order_number] = @order.number #should be already present
              format.html {redirect_to new_order_url}
            when 'payu'
              format.html {redirect_to @order.payu_billing_page_url_non_seamless(order_payu_response_url)}
            when 'stripe'
              session[:stripe] = true
              format.html {redirect_to new_order_url}
            end
          end

          #OldCode if !is_cod_order && @order.total <= 0 && @order.wallet_discount > 0
          #OldCode   @order.delay_queue.update_column(:pay_type, WALLET)
          #OldCode   (@order.delay_queue.paid_via_wallet).execute
          #OldCode   format.html { redirect_to(order_url(@order.number), :notice => 'Thank you for your order.')}
          #OldCode elsif @order.bank_deposit? || is_cod_order || @order.gharpay? || @order.total <= 0
          #OldCode   if @order.total <= 0
          #OldCode     (@order.delay_queue.good_data!).execute 
          #OldCode   else
          #OldCode     @order.delay_queue.execute 
          #OldCode   end
          #OldCode   format.html { redirect_to(order_url(@order.number), :notice => 'Thank you for your order.') }
          #OldCode elsif @order.paypal?
          #OldCode   @order.delay({:run_at => 30.minutes.from_now}).cancel_unless_confirmed
          #OldCode   if paypal_smart_pay
          #OldCode     @order.delay_queue.update_payment_gateway_error_code(order_attempted_payment_gateway: 'paypal_smartpay').execute
          #OldCode     paypal_create(@order, @country_code, @symbol)
          #OldCode   else
          #OldCode     @order.delay_queue.update_payment_gateway_error_code(order_attempted_payment_gateway: 'paypal').execute
          #OldCode     format.html { redirect_to @order.paypal_payments_standard_url(order_url(@order.number), @symbol || session[:country][:symbol], @country_code)}
          #OldCode   end
          #OldCode elsif (@order.billing_international? || @order.actual_country_code != 'IN') && @order.online_payment?
          #OldCode   @order.delay({:run_at => 30.minutes.from_now}).cancel_unless_confirmed
          #OldCode   @order.delay_queue.update_payment_gateway_error_code(order_attempted_payment_gateway: 'paypal').execute
          #OldCode   format.html { redirect_to @order.paypal_payments_standard_url(order_url(@order.number), @symbol || session[:country][:symbol], @country_code)}
          #OldCode elsif @order.online_payment? && @order.payu_money?
          #OldCode   @order.delay({queue: 'high_priority', priority: -3, :run_at => 30.minutes.from_now}).cancel_unless_confirmed
          #OldCode   @order.delay_queue.update_attribute('attempted_payment_gateway', 'payu_money').execute
          #OldCode   @values = @order.payu_billing_page_url_non_seamless(order_payu_response_url, true)
          #OldCode   format.html {render 'payu_request', layout: false}
          #OldCode elsif @order.paytm? && !@order.billing_international? && @order.actual_country_code == 'IN'
          #OldCode   @order.delay({queue: 'high_priority', priority: -3, run_at: 30.minutes.from_now}).cancel_unless_confirmed
          #OldCode   @order.delay_queue.update_attribute('attempted_payment_gateway', PAYTM).execute
          #OldCode   @values = @order.paytm_non_seamless_parameters
          #OldCode   format.html {render 'paytm_request', layout: false}
          #OldCode else
          #OldCode   @order.delay({queue: 'high_priority', priority: -3, :run_at => 30.minutes.from_now}).cancel_unless_confirmed
          #OldCode   if session[:payment_gateway].present? && session[:payment_gateway] == 'PAYU'
          #OldCode     if rand(ENV['REDIRECT_TO_RAZORPAY'].to_i)==1 #randomly select razorpay option
          #OldCode       @order.delay_queue.update_attribute(:attempted_payment_gateway, 'razorpay').execute
          #OldCode       session[:razorpay] = 'razorpay_gateway'
          #OldCode       #session[:order_number] = @order.number #should be already present
          #OldCode       redirect_to new_order_url
          #OldCode     else    #use payu otherwise
          #OldCode       @order.delay_queue.update_attribute('attempted_payment_gateway', 'payu').execute
          #OldCode       redirect_to @order.payu_billing_page_url_non_seamless(order_payu_response_url)
          #OldCode     end
          #OldCode     return
          #OldCode   else
          #OldCode     # if false
          #OldCode     #   @merchant_id = 'M_sha21501_21501'
          #OldCode     #   @merchant_key = '39sxhhs7xv2n2y2us2'
          #OldCode     #   @checksum =  get_checksum(@merchant_id,
          #OldCode     #                           @order.number,
          #OldCode     #                           @order.total,
          #OldCode     #                           order_response_url,
          #OldCode     #                           @merchant_key)
          #OldCode     #   @order.update_attribute('attempted_payment_gateway', 'old ccavenue')
          #OldCode     #   format.html {render "autopost", :layout => false}
          #OldCode     # else
          #OldCode     #   @order.update_attribute('attempted_payment_gateway', 'mpcg ccavenue')
          #OldCode     #   @encrypted_data = ccavenue_mpcg_billing_page(@order)
          #OldCode     #   @access_code = Mirraw::Application.config.ccavenue_mpcg_access_code
          #OldCode     #   @ccavenue_mpcg_transaction_url = Mirraw::Application.config.ccavenue_mpcg_url + '/transaction/transaction.do?command=initiateTransaction'
          #OldCode     #   format.html {render "ccavenu_mcpg_order_request", :layout => false}
          #OldCode     # end
          #OldCode   end
          #OldCode end
        else
          if @ship_to_same_address
            @order.errors.keys.each do |key|
              if @order.errors[key].first =~ /^Shipping/  
                @order.errors.delete(key)
              end
            end
          end
          # Calculate possible international shipping cost
          international_shipping_cost = current_cart_hash.get_international_shipping_cost(@conversion_rate, @order.country)
          gon.international_shipping_cost = international_shipping_cost
          gon.international_shipping_cost_currency = @symbol.to_s+' '+international_shipping_cost.to_s
          # gon.international_shipping_cost_currency = current_cart_hash.get_international_shipping_cost_in_currency_with_symbol(@conversion_rate,@symbol, @order.country)
          gon.weight = current_cart_hash.get_weight
          gon.total = current_cart_hash.total_price_currency(@conversion_rate)
          gon.international_minimum_order_value = (5000/@conversion_rate).ceil
          gon.international_minimum_order_value_currency = @symbol + '.' + gon.international_minimum_order_value.to_s
          gon.country_wise_shipping_time = Country.get_country_shipping_time
          gon.delivery_time, addon_delivery_time, available_in_warehouse, india_delivery_time, vendor_pickup_locations = calculate_delivery_time
          set_gon_variables
          gon.shipping_time = (gon.delivery_time + current_cart_hash.vacation_day_count).to_f.round
          @integration_status = "new"
          set_smart_pay_visiblity
          if @paypal_smartpay || @show_inline_cc
            format.json {render json: {name: 'VALIDATION_ERROR', errors: @order.errors.messages.values.flatten.join(', ')}}
          end
          if params[:form_type] == "new2"
            format.html { render action: "new2" }
          else
            format.html { render action: "new"}
          end
        end
      end      
    end
  end

  # PUT /orders/1
  # PUT /orders/1.json
  def update
    @order = Order.find(params[:id])

    respond_to do |format|
      if @order.update_attributes(params[:order])
        format.html { redirect_to order_url(@order.number), notice: 'Order was successfully updated.' }
        format.json { head :ok }
      else
        format.html { render action: "edit" }
        format.json { render json: @order.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /orders/1
  # DELETE /orders/1.json
  def destroy
    @order = Order.find(params[:id])
    @order.destroy

    respond_to do |format|
      format.html { redirect_to orders_url }
      format.json { head :ok }
    end
  end

  def add_design
    @order = Order.preload(:stitching_measurements).find(params[:order_id])
    @events= @order.events
    if (design = Design.includes(:variants => :option_type_values).where(:id => params[:design_id]).first).present?
      params['add_design'] = true
      if (response = @order.get_old_item_id(design, params))[:more_input]
        render :json => response
      else
        if @order.add_design(design, current_account, response[:old_item_id])
          @order.add_measurements(design.id,params[:replace_data]) if params[:replace_data].present? && (params[:replace_data][0].to_i != design.id) && @order.stitching_measurements.any?{|sm| sm.design_id == params[:replace_data][0].to_i} && params[:replace_data].length >= 2
          @order.add_notes_without_callback("Added new design #{design.id}", 'replacement', current_account) 
          # @order.sidekiq_delay(queue: 'low').update_cost_estimation if @order.international?
          SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(@order.class.to_s, @order.id, "update_cost_estimation") if @order.international?
          render :template => '/orders/add_design'
        else
          render :json => {:error => 'Error adding design to order. Please check design stock'}
        end
      end
    else
      render :json => {:error => "Wrong (#{params[:design_id]}) design id entered please check again"}
    end
  end
  
  def add_variant
    @order = Order.preload(designer_orders: :line_items).where(id: params[:order_id]).first
    if (variant = Variant.where(:id => params[:variant_id]).where('quantity > 0').includes(:design => :designer, :option_type_values => :option_type).first)
      search_clause = params[:old_variant_id].present? ? {variant_id: params[:old_variant_id]} : {design_id: params[:old_design_id]}
      old_item_id = @order.line_items.where(search_clause).pluck(:id).first
      if @order.add_variant(variant, old_item_id)
        @order.add_notes_without_callback("Added new variant #{variant.id} of design #{variant.design.id}",'replacement',current_account)
        note = "Added new variant #{variant.option_types.first.name} : #{variant.option_type_values.first.name} of design #{variant.design.id}"
        DesignerMailer.send_design_updates_to_designer(variant.design.designer,@order.number,note).deliver_now
        # @order.sidekiq_delay(queue: 'low').update_cost_estimation if @order.international?
        SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(@order.class.to_s, @order.id, "update_cost_estimation") if @order.international?
        render :template => '/orders/add_design'
      else
        render :json => {:error => 'Error adding variant/size to order. Please check design variant/size stock'}
      end
    else
      render :json => {:error => 'Wrong ('+params[:variant_id]+') design variant/size id entered please check again'}
    end
  end

  def delete_line_item
    # Prefetching design relations
    line_item = LineItem.includes(:design => [:images, :variants]).find_by_id(params[:id])
    canceled = true
    if line_item.present?
      designer_order_id = line_item.designer_order_id
      designer_order = DesignerOrder.includes(:line_items).find_by_id(designer_order_id)
      @order = Order.all_rel_min.find_by_id(designer_order.order_id)
      if (canceled = designer_order.cancel_designer_order_or_item(params[:reason], line_item: line_item))
        @order.add_notes_without_callback("Removed line item with design id #{line_item.design_id} with quantity #{line_item.quantity}", 'replacement', current_account)
        line_item.add_into_scan('Line Item Canceled', current_account.id)
      end
      # Update the amount in gharpay system
      if @order.gharpay?
        # You can no longer update items in the existing order
      end
      # @order.sidekiq_delay(queue: 'low').update_cost_estimation if @order.international?
      SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(@order.class.to_s, @order.id,"update_cost_estimation") if @order.international?
    end
    respond_to do |format|
      format.js
    end
  end
  
  def update_line_item
    @line_item = LineItem.find(params[:id])  
    @designer_order = @line_item.designer_order
    @order = @designer_order.order
    old_quantity = @line_item.quantity
    @order.update_line_item(@line_item, params[:quantity].to_i)
    @order = Order.where(:id => @order.id).first
    new_quantity = @line_item.quantity
    @order.add_notes_without_callback("Updated line item quantity for design id #{@line_item.design_id} old quantity was #{old_quantity} new quantity is #{new_quantity}", 'other', current_account) if @order.save!
    # @order.sidekiq_delay(queue: 'low').update_cost_estimation if @order.international?
    SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(@order.class.to_s, @order.id, "update_cost_estimation") if @order.international?
    @line_item.add_into_scan('Quantity Updated', current_account.id) if new_quantity != old_quantity
    respond_to do |format|
      format.js
    end
  end

  def update_shipping_address
    response = {error: "error updating shipping address"}
    order = Order.find_by_id(params[:order_id])
    if params[:source] == 'By User From Order Show'
      edit_attributes = {street: params[:new_street],city: params[:new_city],pincode: params[:new_pincode],buyer_state: params[:new_state]}
    else
      edit_attributes = {name: params[:new_name],street: params[:new_street],city: params[:new_city],pincode: params[:new_pincode],buyer_state: params[:new_state]}
      edit_attributes.merge!({state_code:params[:new_state_code],country_code:params[:new_country_code],country:params[:new_country]}) if order.international? || !order.cod?
      edit_attributes.merge!({phone: params[:new_phone], billing_phone: params[:new_bill_phone]}) if DEPARTMENT_HEAD_EMAILS['support'] == current_account.email
    end
    if order.present? 
      order.assign_attributes(edit_attributes)
      if order.save
        order.add_notes_without_callback('shipping_address changed','dispatch',current_account)
        # order.sidekiq_delay.address_verification if order.international?
        SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id,"address_verification") if order.international?
        response = {msg: "Successfully Updated"}
      end
    end
    render json: response
  end

  def send_order_ack_email
    @order = Order.all_rel_min.where(:number => params[:number]).first
    SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_order_details_to_buyer", {"#{@order.class}": @order.id}) if @order.present? && (@order.designer_orders.count >= 1)
    # OrderMailer.sidekiq_delay.mail_order_details_to_buyer(@order) if @order.present? && (@order.designer_orders.count >= 1)
    message = @order.present? ? 'Mail Sent successfully' : "This order number '#{params[:number]}' does not exists."
    respond_to do |format|
      format.js{ render json: {nothing: true, notice: message}}
      format.html{redirect_to :back, notice: message}
    end
  end
  
  def send_ship_direct_email
    @order = Order.all_rel_min.where(:number => params[:number]).first
    @order.tag_list.add('direct')
    @designer = @order.designer_orders.first.designer
    @designer.adjustments.create!(:order => @order, :notes => "Shipping Cost for #{@order.number}", :amount => @order.shipping, :status => 'unpaid') if @designer.adjustment_allowed?
    @order.save
    SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "ship_direct", {"#{@order.class}": @order.id}) if @order.present? && (@order.designer_orders.count == 1)
    #OrderMailer.sidekiq_delay.ship_direct(@order) if @order.present? && (@order.designer_orders.count == 1)
    render :nothing => true
  end
  
  def convert_to_gharpay
    @order = Order.all_rel_min.where(:number => params[:number]).first
    @order.move_to_pending! if @order.state == "cancel"
    @order.pay_type = GHARPAY
    @order.save

    @order.post_order_confirmation_work if @order.present?
    render :nothing => true
  end

  def multiple_local_invoice_links
    @order = Order.all_rel_min.where(:number => params[:number]).first
    @order.move_to_pending! if @order.state == "cancel"
    if params[:use].blank?
      require 'mechanize'

      agent = Mechanize.new
      agent.user_agent = "Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_2; ru-ru) AppleWebKit/533.2+ (KHTML, like Gecko) Version/4.0.4 Safari/531.21.10"

      # Take mirraw url as input
      # Login
      agent.verify_mode = OpenSSL::SSL::VERIFY_NONE
      page = agent.get("https://mars.ccavenue.com/mer_register/subuser_login.jsp")
      form = page.form_with(:name => "frm_login")
      form["userId"] = "M_sha21501_21501"
      form["subuserName"] = "support"
      form["password"] = "Apricot@218"
      form.action = 'subuser_login_process.jsp'
      form.submit
      #puts page.uri

      # Customer Data
      agent.verify_mode = OpenSSL::SSL::VERIFY_NONE
      page = agent.get("https://mars.ccavenue.com/mer_register/sendInvoice/sendInvoiceDetails_ccav.jsp")
      form = page.form_with(:name => "invoice")
      form["get_custName"] = @order.billing_name.strip
      form["get_custadd"] = @order.billing_address.strip
      form["get_email"] = @order.billing_email
      ccavenue_invoice_number = form["get_invoiceNo"] = @order.number + '_' + SecureRandom.hex(2)
      form["get_desc"] = "Your order number: #{@order.number}"
      form["get_invoiceamt"] = @order.total
      form["get_notes"] = "For queries mail <NAME_EMAIL> or call #{MIRRAW_CONTACT_INFO}"
      form.action = "sendInvoiceMail_ccav.jsp"
      page = form.submit
      #puts page.uri
      form = page.form_with(:name => "invoiceMail")
      form.action = "invoiceMailSuccess_ccav.jsp"
      page = form.submit unless Rails.env.development?
      #puts page.uri
      SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "multiple_payment_invoice", {"#{@order.class}": @order.id}, ccavenue_invoice_number)
      #OrderMailer.sidekiq_delay
      #           .multiple_payment_invoice(@order,ccavenue_invoice_number)
      render :nothing => true
    else
      # Sending CCAvenue payment link via API
      if @order.ccavenue_payment_link_status.blank? || @order.ccavenue_payment_link_status == 'failed'
        @order.ccavenue_payment_link_status = 'attempted'
        @order.save
        @order.send_cbp_link
        message = {:message => 'payment link generation in progress', :previous_failure_message => @order.ccavenue_api_error.present? ? @order.ccavenue_api_error : ''}
      elsif @order.ccavenue_payment_link_status == 'passed'
        message = {:message => 'payment link already sent', :ccavenue_details => {:invoice_id => @order.ccavenue_invoice_id, :payment_link => @order.ccavenue_payment_link}}
      elsif @order.ccavenue_payment_link_status == 'attempted'
        message = {:message => 'payment link generation in progress'}
      end
      render :json => message
    end
  end
  
  def send_paypal_invoice
    @order = Order.all_rel_min.where(:number => params[:number]).first
    @order.move_to_pending! if @order.state == "cancel"
    
    invoice_data = Payment::Paypal::PaypalInvoiceData.new(@order, @order.country_code, @order.currency_code)
    payload = invoice_data.get_invoice_draft_hash
    api =  Payment::Paypal::PaypalInvoicesApi.new(@order)
    if @order.other_details["paypal_invoice_url"].present?
      res = api.send_reminder(@order.other_details["paypal_invoice_url"])
      if !res.present? || !res['has_error']
        @order.add_notes_without_callback("Invoice Reminder Sent To The Customer", "Paypal Invoice")
      end
    else 
      res = api.create_draft_invoice(payload)
      if !res.key?("has_error")
        @order.other_details["paypal_invoice_url"] = res["href"]
        @order.update_attributes({other_details: @order.other_details})
        res = api.send_draft_invoice(@order.other_details["paypal_invoice_url"])
        @order.add_notes_without_callback("Invoice And Reminder Sent To The Customer", "Paypal Invoice") if !res['has_error']
      end
    end
    render json: res
  end

  def is_valid_user
    unless ADMIN_PANEL_ACCESS["shipment_url_access"].to_a.include?(current_account.email)
      redirect_to root_path, notice: 'You are not authorized to access this page.'
    end
  end
  
  def combined_sticker
    order = Order.where(id: params[:id]).first
    if params[:shipment_id].present?
      shipment = Shipment.where(id: params[:shipment_id]).first
      if shipment.present?
        begin
          pdf = CombinePDF.new
          pdf << CombinePDF.parse(HTTParty.get(shipment.invoice.url).body)
          pdf << FEDEX_DECLARATION if shipment.shipper.name.try(:downcase) == 'fedex'
          # pdf << FEDEX_FORM if shipment.shipper.name.try(:downcase) == 'fedex'
          send_data(pdf.to_pdf, filename: "#{shipment.number}.pdf", disposition: 'inline', type: "application/pdf")
        rescue
          send_data(shipment.invoice,filename: "#{shipment.number}.pdf",disposition: 'inline', type: "application/pdf")
        end
      else
        redirect_to request.referer.present? ? :back : root_url, notice: 'Shipment not found.'
      end
    else
      shipment = order.shipments.where(designer_order_id: nil).last
      if shipment.present?
        cs_url = order.other_details["combined_sticker_url_#{shipment.id}"]
        if (cs_url.blank?) || (cs_url.present? && ((pdf = CombinePDF.parse(HTTParty.get(cs_url).body)).present? && pdf.pages.count.to_i.zero?))
          shipper_name  = shipment.shipper.name.upcase
          case shipper_name
          when 'SKYNET'
            label_count,invoice_count = 1,4
          when 'DHL'
            label_count,invoice_count = 0,2
          when "FEDEX"
            label_count,invoice_count = 3,4
          when "ARAMEX"
            label_count,invoice_count = 3,3
          when "DHL ECOM"
            label_count,invoice_count = 1,3
          when "ATLANTIC"
            label_count,invoice_count = 1,3
          else
            label_count,invoice_count = 1,1
          end
          begin
            shipment_label  = CombinePDF.parse(HTTParty.get(shipment.label.url).body) if label_count > 0
            shipment_label = shipment_label.pages.first if shipment_label.present? && shipper_name == "FEDEX"
            shipment_invoice = CombinePDF.parse(HTTParty.get(shipment.invoice.url).body) if invoice_count > 0
          rescue
          end
          # pdf_content = ActionController::Base.new.render_to_string(
          #   template:  'orders/label.html',
          #   layout:  false,
          #   locals: {:@order => order}
          # )
          #address_label = CombinePDF.parse(WickedPdf.new.pdf_from_string(pdf_content))
          pdf = CombinePDF.new
          #pdf << address_label if ['DHL ECOM'].exclude?(shipper_name)
          label_count.times{pdf << shipment_label||=nil}
          invoice_count.times do |i|
            if ['FEDEX','DHL','SKYNET'].include?(shipper_name) && shipment_invoice.present? && !shipment.csb_used?
              invoice_only = CombinePDF.new
              shipment_invoice.pages.each_with_index{|page,index| invoice_only << page unless index == shipment_invoice.pages.count-1}
              pdf << (i == 0 || i== 1 ? shipment_invoice : invoice_only)
            else
              pdf << shipment_invoice||=nil
            end
          end
          file_name = "combined_sticker_#{order.id}_#{shipment.id}"
          AwsOperations.create_aws_file(file_name, pdf.to_pdf, false)
          order.other_details["combined_sticker_url_#{shipment.id}"] = AwsOperations.get_aws_file_path(file_name)
          order.skip_before_filter = true
          order.save!(validate: false)
          # 2.times{pdf << FEDEX_DECLARATION} if ['FEDEX'].include? shipper_name
          # 2.times{pdf << FEDEX_FORM} if shipper_name == 'FEDEX'
          send_data(pdf.to_pdf, :filename => "combined.pdf", :disposition => 'inline', :type => "application/pdf")
        else
          # pdf = CombinePDF.parse(HTTParty.get(order.other_details["combined_sticker_url_#{shipment.id}"]).body)
          send_data(pdf.to_pdf, :filename => "combined.pdf", :disposition => 'inline', :type => "application/pdf")
        end
      else
        redirect_to request.referer.present? ? :back : root_url, notice: 'Shipment not found.'
      end
    end
  end

  def order_check_page

    LineItem.update_scanned_input_param(params) # needs to be checked
    if params[:total_items].present?
      total_products = params[:total_items]
      order_hash = {}
      design_rack_quantity = {}
      (1..total_products.to_i).each do |num|
        input_id = "input_#{num}"
        barcode = params[input_id].presence
        barcode_split = barcode.to_s.strip.split('-')
        if [5, 7].include?(barcode_split.length)
          design_id = barcode_split[4]
          variant_id = barcode_split[6]
          order_hash[barcode_split[1]] = true
          key = barcode_split.length == 7 ? "#{design_id}-#{variant_id}" : design_id
          if design_rack_quantity[key]
            design_rack_quantity[key][:quantity] +=1
            design_rack_quantity[key][:rack_code] << barcode_split[0]            
          else            
            design_rack_quantity[key] = {quantity: 1, rack_code: [barcode_split[0]]}
          end
        end
      end
      status,data, cargo_bucket = Order.compute_order_complete(order_hash,design_rack_quantity)
      case status
      when :perfect
        @order = data
        if @order.international?
          measured_weight = [params[:weight].to_i, params[:total_weight].to_i].max
          @order.mark_check_items(current_account, (measured_weight + params[:extra_packaging_weight].to_i)/1000.0, params[:volumetric_weight].to_f, params[:extra_packaging_weight].to_i, measured_weight, design_rack_quantity, cargo_bucket)
          if @order.eligible_for_shipconsole?
            ShipconsoleAllocationService.new(@order, design_rack_quantity).call
          end
          @shipper_weight = @order.best_shipper.to_s.downcase == 'fedex' ? 60 :30
          # @order.sidekiq_delay(queue: 'low')
          #       .update_cost_estimation if @order.international?
          SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(@order.class.to_s, @order.id, "update_cost_estimation") if @order.international?        
          if @order.shipment_buckets.exists?
            @cargo_bucket = cargo_bucket
            # @order.sidekiq_delay(queue: 'critical')
            #       .create_international_shipment(
            #         current_account.id
            #       ) if SCAN_AUTOMATE_SHIPMENT_SHIPPERS.include?('cargo')
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(@order.class.to_s, @order.id, "create_international_shipment", current_account.id) if SCAN_AUTOMATE_SHIPMENT_SHIPPERS.include?('cargo')
          elsif SCAN_AUTOMATE_SHIPMENT_SHIPPERS.include?(@order.best_shipper)
            if (@order.best_shipper.to_s.downcase == 'dhl' || @order.best_shipper.to_s.downcase == 'ups')
              # @order.sidekiq_delay(queue: 'critical')
              #   .create_international_shipment(
              #     params[:length].to_i,
              #     params[:breadth].to_i,
              #     params[:height].to_i,
              #     current_account.id
              #   )
              SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(
                                                              @order.class.to_s, 
                                                              @order.id, 
                                                              "create_international_shipment", 
                                                              params[:length].to_i,
                                                              params[:breadth].to_i,
                                                              params[:height].to_i,
                                                              current_account.id
                                                            )
            else 
              # @order.sidekiq_delay(queue: 'critical')
              #   .create_international_shipment(current_account.id)
              SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(@order.class.to_s, @order.id, "create_international_shipment", current_account.id)
            end
          end
        else
          @error_message = 'Domestic Order Scanned'
          @order = nil
        end
      when :error
        @error_message = data
      end
    end
    respond_to do |format|
      format.html
      format.js
    end
  end

  def add_notes
    @order = Order.find_by_id(params[:order_id])
    if @order.present? && params[:notes_id].present?
      event = @order.add_notes_without_callback(params[:notes_id], (params[:note_type].presence || current_account.try(:role).try(:name)), current_account)
      render :json => {:notes => event, time: event.event_timestamp.strftime('%d, %b %y %I:%M %p')}
    else
      render :json => {:error => 'Order not found || notes blank'}
    end
  end
  
  def add_tags
    @order = Order.all_rel_min.find_by_id(params[:order_id])
    if @order.present?
      note_content = ''
      tags = params[:tags] || params[:add_tags]
      if tags[0] == '-'
        note_content = "Removed tag #{tags[1..-1]}"
        @order.tag_list.remove(tags[1..-1])
      else
        note_content = params[:reason].present? ? "Added tag #{tags} comment - #{params[:reason]}" : "Added tag #{tags}"
        tag_name = tags.downcase
        @order.tag_list.add(tag_name)
        if params[:tags].to_s.include?('partial dispatched')
          @order.partial_package_shipped!
        end
        # @order.add_tags_with_additional_fields(tag_wise_additional_details: {tag_name.to_s => {'reason' => params[:reason].to_s}}) if params[:reason].present?
      end
      event = @order.add_notes_without_callback(note_content, 'other', current_account)
      if @order.save!        
        if tag_name == 'dndcs_cancel'
          @order.designer_orders.each do |designer_order|
            SidekiqDelayGenericJob.perform_async("OrderMailer",nil,"send_dndcs_cancel_mail",{"#{designer_order.class}" => designer_order.id},{"#{@order.class}" => @order.id})
          end
        end
        if params[:add_tags].present?
          redirect_to :back
        else
          tags = @order.tag_list.reject{|tag| tag.include?('convert-mkt')}.join(',')
          render :json => {:tags => tags, :notes => event, time: event.event_timestamp.strftime('%d, %b %y')}
        end
      else
        render :json => {:error => 'Error adding tags'}
      end
    else
      render :json => {:error => 'Order not found'}
    end
  end

  def apply_coupon_on_order
    coupon_code = params[:coupon_code]
    order = Order.where(id: params[:order_id]).first
    if (coupon_code && coupon = Coupon.where('lower(code) = ?',coupon_code.downcase).first).present? && coupon.coupon_type == 'COFF' && coupon.live? && coupon.converted_to_refund != true && order.present?
      order.discount +=  coupon.flat_off.to_i
      order.coupon = coupon
      order.add_notes_without_callback("Used Coupon : #{coupon.code}",'payment',current_account) if order.save
      coupon.coupon_used_on = order
      coupon.use_count += 1
      coupon.save!
      redirect_to order_order_detail_path(order) , notice: "Coupon Applied Successfully !!!"
    else
      redirect_to order_order_detail_path(order) , notice: "Coupon Is Not Valid !!!"
    end
  end

  #order/amazon_success?amznPmtsOrderIds=407-7077823-7206769&amznPmtsReqId=Mzg1NjQy&amznPageSource=CartPage&merchName=Mirrawdesigns&amznPmtsYALink=https%3A%2F%2Fpaywithamazon-sandbox.amazon.in%2Foverview&amznPmtsPaymentStatus=REJECTED
  def amazon_return
    amazon_order_id = params["amznPmtsOrderIds"]
    status = params[:amznPmtsPaymentStatus]
    @message = "Your order has been received, and your payment has been #{status}. Thank you for your purchase! Your Amazon Payments ID is: #{amazon_order_id}. Note: In-case of payment failure, you will receive an email from 'Pay with Amazon' asking you to revise the payment."
    order= Order.where(amazon_order_id: amazon_order_id,pay_type: PAY_WITH_AMAZON).first  if amazon_order_id.present?
    if order.present?
      if cart = order.cart
        session[:cart_id] = nil if cart.id == session[:cart_id]
        cart.update_column(:used,true) unless cart.used
      end
      redirect_to order,notice: @message
    end
  end

  def amazon_response
    if params['Signature'] == PayWithAmazon.generate_signature(params['UUID'].to_s + params['Timestamp'].to_s) && (Time.now - Time.parse(params['Timestamp'])) < 10.minutes
      response_hash = Hash.from_xml(params['NotificationData'])[params['NotificationType']]['ProcessedOrder']
      amazon_order_id = response_hash['AmazonOrderID']
      order = Order.where(pay_type: PAY_WITH_AMAZON, amazon_order_id: amazon_order_id).first
      case params['NotificationType']
      when 'NewOrderNotification'
        if order.blank?
          Design.country_code = 'IN'
          line_items = response_hash['ProcessedOrderItems']['ProcessedOrderItem']
          line_items = [line_items] unless line_items.is_a?(Array)
          cart_data = line_items.first.try(:[], 'CartCustomData') || {}
          @order = Order.new(currency_code: 'Rs', pay_type: PAY_WITH_AMAZON, amazon_order_id: amazon_order_id, country: 'India', track: 0, currency_rate: 1, currency_rate_market_value: 1, actual_country_code: 'IN', app_source: 'Desktop', cart_id: cart_data['cart_id'].to_i)
          @order.set_buyer_details_and_address(response_hash['BuyerInfo'],response_hash['ShippingAddress'], response_hash['BillingAddress'] || response_hash['ShippingAddress'])
          @order.generate_order_number
          copy_from_session(HashWithIndifferentAccess.new(cart_data))
          cart = Cart.find_by_id(cart_data['cart_id'].to_i)
          cart.update_column(:used, true) unless cart.used
          cart.amazon_rectify_cart(line_items)
          cart = Cart.all_rel.find_by_id(cart_data['cart_id'].to_i) #reloading cart
          @order.add_line_items_from_cart(cart)
          @order.save!
          SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_order_details_to_buyer", {"#{@order.class}": @order.id})
          # OrderMailer.sidekiq_delay.mail_order_details_to_buyer(@order)
        end
      when 'OrderReadyToShipNotification'
        if order.state != 'sane'
          order.set_buyer_details_and_address(response_hash['BuyerInfo'],response_hash['ShippingAddress'], response_hash['BillingAddress'] || response_hash['ShippingAddress'])
          SidekiqDelayGenericJob.perform_async("PayWithAmazon", nil, "order_acknowledgment", {"#{order.class}": order.id})
          # PayWithAmazon.sidekiq_delay.order_acknowledgment(order)
          if order.amount_sent_payment_gateway.to_i == order.total.to_i
            order.add_notes("SUCCESS",true)
            # order.sidekiq_delay(queue: 'critical').good_data!
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "good_data!")
          else
            order.add_notes_without_callback('Received amount mismatch','payment')
            # order.sidekiq_delay(queue: 'critical').confirmed_order!
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "confirmed_order!")
          end
        end
      when 'OrderCancelledNotification'
        if order.state != 'cancel'
          order.order_cancel_reason = 'Canceled by Amazon payment'
          # order.sidekiq_delay(queue: 'critical').cancel!
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "cancel!")
        end
      else
        render(nothing: true, status: 500) && return
      end
      render nothing: true, status: 200
    else
      render nothing: true, status: 403
    end
  rescue => error
    ExceptionNotify.sidekiq_delay.notify_exceptions('Amazon Response error', 'Amazon Response error', { error: error.inspect , params: params})
    render nothing: true, status: 503
  end

  def g2a_response
    OrdersController.sidekiq_delay_until(10.minutes.from_now)
                    .g2a_ipn_authentication(params)
    render nothing: true
  end

  def self.g2a_ipn_authentication(params1)
    order = Order.preload(cart: :line_items).find_by_number(params1['userOrderId'])
    Design.country_code = order.actual_country_code
    if order.present? && params1['hash'] == order.g2a_ipn_hash
      case params1['status']
      when 'complete'
        if order.state != 'sane'
          payment_details = params1.dup
          %w(type refundedAmount provisionAmount hash email isCash sendPush processingTime controller action).each do |k|
            payment_details.delete(k)
          end
          payemnt_gateway_details = order.payment_gateway_details.to_s
          payemnt_gateway_details += "\n"+payment_details.to_json
          if order.items_and_shipping_total == params1['amount'].to_f
            order.add_notes('SUCCESS',true)
            order.update_attributes(payment_gateway: "#{params1['paymentMethod']} #{params1['paymentMethodGroup']}",g2a_txn_status: params1['status'],payment_gateway_details: payemnt_gateway_details)
            # order.sidekiq_delay(queue: 'critical').good_data!
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "good_data!")
          else
            order.update_attributes(payment_gateway: "#{params1['paymentMethod']} #{params1['paymentMethodGroup']}",g2a_txn_status: params1['status'],payment_gateway_details: payemnt_gateway_details)
            order.add_notes_without_callback('Received amount mismatch','payment')
            # order.sidekiq_delay(queue: 'critical').confirmed_order!
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "confirmed_order!")
          end
        end
      when 'cancel'
        if order.state != 'cancel'
          order.update_attributes(payment_gateway: "#{params1['paymentMethod']} #{params1['paymentMethodGroup']}",g2a_txn_status: params1['status'])
          order.order_cancel_reason = 'Canceled by G2A payment'
          # order.sidekiq_delay(queue: 'critical').cancel!
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "cancel!")
        end
      when 'rejected'
        if order.state != 'cancel'
          order.update_attributes(payment_gateway: "#{params1['paymentMethod']} #{params1['paymentMethodGroup']}",g2a_txn_status: params1['status'])
          order.order_cancel_reason = 'Rejected by G2A payment'
          # order.sidekiq_delay(queue: 'critical').cancel!
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "cancel!")
        end
      when 'pending'
        if order.state!= 'sane' || order.state != 'pending'
          order.update_attribute(:g2a_txn_status, params1['status'])
          order.add_notes_without_callback('Waiting for payment from G2A','payment')
          # order.sidekiq_delay(queue: 'critical').paypal_pending!
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "paypal_pending!")
        end
      else
        order.order_cancel_reason = 'Failed to verify payment by G2A'
        order.cancel!
      end
    end
  end

  def stripe_response
    redirect_to new_order_path if ((key = session[:intent]).blank? || (order = Order.find_by_number(session[:order_number])).blank?)
    session[:intent] = nil
    session[:stripe] = nil
    gon.session = false
    response = order.retrieve_intent(key)
    order.payment_gateway = 'stripe'
    order.payment_gateway_details = response
    order.paypal_error = response.last_payment_error
    order.paypal_payer_id = response.id
    order.save
    if order.currency_code.downcase == 'jpy'
      amount = response.amount_received * order.currency_rate
    else
      amount = (response.amount_received / 100.0) * order.currency_rate
    end
    Order.sidekiq_delay(queue: 'critical')
         .process_stripe_order!(order.id, amount, response.status)
    respond_to do |format|
      format.html {
        if response.status == 'succeeded'
          redirect_to(order_url(order.number), :notice => 'Your order was successfully completed.')
        else
          flash[:error] = 'Your order was cancelled!'
          redirect_to new_order_path
        end
      }
    end
  end

  def paypal_res
    OrdersController.sidekiq_delay(queue: 'critical')
                    .paypal_ipn_authentication(request.raw_post, params)
    render :nothing => true  
  end

  def self.paypal_ipn_authentication(raw, params1)
    if Rails.env.production?
      url1 = 'https://www.paypal.com/cgi-bin/webscr?cmd=_notify-validate'
    else
      url1 = 'https://www.sandbox.paypal.com/cgi-bin/webscr?cmd=_notify-validate'
    end
    uri = URI.parse(url1)
    http = Net::HTTP.new(uri.host, uri.port)
    http.open_timeout = 60
    http.read_timeout = 60
    http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    http.use_ssl = true
    begin
      response = http.post(uri.request_uri, raw,
                           'Content-Length' => "#{raw.size}",
                           'User-Agent' => "My custom user agent"
                         ).body
    rescue => e
      ExceptionNotify.sidekiq_delay.notify_exceptions("Desktop paypal response error", "Order: #{params1['invoice']}, error: #{e.to_s}", {params: params1})
    end
    @order = params1['invoice'].present? ? Order.where(:number => params1['invoice']).first : nil
    @order.add_to_payment_gateway_details(params1.to_json) if @order.present?
    case response
    when "VERIFIED"
      # check that paymentStatus=Completed
      # check that txnId has not been previously processed
      # check that receiverEmail is your Primary PayPal email
      # check that paymentAmount/paymentCurrency are correct
      # process payment
      unless params1['txn_id'].present?
        ExceptionNotify.sidekiq_delay.notify_exceptions( "Paypal error", "No txn_id for order: #{@order.try(:id).to_i}", {params: params1})
      end

      case params1['payment_status']
      when "Completed"
        if Rails.env.production?
          receiver_email = "<EMAIL>"
        else
          # receiver_email = "<EMAIL>"
          receiver_email = "<EMAIL>"
        end
        if params1['invoice'].to_s.include?('addon')
          Order.mark_additional_payment_complete_paypal(receiver_email,params1)
        else
          #@order = Order.where(:number => params1['invoice']).first
          return unless @order.present?

          transaction = Order.where(:paypal_txn_id => params1['txn_id']).first

          paypal_allowed_currencies = Order::PAYPAL_ALLOWED_CURRENCIES

          if params1['mc_currency'] && paypal_allowed_currencies.include?(@order.currency_code)
            lower_total = (@order.total/@order.currency_rate).round(2) - 20
          elsif @order.domestic_paypal?(@order.country_code, @order.currency_code)
            lower_total = @order.total.round(2)
          else
            lower_total = CurrencyConvert.to_usd_paypal_rate(@order.total,@order.currency_rate,@order.currency_code,@order.country_code).to_i - 20
          end
          non_duplicate = true  
          non_duplicate = false if (transaction.present? && transaction.id != @order.id)
          if @order.present?
            @order.payment_gateway='paypal'
            @order.paypal_payer_id = params1['payer_id']
            @order.paypal_txn_id = params1['txn_id']
            @order.payu_error = 'PPNOTXNID' unless params1['txn_id'].present?
            @order.paypal_payment_type = params1['payment_type']
            @order.paypal_ipn_track_id = params1['ipn_track_id']
            @order.paypal_mc_gross = params1['mc_gross']
            @order.paypal_mc_currency = params1['mc_currency']
            @order.paypal_num_cart_items = params1['num_cart_items']
            if (params1['receiver_email'] == receiver_email && non_duplicate && (params1['mc_gross'].to_i >= lower_total)) || Rails.env.development?
              # @order.save
              # @order.cancel_unless_confirmed
              if @order.paypal_payment_type != 'echeck'
                @order.add_notes 'SUCCESS', false
                @order.good_data! if @order.can_good_data?
              elsif @order.cancel?
                @order.add_notes_without_callback 'PAYPAL PAYMENT COMPLETE', 'payment'
                @order.move_to_pending!
                @order.good_data! if @order.can_good_data? && @order.paypal_payment_type != 'echeck'
              end
              SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_order_details_to_buyer", {"#{@order.class}": @order.id})
              # OrderMailer.sidekiq_delay.mail_order_details_to_buyer(@order)
            else
              @order.save
            end
          end
        end
      when "Pending"
        unless params1['invoice'].present?
          ExceptionNotify.sidekiq_delay.notify_exceptions("Paypal error", "invoice error", {params: params1})
        end
        if params1['invoice'].to_s.include?('addon')
          Order.mark_additional_payment_complete_paypal(receiver_email,params1,'incomplete')
        else
          #@order = Order.where(:number => params1['invoice']).first
          if @order.present?
            if params1['txn_id'].present?
              @order.payment_gateway='paypal'
              @order.paypal_payer_id = params1['payer_id']
              @order.paypal_txn_id = params1['txn_id']
              @order.paypal_payment_type = params1['payment_type']
              @order.paypal_ipn_track_id = params1['ipn_track_id']
              @order.paypal_mc_gross = params1['mc_gross']
              @order.paypal_mc_currency = params1['mc_currency']
              @order.paypal_num_cart_items = params1['num_cart_items']
              @order.ccavenue_api_error = 'PPPENDING'
              @order.save
            else
              @order.update_column(:payu_error, 'PPNOTXNID')
            end
            @order.paypal_pending! unless @order.pending?
          end
        end
      when "Denied"
        if @order.present?
          # @order.sidekiq_delay
          #       .update_payment_gateway_error_code(
          #         order_payment_gateway: 'paypal', 
          #         error: 'PPDENIED'
          #       )
          SidekiqDelayGenericJob.perform_async(@order.class.to_s, 
                                                @order.id, 
                                                "update_payment_gateway_error_code", 
                                                order_payment_gateway: 'paypal', 
                                                error: 'PPDENIED', 
                                                sidekiq_request_params: true )
          # @order = Order.where(:number => params1['invoice']).first
          @order.cancel_unless_confirmed
        end
      when "Failed"
        if @order.present?
          # @order.sidekiq_delay
          #       .update_payment_gateway_error_code(
          #         order_payment_gateway: 'paypal',
          #         error: 'PPFAILED'
          #       )
          SidekiqDelayGenericJob.perform_async(@order.class.to_s, 
                                                @order.id, 
                                                "update_payment_gateway_error_code", 
                                                order_payment_gateway: 'paypal', 
                                                error: 'PPFAILED', 
                                                sidekiq_request_params: true )
          # @order = Order.where(:number => params1['invoice']).first
          @order.cancel_unless_confirmed
        end
      end
    when "INVALID"
      # @order.sidekiq_delay
      #       .update_payment_gateway_error_code(
      #         order_payment_gateway: 'paypal',
      #         error: 'PPINVALID'
      #       ) if @order.present?
      SidekiqDelayGenericJob.perform_async(@order.class.to_s, 
                                            @order.id, 
                                            "update_payment_gateway_error_code", 
                                            order_payment_gateway: 'paypal', 
                                            error: 'PPINVALID', 
                                            sidekiq_request_params: true 
                                          ) if @order.present?
    else
      # @order.sidekiq_delay
      #       .update_payment_gateway_error_code(
      #         order_payment_gateway: 'paypal',
      #         error: "PPUNKNOWN-#{params1['payment_status']}"
      #       ) if @order.present?
      SidekiqDelayGenericJob.perform_async(@order.class.to_s, 
                                            @order.id, 
                                            "update_payment_gateway_error_code", 
                                            order_payment_gateway: 'paypal', 
                                            error: "PPUNKNOWN-#{params1['payment_status']}", 
                                            sidekiq_request_params: true 
                                          ) if @order.present?
    end
  end

  def cod_verify
    if params.present? && params['SID'].present?
      order = Order.find_by_zipdial_transaction_token(params['SID'])
      if order.present?
        unless order.sane?
          unless order.check_similar_confirmed_order_week?
            order.good_data! if order.can_good_data?
          else
            order.tag_list.add('duplicate')
            order.add_notes_without_callback("Added tag duplicate", 'other', current_account)
          end
          order.save!
        end
      end
    end
    render :nothing => true
  end

  def update_phone_number
    order = Order.find_by_id(params[:order_id])
    if params[:type] == 'phone'
      order.update(phone: params[:value])
    elsif params[:type] == 'email'
      order.update(email: params[:value])
    end
    if order.save
      order.add_notes_without_callback("#{params[:type]} changed",'dispatch',current_account)
      response = {msg: "#{params[:type]} Successfully Updated"}
    end
    render json: response
  end

  def res
    @order = Order.where(:number => params['Order_Id']).first
    return unless @order.present?

    @merchant_key = '39sxhhs7xv2n2y2us2'
    @merchant_id = params['Merchant_Id']
        
  	@amount = params['Amount'];
  	@order_id = params['Order_Id'];
  	@merchant_param = params['Merchant_Param'];
  	@checksum = params['Checksum'];
  	@authdesc = params['AuthDesc'];

    @order.ccavenue_authdesc = @authdesc
    @order.ccavenue_nb_order_no = params['nb_order_no']
    @order.ccavenue_nb_bid = params['nb_bid']
    @order.ccavenue_card_category = params['card_category']
    @order.ccavenue_bank_name = params['bank_name']
    
    @computed_checksum = get_checksum(@merchant_id, @order_id, @amount, @authdesc, @merchant_key)
    valid = @checksum == @computed_checksum.to_s

    if valid && @authdesc == "Y"
      @order.post_order_confirmation_work
      @order.notes ="SUCCESS"
      redirect_to(order_url(@order.number), :notice => 'Your order was successfully completed.')
    elsif valid && @authdesc == "B"
      redirect_to(order_url(@order.number), :notice => 'Thank you for shopping with us. We will keep you posted regarding the status of your order through e-mail"')
      @order.add_notes_without_callback("Payment not yet confirmed", 'payment')
    elsif valid && @authdesc == "N"
      respond_to do |format|
        format.html {render "declined", :layout => false}
      end
      @order.add_notes_without_callback("Transaction declined", 'payment')
      @order.cancel_order_increase_quantity
    else
      redirect_to root_url, :notice => "Unknown error happened."
      @order.add_notes_without_callback("Unknown error happened.", 'payment')
     end
     @order.save!
  end

  def ccavenue_mpcg_response
    encrypt_response = params[:encResp]
    decrypt_resp = Order.ccavenue_mpcg_decrypt(encrypt_response, Mirraw::Application.config.ccavenue_mpcg_enc_key)
    response = Rack::Utils.parse_nested_query(decrypt_resp)
    order = Order.find_by_number(response['order_id'])
    if order.present?
      order.payment_gateway = 'ccavenue'
      order.ccavenue_card_category = response['card_name']
      order.ccavenue_merchant_data = response['merchant_param1'] + '. ' + response['merchant_param2'] + '. ' + response['merchant_param3'] + '. ' + response['merchant_param4'] + '. ' + response['merchant_param5']
      order.ccavenue_nb_bid = response['bank_ref_no']
      order.ccavenue_nb_order_no =  response['tracking_id']
      order.ccavenue_order_status = response['order_status'].downcase
      order.ccavenue_failure_message = response['failure_message']
      order.ccavenue_payment_mode = response['payment_mode']
      order.ccavenue_bank_status_code = response['status_code']
      order.ccavenue_bank_status_message = response['status_message']
      order.ccavenue_customer_identifier = response['customer_identifier']
      order.ccavenue_currency = response['currency']
      notice = ''
      session[:order_status] = order.ccavenue_order_status
      session[:order_number] = order.number
      update_order_payment_status(order.ccavenue_order_status, response['amount'], order)
      return
    end
  end
  
  #post request of razorpay 
  def razorpay_submission
    @order = Order.find_by_number(params[:order_number])
    Order.sidekiq_delay.process_razorpayment(@order.id, params[:payment_id])
    redirect_to(order_url(@order.number), notice: 'Thank you for your order.')
  end

  def payu_response
    notice = nil
    payu_salt = Mirraw::Application.config.payu[:salt]
    if params[:key].present? && params[:txnid].present? && params[:amount].present? && params[:productinfo].present? && params[:firstname].present? && params[:email].present? && params[:mihpayid].present?
      computed_hashsum = Order.payu_billing_page_response_checksum(params[:key], params[:txnid], params[:amount], params[:productinfo], params[:firstname], params[:email], params[:udf1], params[:udf2], params[:udf3], params[:udf4], params[:udf5], payu_salt, params[:status])
      if computed_hashsum == params[:hash]
        if params[:txnid].present? && params[:status]
          if params[:txnid].exclude?('addon')
            if (order = Order.find_by_number(params[:txnid])) && (order.payu_mihpayid != params[:mihpayid])
              order.payment_gateway = 'payu'
              order.payu_mihpayid = params[:mihpayid]
              order.payu_status = params[:status].downcase
              order.payu_unmapped_status = params[:unmappedstatus]
              order.payu_payment_category_mode = params[:PG_TYPE]
              order.payu_bank_ref = params[:bank_ref_num]
              order.payu_bankcode = params[:bankcode]
              order.payu_error = params[:error]
              order.payu_error_message = params[:error_Message]
              order.payu_name_on_card = params[:name_on_card]
              order.payu_card_num = params[:cardnum]
              order.payu_payment_issuing_bank = params[:issuing_bank]
              order.payu_card_type = params[:card_type]
              order.payu_money_id = params[:payuMoneyId]
              update_order_payment_status(order.payu_status, params[:amount], order)
              return
            else
              notice ='Your order is placed successfully, please check your mail for confirmation.'
            end
          else
            Order.mark_additional_payment_complete_payu(params)
          end
        end
      end
      redirect_to(root_path,notice: notice)
      return
    end
  end

  # Handles the response generated by paytm
  def paytm_response
    paytmparams = request.request_parameters
    if Order.valid_paytm_response?(paytmparams) && (order = Order.find_by_number(paytmparams['ORDERID']))&&
    (order.paytm_txn_id.nil? || order.paytm_txn_id != paytmparams['TXNID'])
      status = order.assign_paytm_attributes(paytmparams)
      update_order_payment_status(status, paytmparams['TXNAMOUNT'], order, true)
      Order.sidekiq_delay(queue: 'critical')
           .process_paytm_order!(order.id, paytmparams['TXNAMOUNT'])
    else
      ExceptionNotify.sidekiq_delay.notify_exceptions('paytm_invalid_response','Paytm invalid response received!', {params: params})
      redirect_to new_order_path, notice: 'Paytm is unavailable, please use other payment options.'
    end
  end

  def self.gharpay_create_order(order)
    # Build xml object by hand since we want to add product details as well. 
    # Hash doesn't allow duplicate keys. productDetails is something that is
    # repeated.
    if false
      @order = order

      # Build xml object by hand since we want to add product details as well. 
      # Hash doesn't allow duplicate keys. productDetails is something that is
      # repeated.

      xm = Builder::XmlMarkup.new
      xm.transaction {
         xm.customerDetails {
           xm.address(@order.billing_street +  ' , ' + @order.billing_city + ' - ' + @order.billing_pincode + ' , ' + @order.billing_state + ' , ' + @order.billing_country)
           xm.contactNo(@order.billing_phone)
           xm.email(@order.billing_email)
           xm.firstName(@order.billing_name)
         }
         xm.orderDetails {
           xm.pincode(@order.billing_pincode)
           xm.clientOrderID(@order.number)
           xm.deliveryDate(Time.zone.now.strftime('%d-%m-%Y'))
           xm.orderAmount(@order.total)

           order.line_items.each do |item|
              xm.productDetails {
                xm.productID(item.design_id.to_s)
                xm.productQuantity(item.quantity)
                xm.unitCost(item.price)
                xm.productDescription(item.design.designer.name + ' - ' + item.design.title)
              }
           end
         }
       }

       @creds = {'username' => Mirraw::Application.config.gharpay_username,
                 'password' => Mirraw::Application.config.gharpay_password}
       options = {:body => xm.target!, :headers => @creds.merge('Content-Type' => 'application/xml') }
       res = HTTParty.post("#{Mirraw::Application.config.gharpay_baseurl}/rest/GharpayService/createOrder", options)

       unless res['createOrderResponse']['errorMessage']
         @order.gharpay_order_id = res['createOrderResponse']['orderID']
         @order.gharpay_status = ''
         @order.save
         @order.gharpay! if @order.state == "new" 
       else
         @order.gharpay_status = res['createOrderResponse']['errorMessage']
         @order.save
       end
    else
      @order = order
      xm = Builder::XmlMarkup.new

      xm.transaction {
        xm.customerDetails {
          xm.address(@order.billing_address)
          xm.contactNo(@order.billing_phone)
          xm.email(@order.billing_email)
          xm.firstName(@order.billing_name)
        }
         xm.orderDetails {
           xm.pincode(@order.billing_pincode)
           xm.clientOrderID(@order.number)
           xm.deliveryDate(Time.zone.now.strftime('%d-%m-%Y'))
           xm.orderAmount(@order.total)
           xm.paymentMode('cash')
         }
       }

       options = {:body => {:data => xm.target!}, :headers => {'Content-Type' => 'application/xml', 'Accept' => 'application/xml'}}
       res = HTTParty.post("#{Mirraw::Application.config.delhivery_baseurl}/api/p/createOrder/xml/?token=#{Mirraw::Application.config.delhivery_token}", options)

       if res
         unless res['createOrderResponse']['errorMessage']
           @order.gharpay_order_id = res['createOrderResponse']['orderID']
           @order.gharpay_status = ''
           @order.save
           @order.gharpay! if @order.state == "new" 
         else
           @order.gharpay_status = res['createOrderResponse']['errorMessage']
           @order.save
         end
       end
    end
  end

  # This method create shipment for aramex international shipping
  def aramex_international_invoice
    @order = Order.includes(:order_addon,:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order], design: [:categories,property_values: :property]]).find_by('number = ?',params[:number].upcase) if params[:number].present?
    redirect_to root_url, notice: 'Order not found' and return unless @order.present?
    redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' and return if @order.present? && (@order[:other_details].try(:[], 'blacklist_user') || @order.blacklist_user_order)    
    if params[:item].present? && params[:total_weight].present? && is_international_order?(@order)      
      order_shipment_create = OrderShipmentCreate.new(@order, 'Aramex', params)
      automation_status = order_shipment_create.create_order_automation_shipment
      if automation_status[:error]
        @order.add_tags_skip_callback('shipment_error')
      else
        @shipment = true
      end    
      redirect_to (request.env["HTTP_REFERER"] || aramex_international_invoice_path), notice: automation_status[:message] and return
    elsif !is_international_order?(@order)
      flash[:notice] = 'It is Domestic order. Shipment cannot be generated'
    elsif params[:item].blank?
      @not_edit = @order.commercial_allow_editing
    end
  end

  # Check if address is not valid international order
  def invalid_address(msg)
    msg.include? "ResolveAddress Consignee Address for shipment"
  end

  # This method check if order is international
  def is_international_order?(order)
    order.country.downcase != "india"
  end

  def fedex_invoice
    if params[:item].blank? && params[:number].present?
      @order = Order.includes(:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order],  design: [:designer,:categories,:images,property_values: :property]]).where('number = ?',params[:number].upcase).first
      redirect_to root_url, notice: 'Order not found' and return unless @order.present?
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order.present? && ( @order[:other_details].try(:[], 'blacklist_user') || @order.blacklist_user_order )
      @not_edit = @order.commercial_allow_editing
    else
      upload_invoice = params[:commit] == 'Create Shipment and Upload Invoice'
      begin
        gen_shipment = ShipmentDelivery::Fedex::FedexOrderLabel.new(params[:number],params[:item],params[:total_weight],params[:packaging_type],params[:packer_id],current_account.id,upload_invoice)
        gen_shipment.generate_fedex_label
        redirect_to (request.env["HTTP_REFERER"] || fedex_invoice_path), notice: 'Label Generation Successful.' and return
      rescue => error 
        redirect_to (request.env["HTTP_REFERER"] || fedex_invoice_path), notice: error.message 
      end  
   
    end
  end

  def clickpost_shippers
    @order = Order.find_by_number params[:number]
    @all_items = LineItem.joins(:designer_order,designer_order: :order).where(orders: {number: params[:number]}, designer_orders: {ship_to: 'mirraw'}, line_items: {received: 'Y', shipment_id: nil})
    selected_items = LineItem.joins(:designer_order,designer_order: :order).where('orders.number=?', params[:number]).where('designer_orders.ship_to=?','mirraw').where('line_items.shipment_id is NULL').where('line_items.received=?','Y').where("line_items.stitching_required IS NULL OR (line_items.stitching_required = 'Y' AND  line_items.stitching_done = 'Y')")
    pickup_pincode  = @order.get_warehouse_shipping_address[5]
    drop_pincode = @order.pincode
    invoice_value = selected_items.sum(:snapshot_price)
    click_post = ClickPostAutomation.new(@order)
    clickpost_shipper_id = click_post.fetch_recommended_couriers(pickup_pincode,drop_pincode,'FORWARD','PREPAID',invoice_value,@order.id)
    clickpost_shipper_id_array = clickpost_shipper_id[@order.id]
    @shippers = Shipper.where(clickpost_shipper_id: clickpost_shipper_id_array).pluck("upper(name)", :id).to_h
    if params[:shipper_id].present?  
      assigned_shipper = Shipper.find_by_id(params[:shipper_id].to_i)
      SidekiqDelayClassSpecificGenericJob.set({queue: 'critical'}).perform_async("ClickPostAutomation","forward_pickup_from_clickpost",{"#{@order.class}": @order.id,skip_object_creation: true}, {"#{assigned_shipper.class}": params[:shipper_id].to_i})
      render json: { notice: 'Shipment is in progress' }
    end
  end
  
  def xpressbees_ups_invoice
    if params[:item].blank? && params[:number].present?
      @order = Order.includes(:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order],  design: [:designer,:categories,:images,property_values: :property]]).where('number = ?',params[:number].upcase).first
      redirect_to root_url, notice: 'Order not found' and return unless @order.present?
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order.present? && ( @order[:other_details].try(:[], 'blacklist_user') || @order.blacklist_user_order )
      @not_edit = @order.commercial_allow_editing
    else
      @order = Order.preload(:order_addon,line_items: :line_item_addons).where(:number => params[:number]).first
      begin
        order_shipment_create = OrderShipmentCreate.new(@order, 'xpressbees_ups', params)
        order_shipment_create.create_order_automation_shipment
        redirect_to (request.env["HTTP_REFERER"] || xpressbees_ups_invoice_path), notice: 'Label Generation In Progress' and return
      rescue => error
        redirect_to (request.env["HTTP_REFERER"] || xpressbees_ups_invoice_path), notice: error.message
      end
    end
  end

  def xindus_invoice
    if params[:item].blank? && params[:number].present?
      @order = Order.includes(:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order],  design: [:designer,:categories,:images,property_values: :property]]).where('number = ?',params[:number].upcase).first
      redirect_to root_url, notice: 'Order not found' and return unless @order.present?
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order.present? && ( @order[:other_details].try(:[], 'blacklist_user') || @order.blacklist_user_order )
      @not_edit = @order.commercial_allow_editing
    else
      @order = Order.preload(:order_addon,line_items: :line_item_addons).where(:number => params[:number]).first    
      begin
        order_shipment_create = OrderShipmentCreate.new(@order, 'Xindus', params)
        order_shipment_create.create_order_automation_shipment 
        redirect_to (request.env["HTTP_REFERER"] || xindus_invoice_path), notice: 'Label Generation In Progress' and return
      rescue => error 
        redirect_to (request.env["HTTP_REFERER"] || xindus_invoice_path), notice: error.message 
      end
    end
  end 

  def ups_invoice
    if params[:item].blank? && params[:number].present?
      @order = Order.includes(:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order],  design: [:designer,:categories,:images,property_values: :property]]).where('number = ?',params[:number].upcase).first
      redirect_to root_url, notice: 'Order not found' and return unless @order.present?
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order.present? && ( @order[:other_details].try(:[], 'blacklist_user') || @order.blacklist_user_order )
      @not_edit = @order.commercial_allow_editing
    else
      begin
        gen_shipment = ShipmentDelivery::UPS::UPSOrderLabel.new(params[:number],params[:item],params[:total_weight],params[:packaging_type],params[:packer_id],current_account.id,params[:shipment_depth],params[:shipment_width],params[:shipment_height])
        gen_shipment.generate_ups_label
        redirect_to (request.env["HTTP_REFERER"] || ups_invoice_path), notice: 'Label Generation Successful.' and return
      rescue => error
        redirect_to (request.env["HTTP_REFERER"] || ups_invoice_path), notice: error.message
      end
    end
  end

  def atlantic_invoice
    if params[:item].blank? && params[:number].present?
      @order = Order.includes(:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order],  design: [:designer,:categories,:images,property_values: :property]]).where('number = ?',params[:number].upcase).first
      redirect_to root_url, notice: 'Order not found' and return unless @order.present?
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order.present? && ( @order[:other_details].try(:[], 'blacklist_user') || @order.blacklist_user_order )
      @not_edit = @order.commercial_allow_editing
    else
      atlantic_label
    end
  end

  def dhl_invoice
    @integration_status = "new"
    @content_template = ["SAREE SAMPLE","DRESS (SALWAR KAMEEZ , KURTI) SAMPLE","IMITATION ORNAMENT SAMPLE","SAREE , IMITATION ORNAMENT SAMPLE","DRESS (GHAGRA CHOLI) SAMPLE","HAIR ACCESSORIES SAMPLE","SAREES , DRESS (SALWAR KAMEEZ) SAMPLE","DRESS (LEHENGA CHOLI) SAMPLE","DRESS (SALWAR KAMEEZ) SAMPLE","DRESS (SALWAR) / IMITATION ORNAMENT SAMPLE","DRESS (SALWAR KAMEEZ) / DRESS MATERIAL SAMPLE","DRESS (KURTA , KURTI) SAMPLE","BOLUSE SAMPLE"]
    if params[:item].blank? && params[:number].present?
      @order = Order.where('number = ?',params[:number].upcase).includes(:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order],  design: [:designer,:categories,:images,property_values: :property]]).first
      redirect_to root_url, notice: 'Order not found' and return unless @order.present?
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order.present? && (@order[:other_details].try(:[], 'blacklist_user')  || @order.blacklist_user_order)
      @not_edit = @order.commercial_allow_editing
    else
      dhl_label
    end
  end

  def dhl_ecom_invoice    
    if params[:item].blank? && params[:number].present?
      @order = Order.where('number = ?',params[:number].upcase).includes(:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order],  design: [:designer,:categories,:images,property_values: :property]]).first
      redirect_to root_url, notice: 'Order not found' and return unless @order.present?
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order.present? && ( @order[:other_details].try(:[], 'blacklist_user')  || @order.blacklist_user_order )
      @not_edit = @order.commercial_allow_editing
      @show_split_msg = @order.split_above_x_gbp
    else
      dhl_ecom_label
    end
  end

  def delhivery_invoice
    if params[:item].blank? && params[:number].present?
      @order = Order.where('number = ?',params[:number].upcase).includes(:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order], design: [:designer,:categories,:images,property_values: :property]]).first
      redirect_to root_url, notice: 'Order not found' and return unless @order.present?
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order.present? && ( @order[:other_details].try(:[], 'blacklist_user') || @order.blacklist_user_order )
      @not_edit = @order.commercial_allow_editing
    else
      delhivery_label
    end
  end

  def skynet_invoice
    if params[:item].blank? && params[:number].present?
      @order = Order.where('number = ?',params[:number].upcase).includes(:tickets, line_items: [:addon_type_values, replaced_product: [:line_item_addons, :designer_order], design: [:designer,:categories,:images,property_values: :property]]).first
      redirect_to root_url, notice: 'Order not found' and return unless @order.present?
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order.present? && ( @order[:other_details].try(:[], 'blacklist_user') || @order.blacklist_user_order )
      @not_edit = @order.commercial_allow_editing
    else
      skynet_invoice_label
    end
  end

  def invoice
    @order = Order.where(number: params[:number].try(:upcase)).first
    @hide_header = true
    @hide_footer = true
    redirect_to '/404' and return unless @order.present?
    @commercial,paypal_rate,currency_code = @order.get_commercial_values
    @shipment_detail = {}
    @shipment_detail['awb_number'] = params[:tracking_num].presence || @order.tracking_number
    @shipment_detail['courier'] = (params[:courier].presence || @order.courier_company).try(:downcase)
    if params[:invoice_number].present?
      @date = params[:invoice_number]
    elsif @order.order_notification.present? && (key=@order.order_notification.keys.find{|k| k.to_s.include? 'invoice'}).present?
      @date = key.to_s.split('_')[1]
    end
    @shipment_detail['total_price'] = 0
    unless @commercial
      litem_multiplier = (@order.total > 5000 || params[:fedex] == '1') ? 0.5 : 1
      @market_rate = 1
    else
      @shipment_detail['currency_code']= currency_code
      @market_rate = ((cc = CurrencyConvert.where{(symbol == currency_code) | (iso_code == currency_code)}.first).exchange_rate.presence || cc.market_rate)
    end

    unless params[:item].present?
      @items=[]
      selected_items,order_total_price = [],0
      @order.designer_orders.preload(line_items: [:line_item_addons,[design: [:categories,property_values: :property]]]).to_a.each do |dos|
        line_items = dos.line_items.to_a
        dos_total  = (line_items.sum{|li| (li.snapshot_price * li.quantity)}.to_f)
        #100.times{puts market_rate}
        line_items.each do |line_item|
          addon_price = line_item.line_item_addons.to_a.sum(&:snapshot_price)
          order_total_price += ((line_item.snapshot_price + addon_price) * line_item.quantity).to_f
          next if line_item.status.present? || ['canceled','vendor_canceled','buyer_returned'].include?(dos.state)
          design = line_item.design
          selected_items << {
            item_id: line_item.id,
            addon_price: addon_price,
            designable_type: design.designable_type,
            hsn_code: design.categories.hsn_code,
            designer_discount: (line_item.snapshot_price * line_item.quantity * dos.discount.to_i/dos_total).round(2),
            name: ((['saree','kurta','kurti','salwarkameez','lehenga','jewellery'].include? design.designable_type.try(:downcase)) ? design.invoice_category_name('dhl').titleize : design.categories.first.name),
            quantity: line_item.quantity,
            weight: line_item.weight.to_f/1000,
            price: line_item.snapshot_price,
          }
        end
      end
      selected_items_group = selected_items.group_by{|item| item[:name]}
      invoice_data= {
        items_id: [],
        order_total_price: (order_total_price + @order.shipping_charges),
        discount: @order.get_total_discount,
        market_rate: @market_rate,
        commercial: @commercial,
        paypal_rate: paypal_rate,
        shipper_name: @shipment_detail['courier']
      }
      items_price_total, shipping = 0, {}
      shipping_charges = @order.shipping_charges / selected_items.sum{|i| i[:quantity].to_i }
      selected_items_group.each do |product_name, items|
        items_count, item_discounted_price, items_price, hsn_code = Shipment.calculate_invoice_items(items, @items, invoice_data, product_name, shipping_charges, true)
        items_price_total  += items_price
        @shipment_detail['total_price'] += items_price
      end
    else
      @items = []
      @shipment_detail['total_price'] = 0
      params[:item].each_slice(8).each do |item|
        discounted_price = (item[2].to_f - item[3].to_f).round(2)
        item_price = (item[1].to_i * discounted_price).round(2)
        gst_tax = (item_price - (item_price/(1+item[7].to_i/100.0)))
        taxable_value =  ((item_price - gst_tax) * @market_rate).round(2)
        @shipment_detail['total_price'] += item_price
        @items << {
          name: item[0],
          quantity: item[1],
          price: item[2].to_f,
          item_discount: item[3].to_f,
          item_discounted_price: discounted_price,
          taxable_value: taxable_value,
          total_price: item[1].to_i.zero? ? discounted_price : (item[1].to_i * discounted_price).round(2),  
          hsn_code: item[4],
          designable_type: item[5],
          weight: item[6],
          gst_rate: item[7],
          gst_tax: (gst_tax * @market_rate).round(2)
        }
      end
      @shipment_detail['total_price'] if @commercial
    end
    
    if params[:edit].present?
      @edit = true
    end
    
    if params[:fedex].present?
      @fedex = true
    end

    if params[:packing_list].present?
      @packing_list = true
    end
    
    respond_to do |format|
      format.html # show.html.erb
      format.pdf do
        if @commercial
          render :layout => false,
                 :pdf => @shipment_detail['awb_number'].presence || @order.number,
                 :template => 'orders/_commercial_invoice.html'
        else
          render :layout => false,
                 :pdf => @shipment_detail['awb_number'].presence || @order.number,
                 :template => 'orders/invoice.html'
        end
      end
    end
  end
  
  def label
    @order = Order.includes(:line_items => [:design => :categories]).where(:number => params[:number]).first
    if @order.present?
      respond_to do |format|
        format.html # show.html.erb
        format.pdf do
          render :layout => false,
                 :pdf => @order.number,
                 :template => 'orders/label.html'
        end
      end
    else
      redirect_to '/orders', :notice => "No order found"
    end
  end

  def check_order_label
    if request.post?
      LineItem.where(id: params[:id]).update_all(sent_to_invoice: "Y-#{Date.today.strftime('%d/%m/%y')}", check_items_done_by: current_account.id, check_items_done_at: DateTime.now)
      LineItem.bulk_add_into_scan('LineItem', params[:id], 'Sent For Invoicing', current_account.id)
      LineItem.update_rack_status(condition: {id: params[:id]}, status: 'rack_out')
      response = {item_status: 'Checked', done_by: current_account.name}
      render :json => response
    else
      @order =Order.where(number: params[:number].try(:upcase)).preload(:line_items).first
      @count = @order.line_items.select{|l| l.sent_to_invoice == "Y-#{Date.today.strftime('%d/%m/%y')}"}.sum(&:quantity)
      @order.add_notes_without_callback("Sent for Invoicing on #{Date.today.strftime("%d/%m/%y")}",'dispatch',current_account) if @count > 0 && (@order.notes.to_s.exclude?("Sent for Invoicing on #{Date.today.strftime("%d/%m/%y")}"))
      @is_gift_wrap_order = @order.order_addon.try(:gift_wrap_price?)
      respond_to do |format|
        format.html
        format.pdf do
          render :layout => false,
                 :pdf => @order.number,
                 orientation: 'Landscape',
                 :template => 'orders/check_order_label.html',
                 page_size: 'A4',
                 margin: { bottom: 0, top: 5 }
        end
      end
    end
  end

  def shipping_label
    @order =Order.where(number: params[:number].try(:upcase)).first
    @shipment = @order.shipments.without_designer_order.recent_first.first
    respond_to do |format|
      format.html
      format.pdf do
        render :layout => false,
               :pdf => @order.number,
               orientation: 'Landscape',
               :template => 'orders/shipping_label.html',
               page_size: 'A4',
               margin: { bottom: 0, top: 5 }
      end
    end
  end

  def pay_by_paypal
    @order = Order.where(:number => params[:number]).first
    if @order.present?
      if @order.currency_code.present?
        symbol = @order.currency_code
        country_code = @order.country_code
      else
        symbol = @symbol || session[:country][:symbol]
        country_code = @country_code || session[:country][:country_code]
      end
      if ['new', 'cancel', 'pending', 'followup'].include?(@order.state)
        @order.move_to_pending! if @order.state == "cancel"
        res =  paypal_create(@order, country_code, symbol)
        # res = JSON.parse(res.first)
        if res["has_error"]
          redirect_to root_url, :notice => res["errors"]
        end
        redirect_url =  res["links"].find { |link| link["rel"] == "approve" }["href"]
        redirect_to redirect_url and return 
      elsif params[:addons].present?
        redirect_to @order.paypal_payments_standard_url(order_url(@order.number), symbol, country_code,params[:addons].to_i)
      else
        redirect_to root_url, :notice => "No order found."
      end
    else
      redirect_to root_url, :notice => "No order found."
    end  
  end

  def pay_by_card
    @order = Order.where(:number => params[:number]).first
    if @order && (@order.state == "new" || @order.state == "cancel" || @order.state == "pending")
      @order.move_to_pending! if @order.state == "cancel"
      @merchant_id = 'M_sha21501_21501'
      @merchant_key = '39sxhhs7xv2n2y2us2'
      @checksum =  get_checksum(@merchant_id,
                                @order.number,
                                @order.total,
                                order_response_url,
                                @merchant_key)
      render "autopost", :layout => false   
    else
      redirect_to root_url, :notice => "No order found."
    end
  end

  def duplicate
    old_order = Order.preload(:stitching_measurements).where(:number => params[:order_no]).first
    design = Design.where(id: params[:design_id].to_i).first
    if (response = old_order.get_old_item_id(design, params))[:more_input]
      render :json => response
    else
      new_number,event = (old_order.present? ? old_order.duplicate_order(design.id, current_account, old_item_id: response[:old_item_id], variant_id: params[:variant_id]) : [false,nil])
      if new_number
        new_order = Order.where(number:new_number).first
        new_order.add_measurements(design.id,params[:replace_data],old_order) if params[:replace_data].present? && old_order.stitching_measurements.any?{|sm| sm.design.id == params[:replace_data][0].to_i} && params[:replace_data].length >= 2
        render :json => {notes: event, time: Time.current.strftime('%d, %b %y'), :order_no => new_number, :order_id => old_order.id}
      else
        render :text => 'notok'
      end
    end
  end
  
  def check_items
    response_data = Hash.new
    if (order = Order.where(:number => params[:number]).first).present?
      order.check_items
      response_data[:status] = 'Order checked'
    else
      response_data[:error] = 'Order not found'
    end
    render :json => response_data
  end

  def customer_order_mail
    response_json = {}
    if (order = Order.find_by_id(params[:order_id])).present?
      if params[:request_type].present?
        case params[:request_type]
        when 'refund_complete'
          response_json = refund_mail(order)
        when 'min_req'
          response_json = min_req_mail(order)
        else
          # Do nothing  
        end
      end         
    else
      response_json = {:error => 'Order not found'}
    end
    render :json => response_json
  end
  
  def refund_mail(order)
    response_json = {}
    if order.billing_email.present?
      SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "send_mail_for_refund_completed", {"#{order.class}": order.id})
      # OrderMailer.sidekiq_delay.send_mail_for_refund_completed(order)
      if (event = order.add_notes_without_callback('Refund Completed.','return'))
        response_json = {notes: event, time: Time.current.strftime('%d, %b %y'), :order_id => order.id}
      else
        response_json = {:error => 'error saving order. please retry'}
      end
    else
      response_json = {:error => 'Billing email not found'}
    end
    response_json
  end
  
  def min_req_mail(order)
    response_json = {}
    if order.total < MIN_INTERNATIONAL_ORDER.to_i and order.billing_email.present?
      SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "send_mail_for_min_req", {"#{order.class}": order.id})
      # OrderMailer.sidekiq_delay.send_mail_for_min_req(order)
      event = order.add_notes_without_callback('min req', 'other')
      tag = 'min'
      order.tag_list.add(tag)
      if order.save!
        tags = order.tag_list.reject{|tag| tag.include?('convert-mkt')}.join(',')
        response_json = {notes: event, time: Time.current.strftime('%d, %b %y'), :tags => tags, :order_id => order.id}
      else
        response_json = {:error => 'error saving order. please retry'}
      end
    else
      response_json = {:error => 'Check order details'}
    end
    response_json
  end

  def get_states
    state_list = []
    if (country_states = Country.get_states(params[:country])).present?
      state_list = country_states.sort
    end
    render :json => state_list
  end

  def get_state_dial_code
    if (dial_code = Order::COUNTRY_DIAL_CODE[params[:country]]).present?
      response = {dial_code: dial_code }
      status = 200
    else
      response = {error: true}
      status = 422
    end
    render :json => response, :status => status
  end

  def get_pincode_format
    pincode_format = []
    pincode_format << PINCODE_FORMATS[params[:country]]
    autocomplete_zipcode_data = if AUTOCOMPLETE_ZIPCODE_ACTIVATED
      AUTOCOMPLETE_ZIPCODE[params[:country].to_s.downcase]
    end || []
    render :json => [pincode_format,autocomplete_zipcode_data]
  end

  def update_landmark
    landmark = params[:landmark]
    order_number = params[:order_number]
    buyer_confirmation = params[:buyer_confirmation] == 'true'
    order = Order.where(number: order_number).first
    if order.present? && order.cod?
      if buyer_confirmation
        if order.can_good_data?
          order.add_tags_skip_callback('user_forced_marked_sane')
          SidekiqDelayGenericJob.set(queue: 'high').perform_async("#{order.class}", order.id, "good_data!")
          # order.sidekiq_delay(queue: 'high').good_data!
        end
        render :json => {status: 'success',location: '',buyer_confirmed: 'true'}
      elsif landmark.present?
        new_address = "#{order.street} , Landmark: #{landmark}"
        if order.update_column('street',new_address)
          if order.can_good_data?
            order.add_tags_skip_callback('user_provided_landmark_marked_sane')
            SidekiqDelayGenericJob.set(queue: 'high').perform_async("#{order.class}", order.id, "good_data!")
            # order.sidekiq_delay(queue: 'high').good_data!
          end
          render :json => {status: 'success',location: params[:location_pt],buyer_confirmed: 'false'} 
        end
      else
        render json: {status: 'fail'} 
      end  
    else
      render json: {status: 'fail'} 
    end  
  end

  def update_google_map_query_limit_status
    status = params[:status]
    SystemConstant.where(name: 'GOOGLE_MAP_API_LIMIT_EXCEEDED').update_all(value: status)
    render json: {status: 'success'}
  end
  
  def get_checksum(*args)
    require 'zlib'
    Zlib.adler32 args.join('|'), 1  
  end

  # def post_order_confirmation_work(order)    
  #   # Mail order details
  #   OrderMailer.delay.mail_order_details_to_buyer(order)
    
  #   if order.gharpay?
  #     OrdersController.delay.gharpay_create_order(order)
  #     order.designer_orders.each do |designer_order|
  #       OrderMailer.delay.dispatch_order_to_designer(order, designer_order)
  #     end
  #     order.gharpay
  #   elsif order.bank_deposit?
  #     order.bank_deposit!
  #     order.save
  #   end
  # end

# tracking details method is use for get orders details based on tracking number.
  def tracking_details
    additional_preload_array, order_preload_array = [], []
    if ENABLE_UNPACKING_BUCKET_PROCESS == 'true'
      additional_preload_array = [:stitching_measurements, [line_item_addons: [:addon_type_value]]]
      order_preload_array = [:designer_orders]
    end

    if (track_num = params[:tracking_number].try(:strip)).present?
      barcode_split = track_num.include?('+') ? track_num.split('+') : track_num.split('-')
      @des_ord_id = barcode_split[1].to_i
      @bag = nil
      if barcode_split.length == 1 || barcode_split.length > 2
        @tracking_num = track_num.downcase
        @des_ord_id = nil
        @orders = Order.preload(:stylist, :tags, order_preload_array).joins(:designer_orders).where('designer_orders.created_at > ?', INSCAN_DO_MONTHS.to_i.months.ago).where('LOWER(designer_orders.tracking_num) LIKE (?) OR LOWER(designer_orders.recent_tracking_number) LIKE (?)',"%#{@tracking_num}%","%#{@tracking_num}%")
      else
        @orders = []
        designer_order = DesignerOrder.preload(order: [:stylist, :tags, order_preload_array]).where(id:@des_ord_id).where('created_at > ?', INSCAN_DO_MONTHS.to_i.months.ago).first
        if designer_order.present?
          @orders << designer_order.order
          @tracking_num = designer_order.tracking_num
        end
      end
    elsif (bag_num = params[:bag_number].try(:strip)).present?
      preload_array = (DISABLE_ADMIN_FUCTIONALITY['detailed_unpacking'] ? [designer_orders: [:rack_list, order: [:stylist, :tags, order_preload_array], line_items: [:qc_done_by_account, additional_preload_array, design: [:images, :property_values => :property]]]] : [designer_orders: [:rack_list, :line_items, order: [:stylist, :tags]]])
      @bag = InwardBag.preload(preload_array).where(number: bag_num).last
      @designer_orders = @bag.try(:designer_orders)
      @tracking_num, @orders, @des_ord_id = nil, nil, nil
    elsif params[:item_scan].present?      
      item_id = params[:item_scan].split('-')[1]
      base_preload_array = [designer_order: [:line_items, :rack_list, order: order_preload_array]]
      preload_array = (DISABLE_ADMIN_FUCTIONALITY['detailed_unpacking'] ? [:qc_done_by_account, additional_preload_array, base_preload_array, [design: [:images, :property_values => :property]]] : base_preload_array)      
      @line_item = LineItem.preload(preload_array).where(id: item_id).last
    else 
      @open_bags = InwardBag.preload(:designer_orders).where(status: 'open').paginate(page: params[:page], per_page: 20)
    end
  end

  def unpack_to_order_detail
    @wrong_unpack = false
    if params[:line_item_id]
      item_id = params[:line_item_id].split('-')[-1]
      if (line_item = LineItem.find_by_id(item_id)).present?
        order_number = line_item.order.number
        redirect_to order_order_detail_path(order_number)
      else
        @wrong_unpack = true
      end
    end
  end

  def best_zone_stitching_call
    @integration_status = 'new'
    @hide_menu = true
    # order_list = Order.unscoped.joins(line_items: [line_item_addons: [addon_type_value: :addon_type_value_group]]).
    #               where("measuremnet_confirmed IS NULL").
    #               where("fabric_measured_on IS NOT NULL").
    #               where("fabric_measured_by IS NOT NULL")
    joins = [line_items: [line_item_addons: [addon_type_value: :addon_type_value_group]]]
    clauses_parent = {line_items: {stitching_sent: nil,measuremnet_confirmed: nil}}
    clauses_parent_not = "line_items.fabric_measured_on is not null and line_items.fabric_measured_by is not null"

    case params[:stitching_type]
    when 'addon'
      clause_child = { addon_type_value_groups: {name: ['standard_stitching', 'custom_stitching']}}
    when 'standard_stitching'
      clause_child = { addon_type_value_groups: {name: 'standard_stitching'}}
    when 'custom_stitching'
      clause_child = { addon_type_value_groups: {name: 'custom_stitching'}}
    when 'stitching'
      clauses_parent_not = ""
      joins << :tags
      clause_child = { tags: {name: 'stitching'}}
      # orders_ids = Order.unscoped.tagged_with('stitching').joins(:line_items).where("stitching_sent IS NULL").
      #                   where("measuremnet_confirmed IS NULL"
    else
      clause_child = { addon_type_value_groups: {name: ['standard_stitching', 'custom_stitching']}}
    end
    orders = Order.unscoped.where(state: 'sane').joins(joins).where(clauses_parent).where(clause_child).where(clauses_parent_not)
    @order_list = params[:country].present? ? orders.where(billing_country: params[:country]) : orders
    @orders = @order_list.paginate(page: params[:page], per_page: 10 || params[:items_per_page])
    ActiveRecord::Associations::Preloader.new.preload(@orders, [:tags, :events, line_items: [line_item_addons: [addon_type_value: :addon_type_value_group]]]); nil
    # try removing this query if possible later
    country_list = orders.group(:billing_country).count
    countries = Country.where(name: country_list.keys).all
    country_list_with_time = country_list.inject({}) do |summary, (country_name,count)|
      country =  countries.find{|cc| cc.name ==country_name}
      if country.present?
        flag = (params[:flag] == 'true' || (7..23).cover?(Time.now.in_time_zone(country.time_zone).hour))
        summary[country_name] = [Time.now.in_time_zone(country.time_zone).strftime("%H:%M"), count] if flag == true
      end
     summary
    end
    @country_list = country_list_with_time.sort_by {|k,v| v}.reverse
  end

  # This method return best shipper
  def get_best_shipper
    order = Order.find(params[:order_id])
    weight = [order.volumetric_weight.to_f, params[:weight].to_f].max
    @number = order.number
    @old_best_shipper = order.best_shipper
    normalized_wt = order.get_normalized_weight(weight)
    if order.cod? && order.international? && COD_COUNTRY_SHIPPER[order.country]
      shipper_name = COD_COUNTRY_SHIPPER[order.country]
    else
      shipper_name = order.check_dhl_ecom_atlantic_serviceable(normalized_wt)
    end
    if shipper_name.present? 
      @notice = "#{shipper_name}"
      order.update_column(:actual_weight,weight) if (weight != order.actual_weight && weight != order.volumetric_weight)
      order.update_column(:best_shipper,shipper_name) if (shipper_name != order.best_shipper)
    else
      @notice = "oops! seems invalid input"
    end
  end

  def get_volumetric_weight
    @order_val = Order.find(params[:order_id])
    volume_weight = (params[:length].to_i * params[:breadth].to_i * params[:height].to_i)/5000.0
    volume_weight = @order_val.get_normalized_weight(volume_weight)
    if volume_weight > 30
      @notice = "Volume Weight should be less than 30Kg."
    elsif @order_val.actual_weight.blank?
      @notice = "Please enter actual weight first."
    else
      @old_best_shipper = @order_val.best_shipper
      actual_weight = @order_val.get_normalized_weight(@order_val.actual_weight)
      max_weight = [actual_weight, volume_weight].max
      new_shipper =  @order_val.check_dhl_ecom_atlantic_serviceable(max_weight)
      if new_shipper.present?
        @order_val.update_column(:volumetric_weight,volume_weight) if (volume_weight != @order_val.volumetric_weight)
        @order_val.update_column(:best_shipper,new_shipper) if (new_shipper != @order_val.best_shipper)
      end
    end
  end

  def update_cod_order_state
    if params[:order_numbers].present?
      order_numbers = params[:order_numbers].collect{
        |key, value| value if value.present?
      }
      orders_to_mark_sane = Order.where(number: order_numbers)
      if params[:commit] == "Mark Sane"
        orders_to_mark_sane.each do |o|
          o.good_data!
          new_note = "#{o[:notes]} [#{current_account.email.split('@')[0]}]"
          o.update_column(:notes, new_note)
          o.add_notes_without_callback("#{current_account.try(:name)} marked sane",'state_change',current_account)
          o.notes = new_note
        end
      flash[:notice] = "#{order_numbers} COD orders marked sane."
      elsif params[:commit] == "Cancel Orders"
        orders_to_mark_sane.each do |o|
          o.add_notes_without_callback("cancel order",'state_change',current_account)
          o.cancel!
        end
      flash[:notice] = "#{order_numbers} COD orders cancelled"
      end
      respond_to do |format|
        format.html {redirect_to orders_path(:codpanel => 't',:view => 'list',:sort =>'total')}
      end
    else
      flash[:notice] = "Please select atleast one order"
      respond_to do |format|
        format.html {redirect_to orders_path(:codpanel => 't',:view => 'list',:sort =>'total')}
      end
    end
  end

  def update_duplicate_order_state
    if params[:order_numbers].present?
      order_numbers = params[:order_numbers].collect{
        |key, value| value if value.present?
      }
      orders_to_mark_confirmed = Order.where(number: order_numbers)
      if params[:commit] == "Confirm Orders"
        orders_to_mark_confirmed.each do |o|
          o.confirmed_order!
          new_note = "#{o[:notes]} [#{current_account.email.split('@')[0]}]"
          o.update_column(:notes, new_note)
          o.add_notes_without_callback("#{current_account.try(:name)} marked confirmed",'state_change',current_account)
          o.notes = new_note
        end
      flash[:notice] = "#{order_numbers} Duplicate orders marked confirmed."
      elsif params[:commit] == "Cancel Orders"
        orders_to_mark_confirmed.each do |o|
          o.add_notes_without_callback("cancel order",'state_change',current_account)
          o.cancel!
        end
      flash[:notice] = "#{order_numbers} Duplicates orders cancelled"
      end
      respond_to do |format|
        format.html {redirect_to orders_path(:duplicate => 't',:number => params['original_order'])}
      end
    else
      flash[:notice] = "Please select atleast one order"
      respond_to do |format|
        format.html {redirect_to orders_path(:duplicate => 't',:number => params['original_order'])}
      end
    end
  end

  def send_order_sms
    render nothing: true ,status: valid_sms? ? 200 : 204
  end

  def get_epst_lpst_details
    @operation_processes = OperationProcess.get_process_data.map{|p| [p.name, [p.id, p.process_type]]}
    @process_start_date = Date.today.beginning_of_day
    @process_end_date = Date.today.end_of_day
    if params[:order_number].present?
      order_number = params[:order_number].strip.first(10).upcase
      product_id = params[:product_id].to_s.strip.first(10)
      if params[:commit] == 'Mail Data'          
        ProcessDate.sidekiq_delay(queue: 'high')
          .mail_order_level_details(
            order_number,
            product_id,
            current_account.email
          )
        flash[:success] = 'Mail will be sent to you shortly.'
        redirect_to get_epst_lpst_details_path
      else
        order_level_data = ProcessDate.get_order_level_details(order_number, product_id)
      end
      product_id.present? ? @product = order_level_data : @order = order_level_data
    elsif params[:process_id].present? && params[:process_date_type].present?      
      process_data = JSON.parse(params[:process_id])
      operation_process = OperationProcess.find_by_id(process_data[0])
      start_date_param = params[:start_date]
      end_date_param = params[:end_date]
      @process_order_details = {}      
      @process_start_date = start_date = Date.new(start_date_param[:year].to_i, start_date_param[:month].to_i, start_date_param[:day].to_i)
      @process_end_date = end_date = Date.new(end_date_param[:year].to_i, end_date_param[:month].to_i, end_date_param[:day].to_i)       
      if params[:commit] == 'Mail Data'
        ProcessDate.sidekiq_delay(queue: 'high')
                   .mail_process_level_details(
                     operation_process,
                     process_data,
                     start_date,
                     end_date,
                     params[:process_date_type],
                     current_account.email
                   )
        flash[:success] = 'Mail will be sent to you shortly.'
        redirect_to get_epst_lpst_details_path
      else
        @process_order_details, skip_condition = operation_process.get_process_order_details(params[:process_date_type]) if operation_process
        @process_details = ProcessDate.get_process_level_details(operation_process, process_data, start_date, end_date, params[:process_date_type]).paginate(page: params[:page], per_page: 30)
      end
    end
  rescue ArgumentError => e
    redirect_to get_epst_lpst_details_path, notice: e.message
  end

  def send_sms_for_cancellation_of_cod_order
    error = true
    error_text = 'Invalid Phone Number'
    order = Order.where(number: params[:order_number]).first
    error, error_text = order.send_sms_cancel_order_code_dom
    render json: {error: error, error_text: error_text}
  end


  private

  def copy_from_session(session_hash = session)
    @order.add_notes(session_hash[:note], false) if session_hash[:note].present?
    @order.multi_channel_marketing = ''
    if session_hash[:utm_source].present?
      @order.utm_source = session_hash[:utm_source].split(',').last(1).join
      @order.multi_channel_marketing.concat('Source : ' + session_hash[:utm_source][1..231] + '-----')
    end
    if session_hash[:utm_medium].present?
      @order.utm_medium = session_hash[:utm_medium].split(',').last(1).join
      @order.multi_channel_marketing.concat('Medium: ' + session_hash[:utm_medium][1..231] + '-----')
    end
    if session_hash[:utm_campaign].present?
      @order.utm_campaign = session_hash[:utm_campaign].split(',').last(1).join
      @order.multi_channel_marketing.concat('Campaign: ' + session_hash[:utm_campaign][1..1000] + '-----')
      campaigns = session_hash[:utm_campaign][1..1000].split(',').uniq
      campaigns.each do |campaign|
        @order.tag_list.add("convert-mkt-#{campaign}")
      end
    end
    if session_hash.has_key?(:ip_address)
      @order.ip_address = session_hash[:ip_address]
    elsif request.remote_ip.present?
      @order.ip_address = request.remote_ip
    end
    @order.other_details['user_agent'] = request.user_agent
    @order.other_details['session_id'] = session_hash.id
    @order.app_source = session_hash[:app_source] if session_hash[:app_source].present?
    @order.amount_sent_payment_gateway = session_hash[:total_amount] if session_hash[:total_amount].present?
    @order.utm_term = session_hash[:utm_term][0..200] if session_hash[:utm_term].present?
    @order.utm_content = session_hash[:utm_content][0..200] if session_hash[:utm_content].present?
    @order.icn_term = session_hash[:icn_term][0..200] if session_hash[:icn_term].present?
    @order.utm_exp_id = session_hash[:utm_expid] if session_hash[:utm_expid].present? # this is used for google optimise experiments
    @order.utm_exp_id = session[:exp_sorted].keys if session[:exp_sorted].present?
    @order.geo = @order.billing_country.downcase == 'india' ? 'domestic' : 'international'
  end

  # DO NOT MAKE MISTAKES OUT HERE
  def ccavenue_mpcg_billing_page(order)
    order_params = Hash.new
    # Required PARAMS
    order_params['currency'] = 'INR'
    order_params['merchant_id'] = CCAVENUE_MCPG_MERCHANT_ID
    order_params['order_id'] = order.number
    order_params['amount'] = order.total
    order_params['redirect_url'] = order_params['cancel_url'] = order_ccavenue_mpcg_response_url(:utm_nooverride => 1)
    order_params['language'] = 'EN'
    
    # Alphabets (60) Characters allowed: Alphabet (A-Z), (a-z).Space in between words.
    order_params['billing_name'] = order.billing_name.gsub(/[^a-z ]/i, ' ').strip
    order_params['delivery_name'] = order.name.gsub(/[^a-z ]/i, ' ').strip

    # Alphanumeric (150) Characters allowed:  Alphabet (A-Z), (a-z). Numbers # (hash), Comma, circular brackets,  /(slash), dot, - (hyphen) Space in between words.
    order_params['billing_address'] = order.billing_street.gsub(/[^0-9a-z ]/i, ' ')
    order_params['delivery_address'] = order.street.gsub(/[^0-9a-z ]/i, ' ')

    # Alphabets (30) Characters allowed: Alphabet (A-Z), (a-z). Space in between words.
    order_params['billing_city'] = order.billing_city.gsub(/[^a-z ]/i, '').strip
    order_params['delivery_city'] = order.city.gsub(/[^a-z ]/i, '').strip

    # Alphabets (30) Characters allowed:  Alphabet (A-Z), (a-z). Space in between words.
    order_params['billing_state'] = order.billing_state.gsub(/[^a-z ]/i, '').strip
    order_params['delivery_state'] = order.buyer_state.gsub(/[^a-z ]/i, '').strip

    # Alphanumeric (15) Characters allowed:  Alphabet (A-Z), (a-z). Numbers
    order_params['billing_zip'] = order.billing_pincode.gsub(/[^0-9a-z]/i, '')
    order_params['delivery_zip'] = order.pincode.gsub(/[^0-9a-z]/i, '')

    # Alphabets (50) Characters allowed: Alphabet (A-Z), (a-z). Space in between words.
    order_params['billing_country'] = order.billing_country.gsub(/[^a-z ]/i, '').strip
    order_params['delivery_country'] = order.country.gsub(/[^a-z ]/i, '').strip

    # Numeric (20)
    order_params['billing_tel'] = order.phone.gsub(/[^0-9]/i, '').strip
    order_params['delivery_tel'] = order.phone.gsub(/[^0-9]/i, '').strip

    # Currently no validation
    order_params['billing_email'] = order.billing_email
    
    merchantData = ''
    order_params.each do |key,value|
      merchantData += (key + '=' + value.to_s + '&')
    end
    @encrypted_data = Order.ccavenue_mpcg_encrypt(merchantData,Mirraw::Application.config.ccavenue_mpcg_enc_key)
  end

  def update_order_payment_status(status, amount, order, confirmation_required=false)
    case status
    when 'success'
      if amount.to_i == order.total
        unless confirmation_required
          if order.can_good_data?
            order.add_notes("SUCCESS", false, current_account)
            # order.sidekiq_delay(queue: 'critical').good_data!
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "good_data!")
            # order.sidekiq_delay.post_order_confirmation_work
            SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "post_order_confirmation_work")
          end
        end
        order.save!
        notice = 'Your order was successfully completed.'
      else
        order.tag_list.add('payment_issue')
        order.add_notes_without_callback("#{order.payment_gateway} Payment difference #{amount.to_i}", 'payment', current_account)
        order.save!
        notice = 'Your order was successfully completed.'
      end
    when 'failure'
      if order.cod_available_on_order?
        notice = 'Sorry, Your Payment was not completed. Please retry using Cash On Delivery or another Payment Method.'
        auto_select_retry_cod = true
      else
        notice = 'Sorry, Your Payment was not completed. Please retry using another Payment Method.'
        auto_select_retry_cod = false
      end
      order.order_cancel_reason = 'transaction has been declined'
      order.cancel!
    when 'aborted'
      notice = 'Your transaction was not completed.'
      order.order_cancel_reason = notice
      order.cancel!
    when 'invalid'
      notice = "There was some issue with your order #{order.number} please contact customer support"
      order.order_cancel_reason = 'transaction issue with order'
      order.cancel!
    end
    if order.cancel?
      order.save
      redirect_to new_order_url(auto_select_retry_cod: auto_select_retry_cod), :notice => notice
    else
      redirect_to(order_url(order.number), :notice => notice)
    end
  end

  def delhivery_label
    shipper_delhivery = Shipper.where('lower(name) =? ','delhivery').first
    @order = Order.where(number: params[:number]).first
    _, shipping_telephone, shipping_address_1,_,shipping_city, shipping_pincode, shipping_state, _ = @order.get_warehouse_shipping_address
    begin
      commercial, paypal_rate, currency_code, market_rate = true ,1 , 'INR', 1
      selected_items = []
      params[:item].each{|key, value| selected_items << value if value[:selected].present?}
      selected_items_group        = selected_items.group_by{|item| item[:name]}
      items_weight_total = params[:total_weight]
      reference, shipping_amt, order_total_price, tax_amount = @order.get_order_invoice_details(commercial, shipper_delhivery.id, selected_items.collect{|i| i[:item_id]})
      order_total_price = @order.international_cod_price(order_total_price)
      shipping_charges = shipping_amt / selected_items.sum{|i| i[:quantity].to_i }
      total_tax = tax_amount / selected_items.sum{|i| i[:quantity].to_i }
      invoice_data = {
        items_id: [],
        order_total_price: order_total_price,
        discount: @order.international_cod_price(@order.get_total_discount),
        market_rate: 1,
        commercial: commercial,
        paypal_rate: paypal_rate,
      }
      selected_items_weight_total = params[:total_weight].to_f
      items_price_total           = 0
      items_weight_total          = 0
      shipping                    = {}
      invoice_items               = []

      selected_items_group.each do |product_name, items|
        items_count, item_discounted_price, items_price, hsn_code = Shipment.calculate_invoice_items(items, invoice_items, invoice_data, product_name, shipping_charges, total_tax: total_tax)
        items_price_total  += items_price
      end

      raise 'Select atleast one item' if items_price_total == 0

      raise "Pincode not available" unless Courier.where(name: 'Delhivery', pincode: @order.pincode, delivery: true).present?
      awb_number = Shipment.delhivery_awb('surface')
      raise "AWB Number not available retry after 10 mins" unless awb_number.present?
      pickup_location = {country: 'India',
              name: Mirraw::Application.config.delhivery_surface_client_name,
              phone: shipping_telephone,
              add:   shipping_address_1,
              city:  shipping_city,
              state: shipping_state,
              pin:   shipping_pincode }

      shipments_req = {
        waybill: awb_number,
        name: @order.name,
        order: reference,
        products_desc: selected_items_group.keys.join(',') ,
        order_date: @order.confirmed_at || @order.created_at,
        payment_mode: 'Prepaid',
        total_amount: items_price_total ,
        cod_amount: 0.0, add: @order.street.gsub(/[&;]/,''),
        city: @order.city,
        state: @order.buyer_state,
        phone: @order.phone,
        country: @order.country,
        pin: @order.pincode,
        weight: "#{params[:total_weight].to_f * 1000.0} gm"}

      request = 'format=json&data='+{pickup_location: pickup_location, shipments: [shipments_req]}.to_json
      api = Mirraw::Application.config.delhivery_baseurl + '/cmu/push/json/?token={token}'
      api_params = api.sub('{token}', Mirraw::Application.config.delhivery_surface_token)
      api_params_encoded = URI.encode(api_params)
      res = HTTParty.post(api_params_encoded, body: request, headers: {'Content-Type' => 'application/x-www-form-urlencoded'})
      if res.body.present? && res.headers.present? && res.headers.content_type.present? && res.headers.content_type.match('json').present?
        res_content = JSON.parse(res.body)
      else
        raise 'retry after 10 mins'
      end
      @shipment = Shipment.new(
        number:          awb_number,
        shipper:         shipper_delhivery,
        price:           items_price_total.to_i,
        weight:          items_weight_total,
        line_item_ids:   invoice_data[:items_id],
        order_id:        @order.id,
        packaging_type:  params[:packaging_type],
        invoicer_id:     current_account.id,
        packer_id:       params[:packer_id],
        mirraw_reference:  reference)

      if @shipment.save
        wa_ids = @order.designer_orders.collect(&:warehouse_address_id)
        label_string = Shipment.generate_delhivery_label_url(awb_number, @order, reference, items_price_total.to_i, 'Delhivery',true, wa_ids)
        @shipment.add_label(label_string)
        # SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(@shipment.class.to_s, @shipment.id, "add_label", label_string)
        ShipmentsController.sidekiq_delay(queue: 'critical')
                           .generate_invoice(
                             invoice_items,
                             @order.number,
                             @shipment.id, 0,
                             items_price_total
                           )
        @order.update_attributes(tracking_number: @shipment.number, courier_company: @shipment.shipper.name )
        gon.shipment_id = @shipment.id
      end
    rescue => error
      redirect_to :back, :notice => error.message
    end
  end

  def skynet_invoice_label
    shipper_skynet = Shipper.where('lower(name) =?','skynet').first
    @order = Order.preload(:order_addon,line_items: :line_item_addons).where(:number => params[:number]).first
    _, shipping_telephone, _, _, shipping_city, shipping_pincode, _, _ = @order.get_warehouse_shipping_address
    country_code =Country.find_by_name(@order.country).try(:iso3166_alpha2) || @order.country_code
    awb_number = AwbNumber.number_available(shipper_skynet.id,true).first
    begin
      raise "Domestic serive not supported" if country_code =="IN"
      raise 'Out of free AWB numbers. Please contact skynet.' unless awb_number.present?
      awb_number.update_column(:available, false)
      if COMMERCIAL_FOR_AUTOMATION == 'true'
        commercial, paypal_rate, currency_code = @order.get_commercial_values
      else
        commercial = false
        paypal_rate = 1
        currency_code = 'INR'
      end
      selected_items              = []
      params[:item].each{|key, i| selected_items << i if i[:selected].present?}
      reference, shipping_amt, order_total_price = @order.get_order_invoice_details(commercial, shipper_skynet.id, selected_items.collect{|i| i[:item_id]})
      selected_items_group        = selected_items.group_by{|item| item[:name]}
      items_weight_total          = ((params[:total_weight].to_f*2).ceil.to_f / 2)

      selected_content_description = "Refer to invoice"
      items_price_total,shipping  = 0,{}
      invoice_items               = []

      market_rate = ((cc = CurrencyConvert.where{(symbol == currency_code) | (iso_code == currency_code)}.first).exchange_rate.presence || cc.market_rate)
      invoice_data                = {
                                items_id: [],
                                order_total_price: order_total_price,
                                discount: @order.get_total_discount,
                                market_rate: market_rate,
                                commercial: commercial,
                                paypal_rate: paypal_rate,
                                shipper_name: 'skynet'
                              }
      shipping_charges = shipping_amt / selected_items.sum{|i| i[:quantity].to_i }
      selected_items_group.each do |product_name, items|
        items_count, item_discounted_price, items_price  = Shipment.calculate_invoice_items(items, invoice_items, invoice_data, product_name, shipping_charges)
        items_price_total  += items_price
      end

      items_price_total = (items_price_total + shipping['total'].to_f).round(2)
      consignee_details = {
        'waybill'               => awb_number.number,
        'consignee name'        => @order.name,
        'city'                  => @order.city,
        'country'               => ((SKYNET_COUNTRIES.keys.include? @order.country) ? SKYNET_COUNTRIES[@order.country] : @order.country),
        'address'               => @order.street.tr("\n"," ").tr("\r"," ").gsub("&","and").strip + ', ' + @order.state_code,
        'pincode'               => @order.pincode,
        'mobile'                => @order.phone,
        'consignee email'       => @order.email,
        'consignee address type'=> 'Residence',
      }
      shipper_detail = Mirraw::Application.config.dhl_shipper_constant
      shipment_details = {
        'weight'               => "#{(items_weight_total*1000)}",
        'package amount'       => "#{items_price_total}",
        'currency'             => currency_code,
        'product to be shipped'=> selected_content_description,
        'shipper name'         => shipper_detail['shipperName'].titleize,
        'shipper address'      => shipper_detail['shipperCompName'] + shipper_detail['shipperAddress1'] + shipper_detail['shipperAddress2'] + shipper_detail['shipperCity'],
        'length'               => params[:shipment_length].to_s,
        'breadth'              => params[:shipment_breadth].to_s,
        'height'               => params[:shipment_height].to_s,
        'return pin'           => shipping_pincode,
        'quantity'             => '1',
        'category of goods'    => 'non-documents',
        'shipper contact'      => shipping_telephone,
        'consignor email'      => '<EMAIL>',
        'consignor address type'=> 'Office',
        'consignor city name'  => shipping_city,
        'consignor country name'=> 'India',
        'pickup type'          => 'pickup',
        'pickup date'          => DateTime.now.in_time_zone.strftime('%m/%d/%Y'),
        'available from time'  => '6:00',
        'available till time'  => '23:00'
      }
      book_shipment = consignee_details.merge(shipment_details)
      url = Mirraw::Application.config.skynet_baseurl
      response = HTTParty.post(url+'/user/signin',{body: Mirraw::Application.config.skynet_credentials})
      result = response.parsed_response
      if result['status'].try(:downcase) == 'success'
        options = {headers: {'Authorization' => 'JWT '+result['token']}, body: {"data":[book_shipment]}}
        response = HTTParty.post("#{url}/bulkbookingjson",options)
        result = response.parsed_response
        if result['status'] == 'Success'
          shipment_data = result['data'].first
          if shipment_data['status'].try(:downcase) != 'invalid' || shipment_data['remarks'].blank?
            @shipment = Shipment.new(
              :number            => shipment_data['awb_number'],
              :shipper           => shipper_skynet,
              :price             => (items_price_total * paypal_rate).to_i,
              :weight            => items_weight_total,
              :line_item_ids     => invoice_data[:items_id],
              :shipment_type     => 'PREPAID',
              :payment_state     => 'unpaid',
              :order_id          => @order.id,
              :packaging_type    => 'Skynet_PAK',
              :invoicer_id       => current_account.id,
              :packer_id         => params[:packer_id],
              :mirraw_reference  => reference,
              :last_event        => 'shipment created'
            )
            if @shipment.save
              @order.update_attributes(tracking_number: @shipment.number, courier_company: shipper_skynet.name)
              OrderMailer.sidekiq_delay.report_mailer('Reminder to upload new AWB numbers for Skynet','Very few AWB Numbers are available for skynet integration. Please contact Skynet and upload new AWB numbers to continue using Skynet automation on order detail page.',{'to_email'=> DEPARTMENT_HEAD_EMAILS['operations'], 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}) if ([300,200,100,50].include? AwbNumber.where(shipper_id: shipper_skynet.id,available: true).pluck(:id).count)
            end
            # @shipment.sidekiq_delay(queue: 'critical').skynet_label
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(@shipment.class.to_s, @shipment.id, "skynet_label")
            ShipmentsController.sidekiq_delay(queue: 'critical')
                               .generate_invoice(
                                 invoice_items,
                                 @order.number,
                                 @shipment.id,
                                 shipping_charges,
                                 items_price_total
                               )
          else
            raise "#{shipment_data['remarks']}"
          end
        else
          raise "Something went wrong"
        end
      else
        raise 'Cannot authenticate, server problem'
      end
      gon.shipment_id = @shipment.id
      @order.remove_tags_skip_callback('shipment_error')
    rescue => error
      awb_number.update_column(:available, true) if awb_number.present? && awb_number.available == false
      @order.add_tags_skip_callback('shipment_error')
      redirect_to :back, :notice => error.message
    end
  end

  def dhl_label
    @order = Order.preload(:order_addon,line_items: :line_item_addons).where(:number => params[:number]).first    
    order_shipment_create = OrderShipmentCreate.new(@order, 'DHL', params)
    automation_status = order_shipment_create.create_order_automation_shipment
    if automation_status[:error]
      @order.add_tags_skip_callback('shipment_error')
    else
      @shipment = true
    end    
    redirect_to (request.env["HTTP_REFERER"] || dhl_invoice_path), notice: automation_status[:message] and return
  end  

  def dhl_ecom_label
    shipper_dhl_ecom = Shipper.where('lower(name) = ?','dhl ecom').first
    @order = Order.where(:number => params[:number]).preload(:order_addon,line_items: :line_item_addons).first
    _, _, shipping_address_1,shipping_address_2,shipping_city, shipping_pincode, _, _ = @order.get_warehouse_shipping_address
    @order.add_notes_without_callback('Test Params', params, nil)
    country_code =Country.find_by_name(@order.country).try(:iso3166_alpha2) || @order.country_code
    begin
      raise "Domestic service not supported" unless shipper_dhl_ecom.domestic || country_code !="IN"
      raise "DHL ECOM Service for #{@order.country} is not supported" if ['US','GB','AU'].exclude?(country_code)
      if COMMERCIAL_FOR_AUTOMATION == 'true'
        commercial, paypal_rate, currency_code = @order.get_commercial_values
      else
        commercial = false
        paypal_rate = 1
        currency_code = 'INR'
      end
      selected_items              = []
      params[:item].each{|key, i| selected_items << i if i[:selected].present?}
      reference, shipping_amt, order_total_price, tax_amount = @order.get_order_invoice_details(commercial, shipper_dhl_ecom.id, selected_items.collect{|i| i[:item_id]})
      selected_items_group        = selected_items.group_by{|item| item[:name]}
      items_weight_total = params[:total_weight]
      selected_shipment_depth = params[:shipment_depth]
      selected_shipment_width = params[:shipment_width]
      selected_shipment_height = params[:shipment_height]
      shipment_desc = params[:ship_contents].tr("&",",") || "Refer to invoice"

      items_price_total           = 0
      shipping                    = {}
      invoice_items               = []
      item_details                = []
      market_rate = ((cc = CurrencyConvert.where{(symbol == currency_code) | (iso_code == currency_code)}.first).exchange_rate.presence || cc.market_rate)
      invoice_data                = {
                                items_id: [],
                                order_total_price: order_total_price,
                                discount: @order.get_total_discount,
                                market_rate: market_rate,
                                commercial: commercial,
                                paypal_rate: paypal_rate,
                                shipper_name: 'dhl_ecom'
                              }
      shipping_charges = shipping_amt / selected_items.sum{|i| i[:quantity].to_i }
      total_tax = tax_amount / selected_items.sum{|i| i[:quantity].to_i }
      selected_items_group.each do |product_name, items|
        items_count, item_discounted_price, items_price, hsn_code = Shipment.calculate_invoice_items(items, invoice_items, invoice_data, product_name, shipping_charges, total_tax: total_tax)
        item_details << {
          skuNumber: items.last[:sku_id],
          description: product_name.gsub('-', ' ').camelize,
          itemQuantity: items_count,
          itemValue: item_discounted_price.round(2),
          weightUOM: "G",
          hsCode: hsn_code,
          countryOfOrigin: "IN",
          contentIndicator: nil,
          igstRate: nil,
          discount: 0,
          commodityUnder3C: 'true',
          meis: 'true',
          cgstAmount: nil, 
          sgstAmount: nil,
          igstAmount: nil,
          cessAmount: nil
        }
        items_price_total  += items_price
      end

      if shipping['total'].to_f > 0.0
        total_quantities = item_details.collect{|item| item[:itemQuantity]}.sum
        ship_amount = shipping['total'].to_f / total_quantities
        item_details.map{|item| item[:itemValue] = (item[:itemValue] + (item[:itemQuantity] * ship_amount)).round(2)}
      end  

      invoice_number = @order.get_invoice_number
      invoice_date   = @order.other_details.try(:[],'invoice_date').try(:strftime, '%Y-%m-%d').presence || Date.today.strftime('%Y-%m-%d')

      dhl_ecom_urls = Mirraw::Application.config.dhl_ecom
      token_response = HTTParty.get("#{dhl_ecom_urls[:token_url]}?clientId=#{dhl_ecom_urls[:credentials]['client_id']}&password=#{dhl_ecom_urls[:credentials]['password']}&returnFormat=json")
      if token_response['accessTokenResponse']['responseStatus']['code'] == '100000'
        token = token_response['accessTokenResponse']['token']
        header = {
          messageType: "LABEL",
          messageDateTime: DateTime.now.strftime("%Y-%m-%dT%H:%M:%S%Z"),
          accessToken: token,
          messageVersion: "1.4",
          messageLanguage: "en"
        }
        from_address = "#{shipping_address_1},#{shipping_address_2}"
        address_array = Order.multi_line_address(50, from_address)
        pickupAddress = {
          name: 'Mirraw',
          address1: address_array[0],
          address2: address_array[1],
          address3: address_array[3],
          city: shipping_city,
          country: "IN",
          state: "27",
          postCode: shipping_pincode
        }

        street = @order.street.tr("\n"," ").tr("\r"," ").gsub("&","and").strip
        street_address = @order.multi_line_address([(street.length * 0.7).to_i,35].min,street)

        consigneeAddress = {
          name: @order.multi_line_address(35,@order.name.gsub("&","and").strip)[0],
          address1: street_address[0],
          address2: street_address[1],
          address3: street_address[2] || "",
          city: @order.city,
          state: @order.state_code,
          country: country_code,
          postCode: @order.pincode,
          idNumber: nil,
          idType: nil
        }


        returnAddress = {
          name: @order.multi_line_address(35,@order.name.gsub("&","and").strip)[0],
          address1: street_address[0],
          address2: street_address[1],
          address3: street_address[2] || "",
          city: @order.city,
          state: @order.state_code,
          country: country_code,
          postCode: @order.pincode
        }


        label = {
          format: "PDF",
          pageSize: "400x600",
          layout: "1x1"
        }

        shipment_items_hash = {
          consigneeAddress: consigneeAddress,
          shipmentID: "#{dhl_ecom_urls[:credentials]['prefix']}#{reference}",
          deliveryConfirmationNo: nil,
          packageDesc: shipment_desc,
          totalWeight: (items_weight_total.to_f*1000).to_i,
          totalWeightUOM: "G", 
          dimensionUOM: "CM",
          height: ('%.2f' % selected_shipment_height).to_f,
          length: ('%.2f' % selected_shipment_depth).to_f,
          width: ('%.2f' % selected_shipment_width).to_f,
          productCode: "PLT",
          incoterm: ['US', 'AU'].include?(country_code) ? 'DDP': 'DDU',
          contentIndicator: "00",
          totalValue: ('%.2f' % items_price_total).to_f,
          currency: currency_code,
          shipmentContents: item_details,
          invoiceNumber: invoice_number,
          invoiceDate: invoice_date,
          reverseCharge: 'FALSE',
          igstPaymentStatus: 'B',
          termsOfInvoice: 'FOB'
        }

        
        shipment_items_hash[:returnAddress] = returnAddress if country_code == 'US'

        body = {
          pickupAccountId: dhl_ecom_urls[:credentials]['pickup_account_id'],
          soldToAccountId: dhl_ecom_urls[:credentials]['sold_to_account_id'],
          inlineLabelReturn: nil,
          pickupAddress: pickupAddress,
          shipperAddress: pickupAddress,
          shipmentItems: [shipment_items_hash],
          label: label
        }


        labelRequest = {
          labelRequest: {
            hdr: header,
            bd: body 
          }
        }

        shipment_label_response = HTTParty.post(dhl_ecom_urls[:shipment_label_url],body: labelRequest.to_json, headers: {'Content-Type' => 'application/json'})
        label_details = shipment_label_response['labelResponse']['bd']['labels'][0]
        if label_details.present? && label_details['responseStatus']['message'] == 'SUCCESS' && ['200','203'].include?(label_details['responseStatus']['code'])
          
          label_url = label_details['labelURL']          
          label_content = Base64.decode64(label_details['content'])
          
          @shipment = Shipment.new(
            :number         => "#{dhl_ecom_urls[:credentials]['prefix']}#{reference}",
            :shipper        => shipper_dhl_ecom,
            :price          => (items_price_total * paypal_rate).to_i,
            :weight         => items_weight_total,
            :line_item_ids  => invoice_data[:items_id],
            :order_id       => @order.id,
            :packaging_type => params[:packaging_type],
            :invoicer_id    => current_account.id,
            :packer_id      => params[:packager_id],
            :courier_label_url => label_url,
            :mirraw_reference => reference
          )

          @order.update_attributes(tracking_number: @shipment.number, courier_company: @shipment.shipper.name ) if @shipment.save
          @shipment.add_label(label_content)
          ShipmentsController.sidekiq_delay(queue: 'critical')
                             .generate_invoice(
                               invoice_items,
                               @order.number,
                               @shipment.id,
                               shipping_charges,
                               items_price_total
                             )
          gon.shipment_id = @shipment.id
          @order.remove_tags_skip_callback('shipment_error')
          redirect_to (request.env["HTTP_REFERER"] || dhl_ecom_invoice_path), notice: 'Label Generation Successful.' and return
        else
          error_message = label_details.present? ? label_details['responseStatus']['messageDetails'].collect(&:values).flatten.join(' ') : shipment_label_response['labelResponse']['bd']['responseStatus']['messageDetails'].collect(&:values).flatten.join('. ')
          raise "Label Generation Failed. #{error_message}"
        end
      else
        raise "Token Expired During Execution.Please Try Again."
      end
    rescue => error
      @order.add_tags_skip_callback('shipment_error')
      redirect_to (request.env["HTTP_REFERER"] || dhl_ecom_invoice_path), notice: error.message
    end
  end

  def atlantic_label
    @order = Order.preload(:order_addon,line_items: :line_item_addons).where(number: params[:number]).first    
    order_shipment_create = OrderShipmentCreate.new(@order, 'Atlantic', params)
    automation_status = order_shipment_create.create_order_automation_shipment
    if automation_status[:error]
      @order.add_tags_skip_callback('shipment_error')
    else
      @shipment= true
    end    
    redirect_to (request.env["HTTP_REFERER"] || atlantic_invoice_path), notice: automation_status[:message] and return
  end

  def valid_sms?
    @order.present? &&
    ORDER_MESSAGES[params[:sms_type]] &&
    @order.send_order_sms(params[:sms_type],current_account.email)
  end

  def order_params
    params.require(:order).permit(:billing_first_name, :billing_last_name, :billing_name, :billing_email,
      :seamless_account, :password,
      :billing_country, :billing_pincode, :billing_street, :billing_city, :billing_state, :billing_phone,
      :first_name, :last_name, :name,
      :country, :billing_street_line_1, :billing_street_line_2, :pincode, :street_line_1, :street_line_2, :street, :city, :buyer_state, :phone,
      :pay_type, :paypal_smart_pay, :paypal_inline_cc)
  end

  def set_smart_pay_visiblity(type: 'paypal')
    if @country_code != 'IN'
      if ENV['PAYPAL_SMART_CHECKOUT'].present?
        @paypal_smartpay = if params[:paypal_smartpay] == '1'
          true
        elsif !session[:pp_smart].nil?
          session[:pp_smart]
        else
          session[:pp_smart] = rand(ENV['PAYPAL_SMART_CHECKOUT'].to_i) == 0
        end
      end

      if ENV['PAYPAL_INLINE_CC_ROLLOUT'].present?
        @show_inline_cc = if params[:paypal_inline_cc] == '1'
          true
        elsif !session[:pp_inline_cc].nil?
          session[:pp_inline_cc]
        else
          session[:pp_inline_cc] = rand(ENV['PAYPAL_INLINE_CC_ROLLOUT'].to_i) == 0
        end
      end
    end
  end

end
