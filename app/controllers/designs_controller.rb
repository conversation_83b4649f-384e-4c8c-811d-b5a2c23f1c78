class DesignsController < ApplicationController
  before_filter :authenticate_account!, :except => [:index, :show, :add_to_reqlist, :wholesale_create, :similar, :change_master_image, :get_line_items_count,:more_designers,:get_variants]
  load_and_authorize_resource :designer, :except => [:add_to_reqlist, :wholesale_create,:more_designers], unless: :is_app_request?
  load_and_authorize_resource :design, :through => :designer, :except => [:get_option_type,:add_to_reqlist,:create_designable, :wholesale_create, :show, :similar, :update_design_quantity, :change_master_image, :get_line_items_count,:more_designers,:bulk_upload_design, :design_eta], unless: :is_app_request?
  before_filter :design_show_set_required_variables, only: :show
  before_filter :required_param_for_new_design, only: [:new, :create]
  before_filter :set_max_uploadable_categories, only: [:new, :create, :update, :edit]
  rescue_from ActiveRecord::RecordNotFound do |exception|
    action_called = params[:action]
    if params[:controller] == 'designs' && ['similar','show','edit'].include?(action_called)
      if (design_slug = params[:id] || params[:design_id]).present? && design = Design.find_by_cached_slug(design_slug)
        case action_called
        when 'show','edit'
          redirect_to designer_design_url(design.designer,design)
        when 'similar'
          redirect_to designer_design_similar_url(design.designer,design)
        end
      else
        @integration_status = "new"
        render "errors/error_404", status: :not_found
        # redirect_to root_path, :notice => 'Sorry, no such product is present!'
      end
    else
      raise
    end
  end
  layout 'seller',only: [:edit,:update,:new,:create]

  caches_action :show, cache_path: proc {"#{@design.cache_key}_#{@symbol}_#{@country_code}_#{@theme_tester.try(:current_theme)}"}, if: proc { !current_account.present? }, layout: false, expires_in: 6.hours

  # GET /designs
  # GET /designs.json
  def index
    # Redirect back to designers url
    respond_to do |format|
      format.html { redirect_to designer_url(@designer)}
    end
  end

  def wholesale_create
    @wholesale = Wholesale.create!(params[:wholesale])
    OrderMailer.sidekiq_delay.send_wholesale_request(@wholesale)
    redirect_to :back, :notice => 'Your request has been submitted'
  end

  # GET /designs/1
  # GET /designs/1.json
  def show
    if @design.state == "reject"
      if current_account.present? && current_account.admin?
        # Do Nothing
      else
        @integration_status = "new"
        render "errors/error_404", status: :not_found
        # category = @design.categories.first
        # if category.present?
        #   redirect_to store_search_url(category.name), :notice => "We couldn't find the design you were looking for. Redirected to Category page"
        # else
        #   redirect_to designer_url(@designer), :notice => "We could not find the design you were looking for. Redirected to designer's boutique."
        # end
        return
      end
    end
    country_code = Design.country_code
    RequestStore.cache_preload "global_discount_on_amount_#{country_code}", "global_price_percent_#{country_code}", "sale_discount_country_#{country_code}", :discount_category, :promotions_categories_ids, "category_discount_country_#{country_code}", "category_wise_global_price_percent_#{country_code}","deal_end_global_category_#{country_code}", "desktop_design_free_stitching_active_for_#{Design.country_code}?"
    RequestStore.cache_preload 'unbxd_container_active_Design Details > Right Side Vertical Container','load_unbxd_container_Design Details > Right Side Vertical Container','unbxd_container_active_Design Details > Bottom Up Horizontal Container','load_unbxd_container_Design Details > Bottom Up Horizontal Container','unbxd_container_active_Design Details > Bottom Horizontal Container','load_unbxd_container_Design Details > Bottom Horizontal Container'
    @privileged_user = @design.design_designer?(current_account)
    @super_user = current_account.present? && current_account.admin?
    if rating_activated?(action_path_merge(params, 'view', 'rating_ui')) || @super_user
      RequestStore.cache_preload "designer_#{@designer.id}_reviews_count"
      reviews = @design.approved_reviews
      review_with_comments = reviews.comments
      @reviews_list_count = review_with_comments.size
      @reviews_list =  review_with_comments.includes(:user).top_rated.paginate(:page => params[:page], :per_page => 10)
      @star_percentage,@star_rating = Design.reivew_percentage(reviews,@design.total_review)
      @designer_rating = RequestStore.cache_fetch("designer_#{@designer.id}_reviews_count", expires_in: 24.hours) do
        review = @designer.reviews.select('count(case when rating >= 4 then id end) as positive, count(id) as total').nps[0]
        review.try(:total).to_i > 0 ? "#{((review.positive.to_f/review.total.to_i) *100).to_i}% Positive #{'review'.pluralize(((review.positive.to_i/review.total.to_i) *100).to_i)} (of #{review.total.to_i})" : 'No reviews yet'
      end
    end
    ActiveRecord::Associations::Preloader.new.preload(@design, [:variants => [:option_types => :option_type_values]])
    ActiveRecord::Associations::Preloader.new.preload(@design, :dynamic_size_charts) if @design.variants.present?
    #@offer_message = Promotions.offer_message
    if account_signed_in? && current_account.user?
      @review = Review.find_by_user_id_and_design_id(current_account.accountable_id,@design.id)
      @user = current_account.user
      @address = @user.default_address
    end

    # Temp Removed
    # if session[:viewing_history].present? && !session[:viewing_history].include?(@design.id)
    #   Design.increment_counter(:clicks, @design.id)
    # end

    # session[:contexio_ex] = rand(1..2) == 1 ? 'on' : 'off' if session[:contexio_ex].blank?
    @history                = promise{ update_recently_viewed() }
    @vacation_mode_on       = @designer.vacation_mode_on?
    # @wholesale              = Wholesale.new
    @category_first = @design.categories[0]
    @category_names   = promise {@category_first.try(:fetch_connected_category_names) || []}
    @discount_percent = promise {@design.effective_discount_percent}
    @effective_price  = promise {@design.effective_price}
    @designer_top_designs = promise {@designer.designs.preload(:images).limit(3)}
    @sibling_designs = @design.sibling_designs.published.preload(:designer, :master_img)
    if @design.in_stock? && !@designer.inactive_designer_state? && @design.check_quantity
      @pair_products = @design.in_stock_addon_products.includes(:designer,:master_img,variants: [option_types: :option_type_values]).limit(3).each do |design|
        design.pair_product_discount = design.addon_discount_percent.to_i
      end
    end
    if @design.buy_get_free == 1
      @bmgnx_hash = PromotionPipeLine.bmgnx_hash
    end
    unless @bmgnx_hash.present?
      @qpm_list = QuantityDiscountPromotion.current_active_promotions
      @qpm_messages = @qpm_list.collect(&:design_message).join(', ')
    end
    @breadcrumb = Breadcrumb.new(:design_show, category: @category_first, design: @design)
    # if @privileged_user || present == false
    #   include_rel   = [ :variants => [:option_types => :option_type_values], :property_values => :property]
    #   @design       = Design.includes(include_rel).where(:id => @design.id).first
    #   categories    = @design.categories
    #   unless categories.present? && @master_image.present?
    #     redirect_to designer_url(@designer), :notice => notice
    #     return
    #   end
    #   @coupons          = @designer.coupons.live
    #   @similar_designs  = [] #similar_designs(@design)
    # end
    @addon_types = promise { STITCHING_ENABLE == 'true' ? @design.get_design_addons(@actual_country, @symbol).to_a : [] }
    respond_to do |format|
      format.html # show.html.erb
      #format.json { render json: @design }
    end
  end

  def similar
    begin
      @design = @designer.designs.includes([:images,:dynamic_price_for_current_country]).find(params[:design_id])
      if (cached_designs = Rails.cache.fetch('similar_designs_id_' + @design.id.to_s)).present?
        @designs = Design.where(:id => cached_designs).includes([:designer, :images, :categories,:dynamic_price_for_current_country, :variants])
      else
        begin
          @similar_designs = Sunspot.more_like_this(@design) do
            fields :category_name, :designer_name, :title, :description, :specification
            with(:state, 'in_stock')
            with(:average_rating, 4..5)
            with(:cluster_winner, 1)
            paginate :page => params[:page], :per_page => 40
          end
          ids = @similar_designs.raw_results.collect{|rr| rr.primary_key.to_i}
          @designs = Design.where(:id => ids).includes([:designer, :images, :categories, :variants])
          Rails.cache.write('similar_designs_id_' + @design.id.to_s, ids, :expires_in => 24.hours)
        rescue Errno::ECONNREFUSED
          @designs = nil
        #@designs = @designer.designs.paginate :page => params[:page], :per_page => 40
        end
      end
      respond_to do |format|
        format.html
      end
    rescue
      redirect_to designer_url(@designer)
    end
  end

  # GET /designs/new
  # GET /designs/new.json
  def new
    @design = Design.new
    # @design.designable = (is_app_request? ? Saree.new : @designable.constantize.new) rescue Other.new
    gon.max_uploadable_categories = Design.get_max_uploadable_categories
    @design.images.build
    @design.designable = @designable.constantize.new
    properties = {}
    if is_app_request?      
      @all_property_value_list.slice(*VENDOR_APP_SELECTIVE_PROPERTIES[@designable][0]).each do |k,v|
        properties[k.titleize] = v.collect{|name, id| {id: id, name: name.titleize}}
      end
    end
    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: {design: @design, valid_categories: @design.get_valid_categories.collect{|c| {id: c.id, name: c.name.titleize} if c.show_to_vendor? }.compact, valid_properties: properties, designbale_package_details: VENDOR_APP_SELECTIVE_PROPERTIES[@designable][1]}}
    end
  end

  def get_option_type
    if (category = Category.find_by(id: params[:category_id])).present?
      @option_types = category.option_type.option_type_values if category.option_type
      @design = Design.new
      @design.variants.build
    end
    respond_to do |format|
      format.js
      format.json { render json: { option_type_values: @option_types}}
    end
  end

  def create_designable
    design = Design.find params['design']
    design.modified_design_data = true
    design.updated_design_details! if design.state == 'in_stock'
    designable = design.designable
    h = {}
    designable.attributes.each{|d| h.merge!(d.first=> params[d.first])  if params.has_key?d.first }
    designable.update_attributes h
    redirect_to :back
  end

  def get_designable
    # categories = MANUL_LIST_DESIGNABLE.map{|i| [i.titleize,i.underscore]}
    render json: {categories: MANUL_LIST_DESIGNABLE, status: 'ok'}
  end

  # GET /designs/1/edit
  def edit
    @design = Design.preload(:option_type_values).find(params[:id])
    @property_values = @design.property_values.preload(:property).order(:id)
    @all_property_value_list = promise{Property.get_property_values_list}
    @designable = @design.designable if @design.designable.present?
    gon.designer_id = @design.designer_id
    #Pull Design Addons
    design_addon_values = @design.addon_type_values

    #Pulling Designer Default Master Addons Values
    columns = 'addon_type_values.id as atv_id, addon_types.id as at_id, addon_type_values.name as atv_name, addon_types.name as at_name,master_addons.price as price,
    addon_type_values.position as atv_position,categories.name as category_name, master_addons.prod_time as prod_time'
    @master_addons = [] #Designer.joins(:master_addons => [:addon_type, :addon_type_value, :category]).where(:id => @design.designer_id).select(columns)
    @master_addons.each_with_index do |value, key|
      design_addon_values.each do |addon_value|
        # Checking if master addon is already present in form of design addon
        # If Yes then take attr values from design addon
        if addon_value.master_id == value[:atv_id].to_i
          @master_addons[key][:prod_time] = addon_value.prod_time
          @master_addons[key][:price] = addon_value.price
          # state says that this value is already created
          @master_addons[key][:state] = true
          @master_addons[key][:atv_id] = addon_value.id
          @master_addons[key][:published] = addon_value.published
        end
      end
    end
    @master_addons=@master_addons.group_by(&:at_name)

    #Pulling All OptionTypes With Category
    @option_types = promise{OptionType.where(category_id: @design.categories[0].try(:cached_self_and_ancestors_id))[0].try(:option_type_values).try(:sort_by,&:position)}
  end

  # POST /designs
  # POST /designs.json
  def create
    # if is_app_request?
    #   new_designable = Saree.new()
    #   source = 'app'
    # else
    new_designable = designable_for_design
    source = is_app_request? ? 'app' : 'web'
    # end

    @design = @designer.designs.build(params[:design])
    @design.designable = new_designable
    @design.version = new_designable.class.name.to_sym
    @design.validate_category = true
    @design.validate_variants = true
    @design.validate_property_values = source == 'web'
    @design.published = true
    @design.eta = 0

    @design.sell_count = 0
    @design.grade = 0
    @design.price = @design.price(RETURN_NORMAL).to_i
    @design.state = "review"

    # params.each do |p|
    #   if(p[0] =~ /image_(.*)/)
    #     @design.images << Image.find(p[1].strip)
    #   end
    # end
    respond_to do |format|
      if @design.save
        if params['addon'].present?
          @design.add_addons(params['addon'])
        end

        # Design.sidekiq_delay_until(Time.now + 1.minute)
        #       .make_design_live(@design)
        SidekiqDelayGenericJob.perform_in(Time.now + 1.minute, "Design", nil, "make_design_live", {@design.class.to_s => @design.id})

        if params[:post_to_facebook] && current_account.designer? && current_account.designer.fb_page_token?
          if current_account.designer.last_fb_post_at?
            timestamp = current_account.designer.last_fb_post_at.advance(:hours => 1)
          else
            timestamp = DateTime.now.advance(:minutes => 30)
          end

          current_account.designer.last_fb_post_at = timestamp
          current_account.designer.save

          url = designer_design_url(current_account.designer, @design)
          # DesignsController.sidekiq_delay_until(timestamp)
          #                  .post_design_to_facebook_page(
          #                    @design,
          #                    current_account.designer.fb_page_token, 
          #                    url
          #                  )
          SidekiqDelayGenericJob.perform_in(timestamp, 
                                            "DesignsController", 
                                            nil,
                                            "post_design_to_facebook_page",
                                            {@design.class.to_s => @design.id},
                                            current_account.designer.fb_page_token,
                                            url
                                          )
        end
        format.html { redirect_to designer_url(@designer), notice: 'Your design was successfully uploaded. It will be live in few mins.' }
        format.json {render json: {status: 200}}
      else
        # @designs = @designer.designs.published.includes([:images]).paginate(:page => params[:page], :per_page => 30)

        # Pulling All OptionTypes With Category
        # columns = 'option_type_values.*, option_types.name as ot_name, categories.id as cat_id'
        # @option_types = OptionTypeValue.joins(option_type: :category).select(columns).order('option_type_values.position ASC').group_by(&:ot_name)
        @design.images.build
        format.html { render :new }
        format.json { render json: {errors: @design.try(:errors).try(:full_messages) || 'Something Went wrong !'}}
      end
    end
  end

  # PUT /designs/1
  # PUT /designs/1.json
  def update
    @design = Design.preload(:option_type_values).find(params[:id])
    can_edit = current_account.can_edit_price?
    redirect_to edit_designer_design_path(@design.designer, @design), notice: "You are not allowed to change price in promotion period." and return unless Design.can_edit_price(params[:design], @design, can_edit)
    @property_values = @design.property_values.preload(:property).order(:id)
    @all_property_value_list = promise{Property.get_property_values_list}
    @design.validate_category, update_winner = true, false
    category_ids = @design.category_ids.uniq.sort
    if category_ids != params[:design][:category_ids].to_a.reject(&:blank?).map(&:to_i).sort
      if params[:design][:category_ids].present? && params[:design][:category_ids].length > (max_uploadable_categories = Design.get_max_uploadable_categories)
        @design.errors.add(:base, "You cannot add more than #{max_uploadable_categories} categories")
        params[:design][:category_ids] = category_ids
        respond_to do |format|
          format.html do
            render :edit
            return
          end
          format.json do
            render json: { 
              json: @design.errors,
              head: :unprocessable_entity 
            }
            return
          end
        end
      end
      @design.image_prcess_for_log(category_ids)
      update_winner = true
    end
    changed_property_ids = params[:design][:property_value_ids].to_a.reject(&:blank?).map(&:to_i).sort
    update_winner = true if @property_values.collect(&:id) != changed_property_ids
    color_pv_ids = Property.get_color_properties
    params[:design][:color] = '' if (color = @property_values.find{|pv| color_pv_ids.include?(pv.property_id)}.try(:id)).present? && changed_property_ids.exclude?(color)
    present_option_type_value_ids = @design.option_type_values.map(&:id).uniq
    if params[:design].present? && params[:design][:variants_attributes].present?
      params[:design][:variants_attributes].reject!{|k,v| v[:id].blank? && present_option_type_value_ids.include?(v['option_type_value_ids'].to_i)}
      params[:design][:designer_quantity] = params[:design][:variants_attributes].collect{|k,v| v[:designer_quantity].to_i}.sum if @design.variants.present?
    end
    respond_to do |format|
      params[:design].delete(:eta) if @design.ready_to_ship || ALLOWED_VENDOR_FOR_PREPARATION.exclude?(@design.designer_id) || !current_account.admin? || ['senior_vendor_team', 'super_admin'].exclude?(current_account.role.try(:name))
      # if params['addon'].present?
      #   @design.update_addons(params['addon'])
      # end
      @design.quantity = params[:design][:designer_quantity].to_i if params[:design][:designer_quantity].present?
      params[:design][:tag_list] = params[:design][:tag_list].split if params[:design][:tag_list].present?
      if (@design.going_to_sold_out && @design.can_unavailable? && @design.assign_attributes(params[:design]) && @design.unavailable)|| @design.update_attributes(params[:design])
        if params[:design].present? && (image_attributes = params[:design][:images_attributes]).present? && !current_account.admin?
          image_attributes.each do |_,value|
            if value['photo'].present?
              @design.modified_design_data = true
              @design.updated_design_details! if @design.state == 'in_stock'
              break
            end
          end
        end
        if @design.variants.present?
          @design.save!
        end
        if update_winner && (dc = @design.design_cluster).present? && (winner_design = Design.select('id').find_by_id(dc.winner_design_id)).present?
          Sunspot.index winner_design
        end

        if params[:manage_boutique].present?
          format.html { redirect_to :back, notice: 'Design was successfully updated.' }
        else
          format.html { redirect_to designer_design_url, notice: 'Design was successfully updated.' }
        end
        format.json { head :ok }
      else
        design_updated = false
        #Pulling All OptionTypes With Category
        columns = 'option_type_values.*, option_types.name as ot_name, categories.id as cat_id'
        @option_types = promise{OptionType.where(category_id: @design.categories[0].try(:cached_self_and_ancestors_id))[0].try(:option_type_values).try(:sort_by,&:position)}

        format.html { render action: "edit" }
        format.json { render json: @design.errors, status: :unprocessable_entity }
      end
    end
  end

  def update_design_quantity
    @design = Design.find(params[:id])
    can_edit = current_account.can_edit_price?
    render :json => {:status => 'error', error_message: "You are not allowed to change price in promotion period."} and return unless Design.can_edit_price(params, @design, can_edit)
    if params[:quantity].present?
      @design.designer_quantity = params[:quantity]
    elsif params[:price].present?
      if @design.is_transfer_model?
        @design.transfer_price = params[:price]
      elsif params[:price] != @design[:price]
        @design.price = params[:price]
      end
    end
    @design.discount_percent = params[:discount_percent].to_i if params[:discount_percent].present?

    if (@design.going_to_sold_out && @design.can_unavailable?)
      @design.skip_after_save_callback = true
    end
    if (@design.going_to_sold_out && @design.unavailable) || @design.save
      render :json => {:status => 'ok', :state => @design.human_state_name,effective_price: @design.effective_price}
    else
      render :json => {:status => 'error', :errors => @design.errors, error_message: @design.errors.full_messages}
    end
  end

  # PUT /design/add_to_reqlist
  def add_to_reqlist
    if params[:design_id].present? && params[:email].present?
      req = Reqlist.where(:design => params[:design_id], :email => params[:email])
      if req.blank?
        @req_list = Reqlist.new
        @req_list.design = params[:design_id]
        @req_list.email = params[:email]
        @req_list.save
      end
    end

    if @req_list && @req_list.errors.present?
      render :json => {:status => 'error', :errors => @req_list.errors}
    else
      render :json => {:status => 'Ok'}
    end
  end

  # Please. Please. Please. Don't delete designs.
  # Just archive it. So that if line items are referencing it
  # it still works.
  def destroy
    @design = Design.find(params[:id])
    @design.destroy
    respond_to do |format|
      format.html { redirect_to designer_url(@designer) }
      format.json { head :ok }
    end
  end

  def post_to_facebook
    if current_account.designer? && current_account.designer.fb_page_token?
      if current_account.designer.last_fb_post_at?
        timestamp = current_account.designer.last_fb_post_at.advance(:hours => 1)
      else
        timestamp = DateTime.now
      end

      current_account.designer.last_fb_post_at = timestamp
      current_account.designer.save

      @design = Design.find(params[:design_id])
      url = designer_design_url(current_account.designer, @design)
      # DesignsController.sidekiq_delay_until(timestamp)
      #                  .post_design_to_facebook_page(
      #                    @design,
      #                    current_account.designer.fb_page_token,
      #                    url
      #                  )
      SidekiqDelayGenericJob.perform_in(timestamp, 
                                        "DesignsController", 
                                        nil,
                                        "post_design_to_facebook_page",
                                        {@design.class.to_s => @design.id},
                                        current_account.designer.fb_page_token,
                                        url
                                      )
      redirect_to :back, :notice => "#{@design.title} will be posted at #{timestamp}."
      return
    end
    redirect_to :back, :notice => "Design wasn't posted."
  end

  def self.post_design_to_facebook_page(design, token, url)
    begin
      if design && design.master_image.photo_processing == false
        page_graph = Koala::Facebook::API.new(token)
        image_url = design.master_image.photo.url(:original)
        page_graph.put_picture(image_url, { "message" => design.title + " \n " + url + "\n" +"Rs. " + design.effective_price.to_s })
      end
    rescue Koala::Facebook::APIError => e
      logger.info e.to_s
    end
  end

  def remove_variants
    errors = false
    error_text = ''
    if (design = Design.where(:id => params[:design_id]).first).present?
      if params[:variant_id].present?
        if (v = design.variants.where(id: params[:variant_id]).first).present? && v.line_items.count == 0
          v.update_attributes(show_variant: false)
          msg = "Size #{v.option_type_values.try(:first).try(:name)} removed successfully"
          render json: {errors: errors, error_text: error_text, message: msg } and return
        else
          render json: {errors: true, error_text: 'Variant cannot be deleted as orders are present'} and return
        end
      else
        is_order_present_on_variant = false
        design.variants.each do |v|
          v.line_items.count == 0 ? v.show_variant = FALSE : is_order_present_on_variant = true
        end
        response = is_order_present_on_variant ? {errors: true, error_text: 'Some variant cannot be deleted as orders are present' } : { errors: errors, error_text: error_text,  message: 'All variants/size options removed'}
      end
      design.save
      render json: response
    else
      render json: {errors: 'design not found'}
    end
  end

  def change_master_image
    image = Image.where(:id => params[:image_id]).first
    design = image.design
    design.images.update_all(:kind => '')
    image.kind = 'master'
    image.save
    render :nothing => true
  end

  def bulk_upload_design
    design_params                      = params[:design].clone
    design_params[:category_ids]       = params[:category_ids].split(',').map(&:to_i)
    design_params[:property_value_ids] = params[:property_value_ids].split(',').map(&:to_i) if params[:property_value_ids].present?
    design_params[:image_ids]          = params[:image_ids].split(',').map(&:to_i) if params[:image_ids].present?
    design_params[:discount_percent]   = TRANSFER_MODEL_DISCOUNT if @designer.is_transfer_model?
    designable = ''
    if design_params[:id].present? && params[:bulk_upload_update]
      unless @designer.is_unicommerce_vendor
        begin
          design = @designer.designs.find(design_params[:id])
          authorize! :edit,design
          design.validate_category = true
          if design_params[:designable_attributes]
            key = design_params[:designable_attributes][:type]
            design_params[:designable_attributes].delete(:type)
            if design.designable.present?
              design.designable.update_attributes(design_params[:designable_attributes])
            else
              designable_types = 'SalwarKameez', 'Lehenga', 'Saree', 'Jewellery', 'Other', 'Bag', 'HomeDecor', 'Kurti', 'Jacket', 'Turban', 'Kurta', 'Consumable', 'Islamic', 'Man', 'Kid', 'MusicalInstrument'
              designable = key.constantize.create(design_params[:designable_attributes]) if designable_types.include?(key)
            end
            design_params.delete('designable_attributes')
          end
          if design_params[:addon_type_values_attributes].present?
            # design.add_addons(design_params[:addon_type_values_attributes])
            design_params.delete('addon_type_values_attributes')
          end

          if design_params[:variants_attributes] && design.variants.present?
            all_variant_details = design_params['variants_attributes'].collect{|_,v| [v['option_type_value_ids'],[v['designer_quantity'],v['design_code'],v['price']]]}.to_h
            all_variants = design.variants.includes(:option_type_values).where('option_type_values.id IN (?)',all_variant_details.keys.compact).references(:option_type_values)
            all_variants.each do |variant|
              if (size = all_variant_details[variant.option_type_values.try(:first).try(:id).to_s]).present?
                variant.designer_quantity = size[0]
                price_columns = @designer.is_transfer_model? ? {transfer_price: size[2].to_i > 0 ? size[2].to_i : variant[:price]} : {price: size[2].to_i > 0 ? size[2].to_i : variant[:price]}
                variant.update_attributes({quantity: variant.designer_quantity, design_code: (size[1].presence || variant.design_code)}.merge(price_columns))
              end
            end
            design_params.delete('variants_attributes')
          else
            design.designer_quantity = design_params['quantity']
            design_params['quantity'] = design.quantity
          end

          design_params.delete('product_id')
          if design.update_attributes(design_params)
            # design.state      = "processing"
            # design.designable = designable if designable.present?
            if design.images.present? && design_params[:image_ids].present?
              s = design.images.first
              s.kind = "master"
              s.save!
            end

            # design.check_for_free_addons(design.addon_type_values)
            # Design.sidekiq_delay_until(Time.now + 1.minute)
            #       .make_design_live(design)
            SidekiqDelayGenericJob.perform_in(Time.now + 1.minute ,"Design", nil, "make_design_live", {design.class.to_s => design.id})

            render :json => {:url => designer_design_url(@designer, design)}
          else
            render :json => {:errors => design.errors}
          end
        rescue => error
          render :json => {:error => error}
        end
      else
        render json: {error: 'Quantity Updation is blocked for Unicommerce Vendors'}
      end
    else
      begin
        if design_params[:designable_attributes]
          key = design_params[:designable_attributes][:type]
          design_params[:designable_attributes].delete(:type)
          designable_types = 'SalwarKameez', 'Lehenga', 'Saree', 'Jewellery', 'Other', 'Bag', 'HomeDecor', 'Kurti', 'Jacket', 'Turban', 'Kurta', 'Consumable', 'Islamic', 'Man', 'Kid', 'MusicalInstrument'
          designable = key.constantize.create(design_params[:designable_attributes]) if designable_types.include?(key)
        end

        design_params.delete('designable_attributes')
        if design_params[:addon_type_values_attributes].present?
          addon_type_values_attributes = design_params[:addon_type_values_attributes]
          design_params.delete('addon_type_values_attributes')
        end
        design            = @designer.designs.build(design_params)
        authorize! :edit,design
        ActiveRecord::Associations::Preloader.new.preload(design,property_values: :property)
        design.validate_category = true
        if designable.present?
          design.designable = designable
          design.version = design_params[:version].try(:to_sym).presence || designable.class.name.to_sym
          design.validate_property_values = true
        end
        design.validate_variants = true
        design.sell_count = 0
        design.grade      = 0
        design.state      = "processing"
        codes = (design.variants.present? ? design.variants.collect(&:design_code).reject(&:blank?).uniq : [])
        raise 'Variant SKU Code is already taken' if codes.present? && design.is_variant_design_code_uniq?(codes)
        if design.save
          # if addon_type_values_attributes.present?
          #   design.add_addons(addon_type_values_attributes)
          # end
          # Design.sidekiq_delay(Time.now + 1.minute).make_design_live(design)
          SidekiqDelayGenericJob.perform_in(Time.now + 1.minute ,"Design", nil, "make_design_live", {design.class.to_s => design.id})
          
          render :json => {:url => designer_design_url(@designer, design)}
        else
          render :json => {:errors => design.errors}
        end
      rescue => error
        render json: {errors: error.to_s}
      end
    end
  end

  def get_line_items_count
    number_of_line_items = Rails.cache.fetch("line_item_count_for_design_#{params[:id]}") do
      # Temp change it to faker & disabled it due to performance issue
      #LineItem.where('created_at > ?', Time.now - 1.day).where(design_id:  params[:id]).count
      rand(3..10)
    end
    render json: {count: number_of_line_items}
  end

  def remove_from_all_catalog
      @design.all_catalog_remove
      render :json => {:status => 'ok'}
  end

  def remove_from_international_catalog
      @design.international_catalog_remove
      render :json => {:status => 'ok'}
  end

  def remove_from_domestic_catalog
      @design.domestic_catalog_remove
      render :json => {:status => 'ok'}
  end

  def more_designers
    if (@design = Design.where(id: params[:id].to_i).first)
      @integration_status = 'new'
      @similar_image_results = @design.get_similar_from_mongo
      if request.xhr?
        @similar_image_results = @similar_image_results[0..3]
        render file: '/designs/all_designers.js', content_type: 'text/javascript'
      end
    else
      redirect_to request.referer.present? ? request.referer : root_path, notice: 'No Designs found'
    end
  end

  def design_eta
    if params[:design_id].present? && (@design = Design.find_by_id(params[:design_id])).present?
      @designer = @design.designer
      if ESSENTIAL_DESIGNERS['designer_ids'].include?(@designer_id.to_s)  ## for futre start
        @eta = ESSENTIAL_DESIGNERS['max_eta']
      else                                                                ## for futre end
        @metro_eta = CITY_BASED_SHIP_TIME[:metro]
        @non_metro_eta = CITY_BASED_SHIP_TIME[:non_metro]
      end
    else
      @design_not_found = params[:design_id]
    end
    render layout: 'admin'
  end

  private

  def update_recently_viewed
    # Recently viewed designs
    session[:viewing_history] = [] unless session[:viewing_history].present?
    session[:viewing_history] << @design.id unless session[:viewing_history].include?(@design.id)
    if (history_size = session[:viewing_history].size) >= 100
      session[:viewing_history].slice!(0,history_size-100)
    end
    history = Design.where(:id => session[:viewing_history]).includes(:images, :designer)

    # save design browing history if user is logged in
    user = current_account.user.add_item_to_history(@design.id) if current_account && current_account.user?

    history
  end

  def similar_designs(design)
    id = design.id.to_s
    cached_name = 'similar_designs_id_' + id
    similar_design_ids = design.similar_designs if (similar_design_ids = Rails.cache.fetch(cached_name)).blank?
    if similar_design_ids.present?
      similar_designs = Design.where(:id => similar_design_ids[0..6]).includes(:images, :designer)
      Rails.cache.write('similar_designs_id_' + id, similar_design_ids)
    end
    similar_designs
  end

  def design_show_set_required_variables
    country_footer = ((['in','ca','gb'].exclude? @country_code.to_s.downcase) ? 'int' : @country_code.to_s.downcase)
    country_code = Design.country_code
    RequestStore.cache_preload "stitching_offer_#{country_code}", "offer_message_#{country_code}", :top_searches_unbxd_data, "additional_discount_#{country_code}", "bmgnx_hash_#{country_code}", :promotion_pipeline_method_variable_hash, "free_stitching_#{country_code}", "/layouts/static_footer_red/static_footer_#{country_footer}", 'load_unbxd_container_Cart Page > Bottom container'

    # Handle both URL patterns: /designs/:design_id and /designers/:designer_id/designs/:design_id
    if params[:designer_id].present?
      # Traditional URL pattern with designer
      @design = @designer.designs.find(params[:id])
    else
      # New simplified URL pattern
      @design = Design.find_by_cached_slug!(params[:design_id])
      @designer = @design.designer
    end

    category_first = @design.categories[0]
    @category = category_first.try(:name)
    categories_id = @design.categories.collect(&:id)
    @is_anarkali = (@design.designable_type == 'SalwarKameez' && (ANARKALI_CATEGORY_FOR_STANDARD_ADDON & categories_id).present?) ? :anarkali : :salwar_kameez
    gon.anarkali_standard_sugguestion = Design::HEIGHT_RANGE_FOR_STANDARD_ADDON[@is_anarkali]
    @seo = SeoList.where(:label => @category).first
    @master_image = @design.master_image
    @integration_status = "new"
    @og = true
    if @design.description?
      @seo_description = @design.description
    else
      @seo_description = 'I found this beautiful design on Mirraw.com. Visit Mirraw to checkout more such amazing designs. Mirraw brings handpicked designs from designers across the India. Click here to ...'
    end
    @og_title = "#{@designer.name} - #{@design.title}"
    @og_image = @master_image.photo.url(:small) if @master_image.present?
    @og_url = @design.canonical_url
    @canonical_path = @design.canonical_path
    @og_type = Mirraw::Application.config.fb_namespace + ':design'
    if @seo.present?
      @seo_keywords = @seo.keyword
    end
    @seo_title = "#{@design.title} - #{@designer.name} - #{@design.id}"
    gtm_data_layer.push({pageType: 'product', productId: @design.id})
    if @country_code == 'IN'
      gon.india_delivery_date = get_india_delivery_date(@design, @designer, @delivery_city)
    end
  end

  def get_india_delivery_date(design, designer,delivery_city)
    if is_essential_design(designer)
      eta = ESSENTIAL_DESIGNERS['max_eta'].to_i
    else
      if delivery_city.present? && !DISABLE_ADMIN_FUCTIONALITY['pdd_from_cp']
        click_post = ClickPostAutomation.new()
        pickup_pincode = design.designer.business_pincode.presence || design.designer.pincode
        eta = click_post.get_eta_from_clickpost(delivery_city, pickup_pincode).to_i
        eta = eta > 0 ? eta + design.eta.to_i + design.designer.vacation_days_count.to_i : 0
      end
      if eta.to_i == 0
        if delivery_city.present? && design.designer.designer_type == 'Tier 1 Designer'
          eta = design.warehouse_shipping_time('india', delivery_city)
        elsif delivery_city.present?     
          eta = design.designer_shipping_time('india', delivery_city) 
        else
          if(logged_user = current_account.try(:user)).present? && logged_user.class == User && (default_address = logged_user.addresses.where(default: 1).last).present?
            eta = design.designer_shipping_time('india', default_address.city.to_s.downcase) 
          else
            pickup_location = design.sor_available? ? 'bhiwandi' : design.designer.pickup_location.try(:downcase)
            eta = Lane.get_max_range_eta_from_lane(pickup_location)
          end
          eta = design.shipping_time if eta.to_i == 0 
        end
      end
    end
    eta += (DESIGNER_LSR.hours.to_f/1.days).round.to_i unless design.sor_available?
    eta += Time.now.advance(days:eta).sunday? ? 1 : 0
    Time.now.advance(days:eta).strftime('%d %b %Y')
  end

  def is_essential_design(designer)
    ESSENTIAL_DESIGNERS['designer_ids'].include?(designer.id.to_s)
  end

  def set_max_uploadable_categories
    gon.max_uploadable_categories = Design.get_max_uploadable_categories
  end

  def required_param_for_new_design
    @designable = params[:type].try(:camelize)
    unless @designable && (BulkUpload.meta_data.keys + [:Other]).include?(@designable.to_sym)
      return redirect_to(designer_url(@designer), notice: 'please select Design type.')
    end
    @property_values = []
    @all_property_value_list = Property.get_property_values_list
    if (category_id = DESIGNABLE_PARENT_CATEGORY_MAPPING.is_a?(Hash) && DESIGNABLE_PARENT_CATEGORY_MAPPING[@designable]).present?
      @all_property_value_list = CategoriesPropertyValue.get_valid_property_values(category_id, @all_property_value_list.slice(*BulkUpload.meta_data[@designable.to_sym][:designs_property_values].map(&:to_s)))
    end
    gon.designer_id = @designer.id
    gon.stitched_property_id = @all_property_value_list['stitching'].try(:[],'stitched')
  end

  def designable_for_design
    designable = @designable.constantize.new(params[:design].require(:designable).permit!)
    designable
  rescue
  end    

  protected

  def get_designer
    # friendlyid rewrites ids to urls
    @designer = Designer.find(params[:designer_id])

    unless @designer
      redirect_to root_path, :notice => 'You need to sign in first to create new account.'
    end
  end

  include RatingHelper
end
