class MirrawAdmin::OrdersController < MirrawAdmin::BaseController
    before_action :authenticate_user
    def cod_orders_state_change

    end

    def cod_orders_state_update
        failed_orders = {}
        order_numbers = params[:order_numbers].split(',')
        order_numbers.each do |order_number|
          order = Order.find_by_number(order_number)
          if order.present?
            if order.state == 'confirmed'
              order.state = 'sane'
              order.save
              flash[:notice] = "Order state has been updated"
            else
              failed_orders[order_number] = "Order state not in confirmed"
              flash[:notice] = "Order state not in confirmed"
            end
          else
            failed_orders[order_number] = "Order does not exist"
            flash[:notice] = "Order does not exist"
          end
        end
        if failed_orders.present?
          MirrawAdminMailer.failed_order_notification(failed_orders).deliver_now
        end
        redirect_to cod_orders_state_change_mirraw_admin_orders_path
    end

    def list_kyc_documents
      @kyc_documents = KycDocument.order(created_at: :desc).paginate(page: params[:page], per_page: 15)
    end

    private
    def authenticate_user
        unless ACCESSIBLE_EMAIL_ID['cod_orders_panel'].to_a.include?(current_account.try(:email))
            redirect_to root_path, notice: 'You are not authorized to access this page.'
        end
    end
end