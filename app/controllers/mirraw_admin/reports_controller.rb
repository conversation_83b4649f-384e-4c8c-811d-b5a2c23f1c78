class MirrawAdmin::ReportsController < MirrawAdmin::BaseController
  def best_sellers
    best_seller = BestsellerService.new(params[:start_date],params[:end_date])
    @distinct_designable_types = best_seller.get_designable_type
    @state = best_seller.get_design_states
    @data = best_seller.get_best_sellers_with_durations
    @data = best_seller.get_best_sellers_with_durations(params[:geo]) if  params[:geo].present?
    @data = @data.select { |item| params[:design_state_dropdown].include?(item[10]) } if params[:design_state_dropdown].present?
    @data = @data.select { |item| item[3] == params[:designable_type] } if params[:designable_type].present?
    @data = @data.select { |item| item[0] == params[:designer_id].to_i } if params[:designer_id].present?
    @data = @data.select { |item| item[1].downcase == params[:designer_name].downcase } if params[:designer_name].present?
    @data = @data.select { |item| item[2] == params[:design_id].to_i } if params[:design_id].present?
    @data = @data.first(params[:top].to_i) if params[:top].present?
    if params[:download_report].present?
        csv_data = best_seller.get_bestsellers_report(@data,current_account)
        send_data csv_data, filename: "best_sellers_report_#{Time.now}.csv", type: "text/csv"
    end
    @total_count = @data.length
    @data = @data.paginate(page: params[:page], per_page: 50)
  end

  def shipconsole_orders
    @start_date = params[:start_time].presence && DateTime.parse(params[:start_time])
    @end_date   = params[:end_time].presence && DateTime.parse(params[:end_time])
    if @start_date && @end_date
      shipconsole_orders = ShipmentAllocation.joins(:order, line_item: :designer_order).where(created_at: @start_date..@end_date).where(master_tracking_number: nil).where.not(orders: { state: 'cancel' }).where(line_items: { status: nil }).where.not(designer_orders: { state: 'canceled' })
    
      if params[:download_report] && shipconsole_orders.present?
        csv_data = ShipconsoleCsvGenerator.new(shipconsole_orders).generate
        batch_uniq_number = "MIRRAW-#{Time.current.strftime("%Y%m%d-%H%M%S")}"
        shipconsole_orders.update_all(mirraw_uniq_number: batch_uniq_number)

        send_data csv_data, filename: "shipconsole_orders_#{@start_date}_to_#{@end_date}.csv"
      else 
        flash[:notice] ="No orders found"
      end
    end
  end

  def upload_shipconsole_tracking_details
    
  end

  def update_shipconsole_tracking
    csv_file = params[:csv_file]
    invoice_csv = params[:csv_file1]
    filename = "shipconsole_tracking-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
    if csv_file.present? && csv_file.content_type == 'text/csv' && invoice_csv.present? && invoice_csv.content_type == 'text/csv'
      begin
        csv_headers = CSV.open(csv_file.path, &:first) || []
        csv_headers = csv_headers.compact.map(&:strip)
        parsed_header = csv_headers.first.chomp(";").split(";")
        required_headers = [
          "ShipmentOption", "ref2", "tracking", "ref1", "Ponumber",
          "OrderID", "PKGType", "DeclaredVal", "DeclaredValAmount",
          "CountryOrTerritoryOfOrigin", "PKGDimUOM", "PCKGBoxNo",
          "PKGHeight", "PKGWidth", "PKGLength", "Weight", "unitofmeasure"
        ]
        missing_headers = required_headers - parsed_header
        if missing_headers.any?
          flash[:alert] = "Error: Missing required headers: #{missing_headers.to_a.join(', ')}"
          redirect_to upload_shipconsole_tracking_details_mirraw_admin_reports_path and return
        end

        directories = AwsOperations.get_directory(bucket: 'ticket-images-new', new_connection: true)
        fog_object = directories.files.create(
          key: filename,
          body: csv_file.read,
          public: false
        )
        csv_file_path = fog_object.url(Time.now + 3600)
        UpdateShipconsoleTrackingJob.perform_async(current_account.email, csv_file_path) if csv_file_path.present?

        if invoice_csv.present?
          e_invoice_filename = "shipconsole_invoice-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
          directories = AwsOperations.get_directory(bucket: 'ticket-images-new', new_connection: true)
          fog_object = directories.files.create(
            key: e_invoice_filename,
            body: invoice_csv.read,
            public: false
          )
          e_invoice_csv_file_path = fog_object.url(Time.now + 3600)

          ShipconsoleInvoiceJob.perform_async(current_account.email, e_invoice_csv_file_path) if e_invoice_csv_file_path.present?
        end
        
        flash[:success] = "CSV file Uploaded successfully for designs. It will take some time to reflect the changes"
        redirect_to upload_shipconsole_tracking_details_mirraw_admin_reports_path
      rescue => exception
        flash[:notice] = "Please upload a valid CSV file."
        redirect_to upload_shipconsole_tracking_details_mirraw_admin_reports_path
      end
    else
      flash[:notice] = "Please upload a valid CSV file."
      redirect_to upload_shipconsole_tracking_details_mirraw_admin_reports_path
    end
  end

end
