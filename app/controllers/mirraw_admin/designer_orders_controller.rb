class MirrawAdmin::DesignerOrdersController < MirrawAdmin::BaseController
  before_filter :initialize_click_post_object, :only =>['get_payload']
    def initialize_click_post_object
        @click_post_obj = ClickPostAutomation.new
    end
    
    def payload
        @shippers = Shipper.all
    end

    def get_payload(dos,order,dos_ids=[],bulk_dispatch=false,is_rtv=false,av_shipper)
        designer = dos.designer
          if bulk_dispatch
            is_cod = false
            reference = "#{designer.id} - #{dos.invoice_number}"
          else
            is_cod = order.try(:cod?) && !order.try(:international?)
            reference = "#{order.try(:number)} - #{dos.id}"
            if dos.pickedup? || dos.replacement_pending?
              reallocation_count = 1
              if (s = dos.shipment || dos.bulk_shipment).present?
                previous_reference = s.invoice_data['latest_reference'].presence || s.mirraw_reference
                reallocation_count = previous_reference.split('-')[2].to_i + 1 if previous_reference.present?
              end
              reference = "#{reference} - #{reallocation_count}"
            end
          end
          all_line_items = []
          pickup_info = designer.get_order_pickup_details(dos,order)
    
          if (international = (order.try(:international?) || dos.ship_to == 'mirraw' || bulk_dispatch))
            company_name, shipping_telephone, shipping_address_1,shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_shipping_address(dos.warehouse_address_id)
            drop_address = "#{shipping_address_1} #{shipping_address_2}"
            drop_phone = shipping_telephone.to_s
            drop_country =  'IN'
            drop_state = shipping_state
            drop_pincode = shipping_pincode
            drop_city = shipping_city
            drop_name = company_name
            all_line_items,invoice_items,shipment = Shipment.generate_international_invoice_details((bulk_dispatch ? dos_ids : dos.id),bulk_dispatch)
            invoice_data = bulk_dispatch ? Shipment.calculate_domestic_invoice(nil, invoice_items, false, nil, designer) : Shipment.calculate_domestic_invoice(order, invoice_items, false, dos)
          else
            drop_address = order.street
            drop_phone = order.phone.to_s
            drop_country = 'IN'
            drop_state = order.buyer_state
            drop_pincode = order.pincode
            drop_city = order.city
            drop_name = order.name
            invoice_items, total, cod_amount, cod_charge, invoice_data, shipment = Shipment.generate_domestic_invoice_details(dos)
          end
          all_items = []
          invoice_items.each do |item|
            all_items.push({sku: item[:sku], price: item[:total_price].round(2),quantity: item[:quantity], description: item[:name]})
          end
          payload =
          {
            pickup_info: {
              pickup_state: pickup_info[:state],
              pickup_address: pickup_info[:address].gsub(/\r/,' ').gsub(/\n/,' ').gsub(',',' '),
              email: pickup_info[:email],
              pickup_time: Time.now.hour < 13 ? (DateTime.now + 4.hours ).strftime('%Y-%m-%dT%H:%M:00Z') : Date.tomorrow.in_time_zone.change(hour: 11).strftime('%Y-%m-%dT%H:%M:00Z'),
              pickup_pincode: pickup_info[:pincode],
              pickup_city: pickup_info[:city],
              pickup_name: pickup_info[:name],
              pickup_country: pickup_info[:country_code],
              pickup_phone: pickup_info[:phone]
            },
            drop_info: {
              drop_address: drop_address.gsub(/\r/,' ').gsub(/\n/,' ').gsub(',',' '),
              drop_phone: drop_phone.to_s,
              drop_country: drop_country,
              drop_state: drop_state,
              drop_pincode: drop_pincode.to_s,
              drop_city: drop_city,
              drop_name: drop_name
            },
            shipment_details: {
              height: 12,
              order_type: is_cod ? "COD" : "PREPAID",
              invoice_value: invoice_data[:item_total].round(0),
              invoice_number: dos.invoice_number,
              invoice_date: Date.today.strftime('%Y-%m-%d'),
              reference_number: reference,
              length: 10,
              breadth: 10,
              weight: av_shipper.name == "WowExpress" ? 500 : 100,
              items: all_items,
              cod_value: is_cod ? invoice_data[:item_total].round(0) : 0,
              courier_partner: av_shipper.clickpost_shipper_id
            },
            gst_info: {
              seller_gstin: designer.gst_no.to_s,
              taxable_value: invoice_data[:total_taxable_value].round(2),
              is_seller_registered_under_gst: designer.gst_no.present?,
              place_of_supply: designer.business_state.presence.try(:upcase) || designer.state.try(:upcase),
              enterprise_gstin: MIRRAW_GST_NUMBER,
              gst_total_tax: invoice_data[:gst_total].round(2),
              invoice_reference: dos.invoice_number
            },
    
            additional: {
              label: true,
              return_info: {
                pincode: pickup_info[:pincode],
                address: pickup_info[:address],
                state: pickup_info[:state],
                phone: pickup_info[:phone],
                name: pickup_info[:name],
                city: pickup_info[:state],
                country: pickup_info[:country_code]
              },
              delivery_type: "FORWARD",
              async: false,
              gst_number: MIRRAW_GST_NUMBER
            }
          }
          if (account_code_name = @click_post_obj.get_account_code_for_shipper(av_shipper.name, international, dos)).present?
            payload[:additional].merge!(:account_code =>  account_code_name)
          end
          response = @click_post_obj.get_response_for(:create_shipment_path, payload).parsed_response.try(:deep_symbolize_keys)
          MirrawAdminMailer.send_payload_and_res(payload,response).deliver_now
      end

    def fetch_payload
        dos = DesignerOrder.find params[:designer_order_id].to_i
        av_shipper = Shipper.find params[:shipper_id].to_i
        order = dos.order
        payload = get_payload(dos,order,dos_ids=[],bulk_dispatch=false,is_rtv=false,av_shipper)
        redirect_to payload_mirraw_admin_clickposts_path
    end

    def bulk_state_mark_complete
    end
  
    def bulk_state_mark_complete_perform
      return redirect_to bulk_state_mark_complete_mirraw_admin_designer_orders_path, alert: "Please select the fields" unless (csv_file = params[:csv_file]).present? && params[:selected_ids].present?
      begin
        BulkStateMarkCompleteJob.perform_async(csv_file.read,params[:selected_ids])
        flash[:notice] = "Update status will be mailed soon !"
      rescue => e
        Rails.logger.error "Bulk designer order state mark failed: #{e.message}"
        return redirect_to bulk_state_mark_complete_mirraw_admin_designer_orders_path, alert: "Unexpected Error Occured :/"
      end
      redirect_to bulk_state_mark_complete_mirraw_admin_designer_orders_path      
    end
end