class DynamicPricesController < ApplicationController
  before_filter :authenticate_account!
  load_and_authorize_resource param_method: :my_sanitizer

    # GET /dynamic_prices/1/activate
  def activate
    #activates dynamic pricing
    design = Design.find(params[:design_id])
    design.update_column(:dynamic_pricing, true) unless design.dynamic_pricing?
    redirect_to show_dynamic_price_path(design)
  end

  # GET /dynamic_prices/1/activate
  def deactivate
    #deactivates dynamic pricing
    design = Design.find(params[:design_id])
    design.update_column(:dynamic_pricing, false) if design.dynamic_pricing?
    redirect_to dynamic_prices_path
  end

  def index
    @integration_status = 'new'
    #@designs=Design.where(dynamic_pricing: true).order("international_grade desc").paginate(page: params[:page], per_page: params[:items_per_page] || 10)
  end

  def show
    @design = Design.find(params[:design_id])
    @variant = Variant.find(params[:variant_id]) if params[:variant_id].present?
    @countries_hash = {}
    countries = Country.order(:priority)

    if @variant.present?
      @dynamic_p = @variant.dynamic_prices
      @dynamic_price = DynamicPrice.new(variant_id: @variant.id)
      @parent_object = @variant
      @return_path = show_variant_dynamic_price_path(@design, @variant)
    else
      @dynamic_p = @design.dynamic_prices
      @dynamic_price = DynamicPrice.new(design_id: @design.id)
      @parent_object = @design
      @return_path = show_dynamic_price_path(@design)
    end

    countries.each do |country|
      @countries_hash[country.id] = country.name
    end
  end

  def create
    design = Design.find(params[:design_id])
    variant = Variant.find(params[:variant_id]) if params[:variant_id].present?
    cid = params[:dynamic_price][:country_id].to_i

    if variant.present?
      variant.dynamic_prices.where(country_id: cid).first_or_create do |dp|
        dp.country_id = cid
      end
      redirect_to show_variant_dynamic_price_path(design, variant)
    else
      design.dynamic_prices.where(country_id: cid).first_or_create do |dp|
        dp.country_id = cid
      end
      redirect_to show_dynamic_price_path(design)
    end
  end

  def update
    design = Design.find(params[:design_id])
    variant = Variant.find(params[:variant_id]) if params[:variant_id].present?
    cid = params[:dynamic_price][:country].to_i
    pscale = params[:dynamic_price][:scale]

    if variant.present?
      dynamic_p = variant.dynamic_prices.where(country_id: cid).first_or_create do |dp|
        dp.country_id = cid
      end
      if(!pscale.nil?)
        dynamic_p.update_attributes(scale: pscale)
      end
      redirect_to show_variant_dynamic_price_path(design, variant)
    else
      dynamic_p = design.dynamic_prices.where(country_id: cid).first_or_create do |dp|
        dp.country_id = cid
      end
      if(!pscale.nil?)
        dynamic_p.update_attributes(scale: pscale)
      end
      redirect_to show_dynamic_price_path(design)
    end
  end

  def upload
    begin
      connection = Fog::Storage.new({
        :provider                 => 'AWS',
        :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
        :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
        :region                   => ENV['AWS_REGION']
      })

      directory = connection.directories.new(
        :key    => ENV['S3_BUCKET']
      )

      filename  = ENV['DYNAMIC_PRICE_UPLOAD_DIRECTORY'] + "#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"

      file =  directory.files.create(
        :key    => filename,
        :body   => params[:csv_file].read,
        :public => true
      )

      DynamicPrice.sidekiq_delay(queue: 'low')
                  .update_designs(filename,params[:remove])
      redirect_to :back, :notice => 'Dynamic Price will be enabled after 6 hour'
    rescue NoMethodError => error
      error = params[:csv_file].blank? ? 'CSV file not uploaded' : error.message
      redirect_to :back, :notice => error
    rescue => error
      redirect_to :back, :notice => error.message
    end
  end

  def my_sanitizer
    params.require(:dynamic_price).permit(:country_id, :variant_id)
  end
end