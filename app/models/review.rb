class Review < ActiveRecord::Base
  #attr_accessible :approved, :rating, :review, :design_id, :user_id, :designer_id, :system_user, :site_review, :assured,:order_id,:geo, :notes
  include SidekiqHandleAsynchronous  
  belongs_to :user
  belongs_to :designer
  belongs_to :design, touch: true
  belongs_to :order
  #has_one :designer_order, -> { where (object){ respond_to?(:designer_id) ? {designer_id: designer_id} : 'reviews.designer_id = designer_orders.designer_id'} }, through: :order,source: :designer_orders
  #has_one :line_item, -> { where (object){ respond_to?(:design_id) ? {design_id: design_id} : 'reviews.design_id = line_items.design_id'} },through: :order,source: :line_items
  has_one :designer_order, ->(object) { where(designer_id: object.designer_id) },through: :order,source: :designer_orders
  has_one :line_item, ->(object) { where(design_id: object.design_id) },through: :order,source: :line_items


  has_one :tailoring_info,through: :line_item

  has_many :survey_answers, :as => :surveyable
  extend DynamicTemplateMethod
  dynamic_template_fields :rating, :review, :approved, :created_at, :updated_at, :system_user
  has_and_belongs_to_many :orders

  #after_save :add_negative_review_event

  scope :site, -> {where(site_review: true)}
  scope :product, -> {where(site_review: [false, nil])}
  scope :comments, -> { where("review is not null and trim(review) <> ''") }
  scope :approved, -> { where(approved: true) }
  scope :nps, -> { where('order_id > 0') }
  scope :top_rated, -> {where('rating >= 4').order('updated_at desc')}
  scope :negative, -> {where('rating <= 3').order('updated_at desc')}
  scope :most_recent, -> {order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from updated_at) as float)) + ((5 - rating) * 100) asc")}

  def self.reivew_percentage(reviews)
    star_percentage=Array.new(6,0)
    star_rating=Array.new(6,0)
    total_review =reviews.count
    if total_review==0
        (1..5).each do |i|
          star_percentage[i]=0
      end
      return [star_percentage, star_rating, total_review]
    else
      (1..5).each do |i|
        star_rating[i]=reviews.where(rating: i).count
      end
      (1..5).each do|i|
        star_percentage[i]=((star_rating[i]*100)/total_review)
      end
      return [star_percentage, star_rating,total_review]
    end
  end

  def survey_answer_array(*ids)
    survey_answer_hash = survey_answers.group_by(&:question_id)
    ids.collect do |id|
      response = survey_answer_hash[id].try{|sa| sa.first.response}
      case response
      when true
        'Yes'
      when false
        'No'
      when nil
        '-'
      end
        
    end
  end

  def reviewer(review, user=nil)
    if review.user.present?
      text = review.user.first_and_last
    else
      text = 'Guest Review'
    end
    text
  end

  # def add_negative_review_event
  #   if rating <= 2
  #     designer_order.try(:order_quality_event!, :add, 'NegativeFeedback')
  #     line_item.try(:order_quality_event!, :add, 'NegativeFeedback')
  #   end
  # end

  def update_vendor_odr
    self.designer_order.try(:order_quality_event!, :remove, 'NegativeFeedback')
    self.line_item.try(:order_quality_event!, :remove, 'NegativeFeedback')
    self.line_item.try(:order_quality_event!, :add, 'PositiveFeedback')
    MetricDefinition.where(name: 'OrderDefectRate').each do |metric_definition|
      MetricValue.import *metric_definition.generate_data(Date.today, "designer_orders.designer_id = #{self.designer_id}"),
                             validate: false, on_duplicate_key_update: { conflict_target: [:actor_id, :actor_type, :metric_definition_id, :generated_on], columns: [:value, :from_date, :to_date, :denominator_count] } if self.designer.present?
    end
  end

  def self.rating_group_text
    group_count = group(:rating).count
    total = group_count.sum(&:last).to_f
    min_total = [total,1].max
    positive_count = group_count.slice(4,5).sum(&:last)
    neutral_count = group_count.slice(3).sum(&:last)
    negative_count = group_count.slice(1,2).sum(&:last)
    {
      total: total.to_i,
      positive: [(positive_count / min_total * 100).round(1), positive_count],
      neutral: [(neutral_count / min_total * 100).round(1), neutral_count],
      negative: [(negative_count / min_total * 100).round(1), negative_count],
    }
  end

  #handle_asynchronously :new_review

  def self.new_review(line_item_id, review_text: nil, rating: nil)
    line_item = LineItem.where(id: line_item_id).first
    order = line_item.order
    rating = rating.to_i
    if order && line_item && (review_text.present? || rating > 0)

      design = line_item.design
      designer = design.designer

      survey_answers = SurveyAnswer.preload(:survey_question).where(surveyable: line_item)
      issues = survey_answers.map{|ans| ans.survey_question.try(:qualify_as)}.uniq.join(', ')

      # ratings = survey_answers.select do |ans|
      #   [*'1'..'5'].include?(ans.answer)
      # end.collect(&:answer).map(&:to_i)
      # avg_rating = ratings.present? ? (ratings.sum / ratings.size.to_f) : 0

      finding_param = order.user_id.nil? ? {order_id: order.id} : {user_id: order.user_id}
      review = design.reviews.where(finding_param).first_or_initialize
      review.rating = 0 if review.rating.blank?
      review.rating = rating  if rating > 0
      review.review = review_text if review_text.present?
      review.assign_attributes(
        issue: issues,
        designer_id: designer.id,
        geo: order.geo,
        order_id: order.id
      )
      review.save!
      review.orders << order unless review.orders.include?(order)

      if (stitching_answers = survey_answers.select{|ans| SurveyQuestion::STITCHING_QUESTIONS.include?(ans.question_id)}).present?
        tag_name = stitching_answers.all?{|ans| ans.response} ? 'Good Stitching' : 'Bad Stitching'
        Order.tag_measurements(order.id,review.id,design.id,tag_name)
      end

      design.update_reviews
      designer.update_reviews
      designer.update_nps_review_score({param1: rating})
      unless rating > 3
        DesignerMailer.send_nps_feedback_to_designer(designer.id, design.id)
      end
    end
  end


  # handle_asynchronously :add_negative_review_event

  # def self.add_negative_review_event(*ids)
  #   where(id: ids).each(&:add_negative_review_event)
  # end
end
