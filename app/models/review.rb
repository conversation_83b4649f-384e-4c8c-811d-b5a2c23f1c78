class Review < ActiveRecord::Base
  #attr_accessible :approved, :rating, :review, :design_id, :user_id, :designer_id, :system_user, :site_review, :assured,:order_id,:geo, :notes
  include SidekiqHandleAsynchronous
  belongs_to :user
  belongs_to :designer
  belongs_to :design, touch: true
  belongs_to :order
  #has_one :designer_order, -> { where (object){ respond_to?(:designer_id) ? {designer_id: designer_id} : 'reviews.designer_id = designer_orders.designer_id'} }, through: :order,source: :designer_orders
  #has_one :line_item, -> { where (object){ respond_to?(:design_id) ? {design_id: design_id} : 'reviews.design_id = line_items.design_id'} },through: :order,source: :line_items
  has_one :designer_order, ->(object) { where(designer_id: object.designer_id) },through: :order,source: :designer_orders
  has_one :line_item, ->(object) { where(design_id: object.design_id) },through: :order,source: :line_items


  has_one :tailoring_info,through: :line_item

  has_many :survey_answers, :as => :surveyable
  extend DynamicTemplateMethod
  dynamic_template_fields :rating, :review, :approved, :created_at, :updated_at, :system_user
  has_and_belongs_to_many :orders

  #after_save :add_negative_review_event

  # Optimized basic scopes
  scope :site, -> { where(site_review: true) }
  scope :product, -> { where(site_review: [false, nil]) }
  scope :comments, -> { where('review IS NOT NULL AND LENGTH(TRIM(review)) > 0') }
  scope :approved, -> { where(approved: true) }
  scope :nps, -> { where('order_id > 0') }

  # Optimized rating scopes with better indexing support
  scope :top_rated, -> { where('rating >= ?', 4).order('updated_at DESC, rating DESC') }
  scope :negative, -> { where('rating <= ?', 3).order('updated_at DESC, rating DESC') }
  scope :rating_above, ->(rating) { where('rating > ?', rating) }
  scope :rating_between, ->(min, max) { where(rating: min..max) }

  # Optimized complex ordering scope with better performance
  scope :most_recent, -> {
    order(Arel.sql("(EXTRACT(EPOCH FROM (NOW() + INTERVAL '7 days')) - EXTRACT(EPOCH FROM updated_at)) + ((5 - rating) * 100) ASC"))
  }

  # Optimized association loading scopes
  scope :with_user, -> { preload(:user) }
  scope :with_design_details, -> { includes(:user, design: [:images, :designer]) }
  scope :with_order_details, -> { includes(:order, :user) }

  # Optimized design-related scopes
  scope :highly_rated_designs, -> {
    joins(:design)
      .where('reviews.rating > ? AND designs.total_review > ?', 4, 20)
      .references(:designs)
  }

  # Optimized ordering scopes
  scope :by_recent_rating, -> { order('updated_at DESC, rating DESC') }
  scope :by_rating_desc, -> { order('rating DESC, updated_at DESC') }
  scope :by_complex_formula, -> {
    order(Arel.sql("(EXTRACT(EPOCH FROM (NOW() + INTERVAL '7 days')) - EXTRACT(EPOCH FROM updated_at)) + ((5 - rating) * 100) ASC"))
  }

  # New composite scopes for common query patterns
  scope :approved_with_comments, -> { approved.comments }
  scope :site_approved_with_comments, -> { site.approved.comments }
  scope :product_approved_with_comments, -> { product.approved.comments }

  # Additional optimized scopes for filtering
  scope :recent, ->(days = 30) { where('created_at >= ?', days.days.ago) }
  scope :for_user, ->(user_id) { where(user_id: user_id) }
  scope :for_design, ->(design_id) { where(design_id: design_id) }
  scope :for_designer, ->(designer_id) { where(designer_id: designer_id) }
  scope :with_order, -> { where.not(order_id: nil) }
  scope :certified, -> { with_order } # Reviews with order_id are certified

  # Geo-based scopes
  scope :domestic, -> { where(geo: 'domestic') }
  scope :international, -> { where(geo: 'international') }

  def self.reivew_percentage(reviews)
    star_percentage=Array.new(6,0)
    star_rating=Array.new(6,0)

    # Use group count to avoid N+1 queries
    rating_counts = reviews.group(:rating).count
    total_review = rating_counts.values.sum

    if total_review == 0
      (1..5).each do |i|
        star_percentage[i] = 0
      end
      return [star_percentage, star_rating, total_review]
    else
      (1..5).each do |i|
        star_rating[i] = rating_counts[i] || 0
        star_percentage[i] = ((star_rating[i] * 100) / total_review)
      end
      return [star_percentage, star_rating, total_review]
    end
  end

  # Optimized class methods using composite scopes for better performance
  def self.site_reviews_for_page(page: 1, per_page: 10)
    site_approved_with_comments
      .rating_above(1)
      .with_user
      .by_recent_rating
      .paginate(page: page, per_page: per_page, total_entries: 100)
  end

  def self.product_reviews_for_page(page: 1, per_page: 10)
    product_approved_with_comments
      .rating_above(1)
      .with_design_details
      .by_recent_rating
      .paginate(page: page, per_page: per_page, total_entries: 100)
  end

  def self.best_reviewed_designs_for_page(page: 1, per_page: 10)
    product_approved_with_comments
      .highly_rated_designs
      .with_design_details
      .by_recent_rating
      .paginate(page: page, per_page: per_page, total_entries: 100)
  end

  def survey_answer_array(*ids)
    survey_answer_hash = survey_answers.group_by(&:question_id)
    ids.collect do |id|
      response = survey_answer_hash[id].try{|sa| sa.first.response}
      case response
      when true
        'Yes'
      when false
        'No'
      when nil
        '-'
      end

    end
  end

  def reviewer(review, user=nil)
    if review.user.present?
      text = review.user.first_and_last
    else
      text = 'Guest Review'
    end
    text
  end

  # def add_negative_review_event
  #   if rating <= 2
  #     designer_order.try(:order_quality_event!, :add, 'NegativeFeedback')
  #     line_item.try(:order_quality_event!, :add, 'NegativeFeedback')
  #   end
  # end

  def update_vendor_odr
    self.designer_order.try(:order_quality_event!, :remove, 'NegativeFeedback')
    self.line_item.try(:order_quality_event!, :remove, 'NegativeFeedback')
    self.line_item.try(:order_quality_event!, :add, 'PositiveFeedback')
    MetricDefinition.where(name: 'OrderDefectRate').each do |metric_definition|
      MetricValue.import *metric_definition.generate_data(Date.today, "designer_orders.designer_id = #{self.designer_id}"),
                             validate: false, on_duplicate_key_update: { conflict_target: [:actor_id, :actor_type, :metric_definition_id, :generated_on], columns: [:value, :from_date, :to_date, :denominator_count] } if self.designer.present?
    end
  end

  def self.rating_group_text
    group_count = group(:rating).count
    total = group_count.sum(&:last).to_f
    min_total = [total,1].max
    positive_count = group_count.slice(4,5).sum(&:last)
    neutral_count = group_count.slice(3).sum(&:last)
    negative_count = group_count.slice(1,2).sum(&:last)
    {
      total: total.to_i,
      positive: [(positive_count / min_total * 100).round(1), positive_count],
      neutral: [(neutral_count / min_total * 100).round(1), neutral_count],
      negative: [(negative_count / min_total * 100).round(1), negative_count],
    }
  end

  #handle_asynchronously :new_review

  def self.new_review(line_item_id, review_text: nil, rating: nil)
    line_item = LineItem.where(id: line_item_id).first
    order = line_item.order
    rating = rating.to_i
    if order && line_item && (review_text.present? || rating > 0)

      design = line_item.design
      designer = design.designer

      survey_answers = SurveyAnswer.preload(:survey_question).where(surveyable: line_item)
      issues = survey_answers.map{|ans| ans.survey_question.try(:qualify_as)}.uniq.join(', ')

      # ratings = survey_answers.select do |ans|
      #   [*'1'..'5'].include?(ans.answer)
      # end.collect(&:answer).map(&:to_i)
      # avg_rating = ratings.present? ? (ratings.sum / ratings.size.to_f) : 0

      finding_param = order.user_id.nil? ? {order_id: order.id} : {user_id: order.user_id}
      review = design.reviews.where(finding_param).first_or_initialize
      review.rating = 0 if review.rating.blank?
      review.rating = rating  if rating > 0
      review.review = review_text if review_text.present?
      review.assign_attributes(
        issue: issues,
        designer_id: designer.id,
        geo: order.geo,
        order_id: order.id
      )
      review.save!
      review.orders << order unless review.orders.include?(order)

      if (stitching_answers = survey_answers.select{|ans| SurveyQuestion::STITCHING_QUESTIONS.include?(ans.question_id)}).present?
        tag_name = stitching_answers.all?{|ans| ans.response} ? 'Good Stitching' : 'Bad Stitching'
        Order.tag_measurements(order.id,review.id,design.id,tag_name)
      end

      design.update_reviews
      designer.update_reviews
      designer.update_nps_review_score({param1: rating})
      unless rating > 3
        DesignerMailer.send_nps_feedback_to_designer(designer.id, design.id)
      end
    end
  end


  # handle_asynchronously :add_negative_review_event

  # def self.add_negative_review_event(*ids)
  #   where(id: ids).each(&:add_negative_review_event)
  # end
end
