class DynamicPrice < ActiveRecord::Base
  #attr_accessible :country_code, :country_id, :design_id, :variant_id, :scale

  belongs_to :design
  belongs_to :variant
  belongs_to :country

  before_save :add_country_code
  before_save :ensure_valid_references

  validates :country_id, presence: true
  validates :scale, numericality: { greater_than_or_equal_to: 0.45 }
  validate :design_or_variant_present

  def add_country_code
    self.country_code=country.iso3166_alpha2  	if !self.country_code.present?
  end

  def design_or_variant_present
    if design_id.blank? && variant_id.blank?
      errors.add(:base, "Either design_id or variant_id must be present")
    end
  end

  def ensure_valid_references
    # Ensure we don't have both design_id and variant_id set
    if design_id.present? && variant_id.present?
      self.design_id = nil
    end
  end

  def self.update_designs(filename,remove_option = nil)
    @remove_option = remove_option.nil? ? {dynamic_pricing: true,stop_indexing: true} : JSON.parse(remove_option).symbolize_keys
    full_path = 'https://s3-'+ ENV['AWS_REGION'] +'.amazonaws.com/' + ENV['S3_BUCKET'] + '/' + filename
    scale=1.0 if (@remove_option[:dynamic_pricing] == false && @remove_option['buy_get_free'] == nil)
    begin
      # Download and clean the CSV content to handle encoding issues
      csv_content = open(full_path).read

      # Handle encoding issues by converting to UTF-8 and removing problematic characters
      csv_content = csv_content.force_encoding('UTF-8')
      unless csv_content.valid_encoding?
        # If not valid UTF-8, try to convert from common encodings
        csv_content = csv_content.force_encoding('ISO-8859-1').encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
      end

      # Remove non-breaking spaces and other problematic characters
      # Handle non-breaking space (U+00A0) more safely for Rails 4.2.5
      csv_content = csv_content.tr("\u00A0", ' ')  # Replace non-breaking space with regular space
      csv_content = csv_content.encode('UTF-8', invalid: :replace, undef: :replace, replace: '')  # Clean encoding

      # Parse CSV and validate format
      csv_data = CSV.parse(csv_content, headers: true, header_converters: :symbol)

      # Validate CSV format
      unless validate_csv_format(csv_data.headers)
        raise "Invalid CSV format. Expected headers: 'design_id | country_id | scale | country_code' OR 'variant_id | country_id | scale | country_code'"
      end

      csv_data.each_slice(1000) do |raws|
        dynamic_price_entries = load_dynamic_price_entries(raws, scale)
        if (DynamicPrice.check_file(dynamic_price_entries))
          DynamicPrice.update_and_insert(dynamic_price_entries)
        end
      end

    rescue Encoding::UndefinedConversionError => e
      Rails.logger.error "Encoding error in CSV file #{filename}: #{e.message}"
      raise "CSV file contains invalid characters. Please save the file as UTF-8 encoded CSV and try again."
    rescue => e
      Rails.logger.error "Error processing CSV file #{filename}: #{e.message}"
      raise e
    end
  end

  def self.check_for_present_designs(data)
    present_designs = []

    # Handle entries with design_id
    design_entries = data[1].select { |h| h[:design_id].present? }
    if design_entries.present?
      design_ids = design_entries.map { |h| h[:design_id] }
      valid_design_ids = Design.where(id: design_ids).pluck(:id)
      present_designs += valid_design_ids
      data[1].delete_if { |h| h[:design_id].present? && valid_design_ids.exclude?(h[:design_id]) }
    end

    # Handle entries with variant_id
    variant_entries = data[1].select { |h| h[:variant_id].present? }
    if variant_entries.present?
      variant_ids = variant_entries.map { |h| h[:variant_id] }
      valid_variant_ids = Variant.where(id: variant_ids).pluck(:id)
      # Get the design_ids from variants for consistency with the return value
      variant_design_ids = Variant.where(id: valid_variant_ids).pluck(:design_id).uniq
      present_designs += variant_design_ids
      data[1].delete_if { |h| h[:variant_id].present? && valid_variant_ids.exclude?(h[:variant_id]) }
    end

    return present_designs.uniq, data
  end

  def self.get_array_to_update_and_insert(dynamic_price_hash, get_design_id=nil)
    dynamic_price_array = []
    if get_design_id.present?
      dynamic_price_hash.each do |x|
        if x[:design_id].present?
          dynamic_price_array << x[:design_id]
        elsif x[:variant_id].present?
          dynamic_price_array << x[:variant_id]
        end
      end
    else
      dynamic_price_hash.each do |x|
        dynamic_price_array << x.values
      end
    end
    dynamic_price_array
  end

  def self.validate_csv_format(headers)
    # Convert headers to strings for comparison
    header_strings = headers.map(&:to_s)

    # Define expected formats
    design_format = %w[design_id country_id scale country_code]
    variant_format = %w[variant_id country_id scale country_code]

    # Check if headers match either format
    design_match = design_format.all? { |col| header_strings.include?(col) }
    variant_match = variant_format.all? { |col| header_strings.include?(col) }

    if design_match && variant_match
      Rails.logger.warn "CSV contains both design_id and variant_id columns. This may cause conflicts."
      return true
    elsif design_match
      Rails.logger.info "Detected Design CSV format: design_id | country_id | scale | country_code"
      return true
    elsif variant_match
      Rails.logger.info "Detected Variant CSV format: variant_id | country_id | scale | country_code"
      return true
    else
      Rails.logger.error "Invalid CSV format. Headers found: #{header_strings.join(', ')}"
      return false
    end
  end

  def self.check_file(dynamic_price_entries)
    actual_array = dynamic_price_entries.flat_map(&:values)
    array_without_nil = actual_array.compact
    (actual_array == array_without_nil) && array_without_nil.present? && (array_without_nil & [nil,0,0.0,'',' ']).empty?
  end

  def self.update_and_insert(dynamic_price_entries)
    remaining_designs = []
    designs_need_to_be_updated = []
    international_codes = SystemConstant.get('DYNAMIC_PRICE_UPLOAD_COUNTRIES').split(',')
    min_rate = CurrencyConvert.get_rate_for_dynamic_pricing
    country_id_hash = Country.get_all_country_id
    # country_id_hash.delete('IN') # this could be the reason for not having scaling for india
    dynamic_price_entries.group_by{|hash_sorted_by_country_code| hash_sorted_by_country_code[:country_code]}.each do |hash_sorted_by_country_code|
      # Extract design_ids from both design_id and variant_id entries
      design_ids = hash_sorted_by_country_code[1].map do |entry|
        if entry[:design_id].present?
          entry[:design_id]
        elsif entry[:variant_id].present?
          # Get design_id from variant for buy_get_free logic
          variant = Variant.find_by(id: entry[:variant_id])
          variant.design_id if variant
        end
      end.compact

      country_code = hash_sorted_by_country_code[0]
      unless international_codes.include?(country_code)
        if @remove_option[:buy_get_free].present?
          buy_get_free_value = country_code == 'IN' ? 1 : 2  #when domestic sheet uploaded will check designs uploaded are present for international b1g1 criteria previously and vice-versa
          designs_to_update = Design.where(id: design_ids, buy_get_free: buy_get_free_value).pluck(:id) if buy_get_free_value.present?
          Design.where(id: designs_to_update).update_all(buy_get_free: 3) if designs_to_update.present? #update designs b1g1 for both
        end
        data1, data2 = DynamicPrice.upload_and_update_dynamic_price(hash_sorted_by_country_code,min_rate,country_id_hash)
      else
        data1, data2 = DynamicPrice.upload_all_country_entries(hash_sorted_by_country_code,min_rate,country_id_hash)
      end
      remaining_designs.push(* data1)
      designs_need_to_be_updated.push(* data2)
    end
    DynamicPrice.insert_and_turn_flag_on(remaining_designs,designs_need_to_be_updated)
  end

  def self.upload_all_country_entries(hash_sorted_by_country_code,min_rate,country_id_hash)
    int_countries = country_id_hash
    if hash_sorted_by_country_code[0] == 'INT'
      int_countries = int_countries.to_a
    elsif (int_country_code = SystemConstant.get(hash_sorted_by_country_code[0])).present?
      int_country_code = int_country_code.split(',')
      int_countries = int_countries.collect{|k,v| [k,v] unless(int_country_code.exclude?k)}.compact
      return [],[] if int_countries.length != int_country_code.length
    end
    if int_countries.class == Array
      # Handle both design_id and variant_id entries
      design_entries = hash_sorted_by_country_code[1].select { |h| h[:design_id].present? }
      variant_entries = hash_sorted_by_country_code[1].select { |h| h[:variant_id].present? }

      # Make unique by design_id or variant_id
      design_entries.uniq! { |h| h[:design_id] }
      variant_entries.uniq! { |h| h[:variant_id] }

      # Create scale arrays for both types
      int_design_scale = design_entries.collect { |d| [d[:design_id], d[:scale], 'design'] }
      int_variant_scale = variant_entries.collect { |v| [v[:variant_id], v[:scale], 'variant'] }

      # Combine both arrays
      int_all_scale = int_design_scale + int_variant_scale

      data1, data2 = DynamicPrice.make_upload_hash(int_countries, int_all_scale, min_rate, country_id_hash)
      return data1, data2
    else
      return [], []
    end
  end

  def self.insert_and_turn_flag_on(remaining_designs, designs_need_to_be_updated)
    # Split remaining designs into those with design_id and those with variant_id
    design_entries = []
    variant_entries = []

    remaining_designs.each do |entry|
      # Handle both hash and array formats
      if entry.is_a?(Hash)
        if entry[:variant_id].present?
          # Convert hash to array format for import: [country_id, variant_id, scale, country_code]
          variant_entries << [entry[:country_id], entry[:variant_id], entry[:scale], entry[:country_code]]
        elsif entry[:design_id].present?
          # Convert hash to array format for import: [country_id, design_id, scale, country_code]
          design_entries << [entry[:country_id], entry[:design_id], entry[:scale], entry[:country_code]]
        end
      elsif entry.is_a?(Array) && entry.size == 4
        # Legacy array format handling
        if entry[1].present? && entry[1] > 0 # design_id is typically the second element
          design_entries << entry
        elsif entry.size > 4 && entry[4].present? # variant_id might be the fifth element
          variant_entries << entry
        end
      end
    end

    # Remove duplicates before import to prevent unique constraint violations
    design_entries.uniq! { |entry| [entry[3], entry[1]] } # unique by [country_code, design_id]
    variant_entries.uniq! { |entry| [entry[3], entry[1]] } # unique by [country_code, variant_id]

    # Import design entries with proper duplicate handling
    if design_entries.present?
      begin
        fields_to_insert = %w{country_id design_id scale country_code}
        DynamicPrice.import fields_to_insert, design_entries, validate: false, on_duplicate_key_update: [:scale]
      rescue ActiveRecord::RecordNotUnique => e
        Rails.logger.error "Duplicate design entries found: #{e.message}"
        # Handle duplicates by updating existing records individually
        design_entries.each do |entry|
          existing_record = DynamicPrice.find_by(country_code: entry[3], design_id: entry[1])
          if existing_record
            existing_record.update_attributes(scale: entry[2])
          else
            DynamicPrice.create(country_id: entry[0], design_id: entry[1], scale: entry[2], country_code: entry[3])
          end
        end
      end
    end

    # Import variant entries with proper duplicate handling
    if variant_entries.present?
      begin
        fields_to_insert = %w{country_id variant_id scale country_code}
        DynamicPrice.import fields_to_insert, variant_entries, validate: false, on_duplicate_key_update: [:scale]
      rescue ActiveRecord::RecordNotUnique => e
        Rails.logger.error "Duplicate variant entries found: #{e.message}"
        # Handle duplicates by updating existing records individually
        variant_entries.each do |entry|
          existing_record = DynamicPrice.find_by(country_code: entry[3], variant_id: entry[1])
          if existing_record
            existing_record.update_attributes(scale: entry[2])
          else
            DynamicPrice.create(country_id: entry[0], variant_id: entry[1], scale: entry[2], country_code: entry[3])
          end
        end
      end
    end

    # Update designs and reindex
    Design.where(id: designs_need_to_be_updated).update_all(@remove_option.slice(:dynamic_pricing, :buy_get_free))
    Sunspot.index Design.where(id: designs_need_to_be_updated) if !(@remove_option[:stop_indexing])
  end

  def self.make_upload_hash(int_country_id_code,int_all_scale,min_rate,country_id_hash)
    remaining_designs = []
    designs_need_to_be_updated = []
    upload_hash = []
    int_country_id_code.each do |country|
      int_all_scale.each do |scale_entry|
        entry_hash = {
          country_id: country[1],
          scale: scale_entry[1],
          country_code: country[0]
        }

        # Add design_id or variant_id based on the type
        if scale_entry[2] == 'variant'
          entry_hash[:variant_id] = scale_entry[0]
        else
          entry_hash[:design_id] = scale_entry[0]
        end

        upload_hash << entry_hash
      end
    end
    upload_hash.group_by{|sort_by_country_code| sort_by_country_code[:country_code]}.each do |sort_by_country_code|
      data1, data2 = DynamicPrice.upload_and_update_dynamic_price(sort_by_country_code,min_rate,country_id_hash)
      remaining_designs.push(* data1)
      designs_need_to_be_updated.push(* data2)
    end
    return remaining_designs, designs_need_to_be_updated
  end

  def self.upload_and_update_dynamic_price(hash_sorted_by_country_code, min_rate, country_id_hash)
    remaining_designs = []
    designs_need_to_be_updated = []

    # Filter out entries with invalid country_id or scale
    hash_sorted_by_country_code[1].delete_if do |h|
      (h[:country_id] != country_id_hash[h[:country_code]]) ||
      ((h[:scale] < ((min_rate[hash_sorted_by_country_code[0]] if h[:country_code] != 'IN') || MINIMUM_SCALING_FACTOR).to_f.round(2) + INTERNATIONAL_MARGIN))
    end

    # Make entries unique by design_id or variant_id
    if hash_sorted_by_country_code[1].any? { |h| h[:design_id].present? }
      design_entries = hash_sorted_by_country_code[1].select { |h| h[:design_id].present? }
      design_entries.uniq! { |h| h[:design_id] }
    end

    if hash_sorted_by_country_code[1].any? { |h| h[:variant_id].present? }
      variant_entries = hash_sorted_by_country_code[1].select { |h| h[:variant_id].present? }
      variant_entries.uniq! { |h| h[:variant_id] }

      # Merge back the unique entries
      hash_sorted_by_country_code[1] = (design_entries || []) + (variant_entries || [])
    end

    if hash_sorted_by_country_code[1].present?
      designs_present, design_to_insert = DynamicPrice.check_for_present_designs(hash_sorted_by_country_code) # designs for mass updation and design_to_insert for mass insertion

      if designs_present.present?
        designs_need_to_be_updated += designs_present

        # Group by scale for batch processing
        design_to_insert[1].group_by { |h| h[:scale] }.each do |scale, entries|
          # Process design entries
          design_entries = entries.select { |h| h[:design_id].present? }
          if design_entries.present?
            design_to_update = DynamicPrice.get_array_to_update_and_insert(design_entries, true)
            dynamic_price = DynamicPrice.where(design_id: design_to_update, country_code: design_to_insert[0])
            designs_in_dynamic_price = dynamic_price.pluck(:design_id)
            dynamic_price.update_all(scale: scale)
            design_entries.delete_if { |h| designs_in_dynamic_price.include?(h[:design_id]) }
            remaining_designs += DynamicPrice.get_array_to_update_and_insert(design_entries)
          end

          # Process variant entries
          variant_entries = entries.select { |h| h[:variant_id].present? }
          if variant_entries.present?
            variant_to_update = DynamicPrice.get_array_to_update_and_insert(variant_entries, true)
            dynamic_price = DynamicPrice.where(variant_id: variant_to_update, country_code: design_to_insert[0])
            variants_in_dynamic_price = dynamic_price.pluck(:variant_id)
            dynamic_price.update_all(scale: scale)
            variant_entries.delete_if { |h| variants_in_dynamic_price.include?(h[:variant_id]) }
            remaining_designs += DynamicPrice.get_array_to_update_and_insert(variant_entries)
          end
        end
      end
    end

    return remaining_designs, designs_need_to_be_updated
  end

  def self.load_dynamic_price_entries(parsed_file,scale)
    parsed_file.collect do |line|
      # Clean country_code with encoding handling
      country_code = line[:country_code].to_s.upcase
                                        .tr("\n", '')
                                        .tr("\u00A0", ' ')  # Replace non-breaking space
                                        .strip

      entry = {
        country_id: line[:country_id].to_i,
        scale: scale || line[:scale].to_f,
        country_code: country_code
      }

      # Detect CSV format and add appropriate ID
      # Format 1: design_id | country_id | scale | country_code
      # Format 2: variant_id | country_id | scale | country_code
      if line[:variant_id].present?
        entry[:variant_id] = line[:variant_id].to_i
      elsif line[:design_id].present?
        entry[:design_id] = line[:design_id].to_i
      else
        # If neither is explicitly present, this shouldn't happen with proper CSV headers
        Rails.logger.warn "CSV row missing both design_id and variant_id: #{line.inspect}"
      end

      entry
    end
  end
end
