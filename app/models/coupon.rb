class Coupon < ActiveRecord::Base
  belongs_to :designer
  belongs_to :source_order, :class_name => 'Order'
  belongs_to :account
  belongs_to :creator_account, class_name: 'Account', foreign_key: 'created_by'
  belongs_to :coupon_used_on, class_name: 'Order'
  has_many   :used_on_orders, -> { select('orders.id,orders.number,state,confirmed_at,coupon_id') }, class_name: 'Order'
  has_many :designs

  COUPON_TYPE = {
    'Flat Discounts' => :FOFF,
    'Percentage Discounts' => :POFF,
    'Credit Discounts' => :COFF,
  }
  
  scope :advertise, -> { where(advertise: true) }

  validates :name, :presence => true
  validates :code, :uniqueness => true
  validates :start_date, :presence => true
  validates :end_date, :presence => true
  validates :coupon_type, presence: true, inclusion: { in: %w(FOFF POFF COFF STITOFF SHIPOFF),
    message: "%{value} is not a valid coupon type. coupon type should be FOFF/POFF/COFF" }
  validate :date_duration_valid

  validates :limit, :presence => true, :numericality => { :only_integer => true, :greater_than_or_equal_to => 1 }
  
  validates :percent_off, numericality: {
    only_integer: true,
    greater_than_or_equal_to: 0,
    less_than_or_equal_to: 100
  }, if: :poff?
  validates :flat_off, numericality: {
    only_integer: true,
    greater_than_or_equal_to: 0,
    less_than_or_equal_to: proc { |f| f.min_amount.to_i },
  }, if: proc { |c| c.foff? }
  validates :min_amount, presence: true, numericality: {
    only_integer: true,
    greater_than_or_equal_to: :flat_off,
  }, if: proc { |c| c.poff? || c.foff? }
  
  validates :use_count, presence: true,  numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :max_discount, numericality: {
    only_integer: true,
    greater_than: 0
  }, allow_nil: true, if: :poff?
  validate :validate_app_source
  # end date has to be greater than start date
  def eligible?(cart)
    unless allowed_in_country?(cart.country_code)
      cart.errors.add(:coupon, 'is not valid for your country')
      return false
    end
    if !coupon_valid?
      cart.errors.add(:coupon, 'is either expired or used')
      false
    elsif !rules_are_eligible?(cart)
      cart.errors.add(:coupon, 'minimum amount not satisfied.') unless cart.errors[:coupon].present?
      false
    else
      true
    end
  end

  def check_mirraw_coupon?
    self.app_name.present? && self.app_name.downcase == 'mirraw'
  end

  def is_app_source_website_or_all?
    app_source.present? && app_source.any?{ |value| ['website', 'all'].include?(value) }
  end

  def coupon_valid?
    self.live? && self.valid? && self.check_mirraw_coupon? && self.is_app_source_website_or_all?
  end
  
  def future?
    start_date && end_date && (start_date > Date.today) && self.limit && (self.use_count < self.limit)
  end

  def live?
    (DateTime.now >= start_date) && (DateTime.now < end_date) &&
      (self.use_count < self.limit)
  end

  def is_stitching?
    coupon_type == "STITOFF"
  end

  def is_shipping?
    coupon_type == "SHIPOFF"
  end
  
  def self.live
    Coupon.where('start_date <= ?', Time.now).where('end_date >= ?', Time.now).where('coupons.use_count >= ?', 0).where('coupons.use_count < coupons.limit')
  end

  def self.signup_coupon_for(country_code)
    Rails.cache.fetch("signup_coupon_#{country_code}", expires_in: 6.hours) { Coupon.live.where(name: "signup_discount_#{country_code}").first || Coupon.live.where(name: 'signup_discount').first }
  end

  def self.valid_to_show
    Coupon.live.where('designer_id is NOT NULL').advertise
  end

  def self.live_by_designer(designer)
    designer = Designer.find(designer)
    designer.coupons.where('start_date <= ?', Time.now).where('end_date >= ?', Time.now).where('coupons.use_count >= ?', 0).where('coupons.use_count < coupons.limit')
  end

  def self.eligible_for(design)
    designer = Designer.find(design.designer)
    live_coupons = designer.coupons.where('start_date <= ?', Time.now).where('end_date >= ?', Time.now).where('coupons.use_count >= ?', 0).where('coupons.use_count < coupons.limit').where('coupons.min_amount < ?', design.effective_price).first
  end

  def rules_are_eligible?(cart)
    designer = self.designer
    running_total = 0

    if self.designer.present?
      cart.line_items.each do |item|
        if item.design.designer == self.designer && !item.sor_available?
          running_total += item.sub_total
        end
      end
      cart.errors.add(:coupon, 'not applicable on this cart') if running_total == 0
    elsif is_stitching?
      running_total = cart.items_total_without_addons(1)
    else
      cart.line_items.each do |item|
        running_total += item.sub_total
      end
    end
    return false if running_total - cart.bmgnx_discounts < min_amount
    return true
  end

  def apply_coupon(cart,return_type=RETURN_SCALED,order=nil)
    normal_return = return_type&RETURN_NORMAL!=0
    scaled_return = return_type&RETURN_SCALED!=0
    discounts = 0
    scaled_discounts = 0
    if self.present? && self.eligible?(cart)
      if self.coupon_type == 'POFF'
        bmgn_discounts = cart.bmgnx_discounts(return_type: RETURN_NORMAL)
        bmgn_discounts_scaled = cart.bmgnx_discounts(return_type: RETURN_SCALED)
      end
      if self.designer.present?
        designer = self.designer
        running_total = 0 if normal_return
        running_total_scale = 0 if scaled_return
        cart.line_items.each do |item|
          if item.design.designer == self.designer
            running_total += item.sub_total(RETURN_NORMAL) if normal_return
            running_total_scale += item.sub_total if scaled_return
          end
        end
        if ['FOFF', 'COFF'].include? self.coupon_type
          scaled_discounts = discounts = self.flat_off.to_i
        elsif self.coupon_type == "POFF"
          discounts = ((self.percent_off * (running_total - bmgn_discounts)) / 100).to_i if normal_return
          scaled_discounts = ((self.percent_off * (running_total_scale - bmgn_discounts_scaled)) / 100).to_i if scaled_return
          # Cap discount if max_discount is set
          if self.max_discount.present?
            discounts = [discounts, self.max_discount].min if normal_return
            scaled_discounts = [scaled_discounts, self.max_discount].min if scaled_return
          end
        end
      elsif ['FOFF', 'POFF', 'COFF'].include?(self.coupon_type)
        running_total = 0 if normal_return
        running_total_scale = 0 if scaled_return
        cart.line_items.each do |item|
          running_total += item.sub_total(RETURN_NORMAL) if normal_return
          running_total_scale += item.sub_total if scaled_return
        end
        if ['FOFF', 'COFF'].include? self.coupon_type
          scaled_discounts = discounts = self.flat_off.to_i
        elsif self.coupon_type == "POFF"
          discounts = ((self.percent_off * (running_total - bmgn_discounts)) / 100).to_i if normal_return
          scaled_discounts = ((self.percent_off * (running_total_scale - bmgn_discounts_scaled)) / 100).to_i if scaled_return
          # Cap discount if max_discount is set
          if self.max_discount.present?
            discounts = [discounts, self.max_discount].min if normal_return
            scaled_discounts = [scaled_discounts, self.max_discount].min if scaled_return
          end
        end
      end
    # elsif self.present? 
    #   apply_stitching_coupon(cart, order)
    end
    if normal_return and scaled_return
      [discounts, scaled_discounts]
    elsif scaled_return
      scaled_discounts
    else
      discounts
    end
  end

  def to_s
    if self.coupon_type == "POFF"
      self.percent_off.to_s + '% off on Rs. ' + self.min_amount.to_s
    else                                                                                      
      "Rs. " + self.flat_off.to_s + ' off on Rs. ' + self.min_amount.to_s
    end
  end

  def self.download_coupon_report(params,account_data)
    if !(created_by = params[:created_by]).nil? || (designer_id = params[:designer_id]).present? || ((start_date=DateTime.parse(params[:coupon_start_date])).present? && (end_date=DateTime.parse(params[:coupon_end_date])).present?)
      coupons_data = Coupon.where(created_by: created_by).where{created_at > 3.months.ago}.order('created_at').preload(:source_order,:coupon_used_on) if created_by.present?
      coupons_data = Coupon.where(designer_id: designer_id).order('created_at').preload(:source_order,:coupon_used_on) if designer_id.present?
      coupons_data = Coupon.where(created_at: start_date.beginning_of_day..end_date.end_of_day).preload(:source_order,:coupon_used_on) if start_date.present?
      file = CSV.generate do |csv|
        csv << ['Created By (Account Id):', Designer.where(id: designer_id).first.try(:name)] if designer_id.present?
        csv << ['Coupon Name','Min Amount','Flatt Off','Percent Off','Use Limit','Used Coupons','Source Order','Used On ','Created By']
        if coupons_data.present?
          coupons_data.find_each do |coupon|
            row = [coupon.name,coupon.min_amount,coupon.flat_off,coupon.percent_off,coupon.limit,coupon.use_count,coupon.source_order.try(:number),coupon.coupon_used_on.try(:number)]
            row <<  account_data[coupon.created_by][0] if (coupon.created_by.present? && account_data[coupon.created_by].present?)
            csv << row
          end
        end
      end
      return file 
    end
  end

  # Define `poff?`, `foff?` and `coff?` for asserting the appropriate coupon type
  [:poff, :foff, :coff].each do |ctype|
    define_method("#{ctype}?") do
      coupon_type == "#{ctype.upcase}"
    end
  end

  def self.validate_and_create_coupon(coupon_params, order_id, rtn_order, account: nil)
    order = Order.find_by_id order_id
    if order.present?
      previous_coupons = Coupon.where(source_order_id: order_id)
      previous_coupon_total = 0
      previous_coupons.each{|coupon| previous_coupon_total += coupon.flat_off unless (coupon.use_count < coupon.limit && coupon.end_date <= Time.now)}  
      coupon_amount = coupon_params[:flat_off].to_i
      line_items = order.line_items.preload(:line_item_addons).to_a
      order_total = (line_items.sum(&:sub_total) + line_items.sum{|l| l.line_item_addons.to_a.sum(&:sub_total)}).to_f*1.5
      order_total += ((order[:notes].present? && order[:notes].include?('system_shipping_cost')) ? order[:notes].split(/system_shipping_cost/)[1][/\d+/].to_i : 0) if ['cancel','cancel_complete'].include? (order.state)
      coupon_creation_flag = ( previous_coupon_total + coupon_amount >= order_total) ? false : true # 1.5x is for margin in coupon amount
      if coupon_creation_flag
        coupon = Coupon.new(coupon_params)
        coupon.name = "#{coupon_params[:name]}_#{coupon_params[:flat_off].to_i}"

        record = true
        while record
          random = "#{SecureRandom.hex(3).upcase}_#{coupon_params[:flat_off].to_i}"
          record = Coupon.find_by_code(random)
        end
        coupon.code = 'OBC' + random
        coupon.advertise = false
        coupon.source_order_id = order.id
        coupon.created_by = account.id if account.present?
        coupon.return_id  = rtn_order.id
        if coupon.save
          return_values = {coupon_code: coupon.code , coupon_gen_date: coupon.created_at , coupon_id: coupon.id}
          rtn_order.update_attributes(return_values) if rtn_order.present?
          return return_values
        end
      end
    end
  end
  
  private

  def apply_validation_foff_or_poff?
    ['FOFF', 'POFF'].include? self.coupon_type
  end

  def date_duration_valid
    if self.start_date.present? && self.end_date.present? &&
      !(self.end_date > self.start_date)
      errors.add(:end_date, 'should be greater than start date')
    end
  end

  def validate_app_source
    self.app_source.reject!(&:empty?) if app_source.present?
  end

  # Returns true if the coupon is allowed in the given country code
  def allowed_in_country?(country_code)
    return true if self.country.blank?
    self.country.split(',').map(&:strip).include?(country_code.to_s.upcase)
  end
end
