class Stylist < ActiveRecord::Base
  #attr_accessible :orders_count, :name, :available, :vacation_start_date, :vacation_end_date, :vacation_message, :stylist_group
  has_many :orders
  has_many :stitching_measurements
  belongs_to :account
  validate :vacation_duration, if: :vacation_changed?
  scope :active, ->{where(available: true)}

  def self.assign_to_stylist(order, group, stylist_ids=[])
    w_stylist_filter = stylist_ids.present? ? ['id in (?)', stylist_ids] : [] 
    stylist = Stylist.where('available = ?',true).where(w_stylist_filter).where('LOWER(TRIM(stylist_group)) = ? OR stylist_group IS NULL OR TRIM(stylist_group) = ?', group.try(:downcase),'').order('orders_count ASC','id ASC').first
    if stylist.present?
      order.stylist_id = stylist.id
      order.assigned_to_stylist_at = DateTime.now
      order.add_notes_without_callback("Assigned to Stylist:#{stylist.name}",'stitching') if order.save
      stylist.orders_count += 1
      stylist.save
      stylist.id
    end
  end

  def self.update_suggested_measurements(suggest_ids,current_account,stitch_measurement,phone = false,attribute_hash=nil)
    suggested_mes = StitchingMeasurement.where(id:suggest_ids)
    suggested_mes << stitch_measurement if stitch_measurement.present?
    suggested_mes.each do |mes|
      if phone
        note = 'Arrange Phone Call'
        mes.phone_call_arranged
      else
        note = 'Approved by Suggestion'
        mes.assign_attributes(attribute_hash) if attribute_hash.present?
        mes.user_review.delete('system_approved')
        mes.approved_by_suggestion
      end
      mes.add_notes(note,true,current_account)
    end
    measurement = suggested_mes.first
    if StitchingMeasurement.joins(:line_item).where('order_id = ? AND stitching_measurements.design_id = ? AND line_items.status IS NULL',measurement.order_id,measurement.design_id).all?{|mes| mes.approved?}
      stylist = measurement.stylist
      LineItem.sidekiq_delay
              .mark_fabric_measurement_done(
                measurement.line_item_id,
                stylist.account_id,
                stylist.name
              )
      LineItem.sidekiq_delay
              .mark_fabric_measurement_confirmed(
                measurement.line_item_id,
                stylist.account_id,
                stylist.name
              )
    end
  end

  def self.assign_to_stylist_mobile(order_id,line_item_id)
    if (order = Order.find_by_id(order_id)).present? && !order.stylist_id? && (line_item = LineItem.find_by_id(line_item_id)).present?
      stitch_count = order.line_items.sane_items.where(stitching_required: 'Y').to_a.count
      order.assign_it_to_stylist(stitch_count, line_item.design.designable_type.downcase, 'custom')      
    end
    stitching_measurement = StitchingMeasurement.where(line_item_id: line_item_id).update_all(stylist_id: order.stylist_id)
  end

  def self.update_measurements(suggest_ids,current_user_id,stitch_measurement_id,phone = false,attribute_hash=nil)
    stitch_mes = StitchingMeasurement.find_by_id(stitch_measurement_id)
    current_account = current_user_id.present? ? User.find_by_id(current_user_id).account : nil
    update_suggested_measurements(suggest_ids,current_account,stitch_mes,phone,attribute_hash)
  end

  def vacation_duration
    if vacation_start_date.present? && vacation_end_date.present? && vacation_end_date <= vacation_start_date && vacation_start_date.to_date >= Date.today
      errors.add(:vacation_end_date, "vacation end date should be greater than vacation start date")
    elsif vacation_start_date.present? && vacation_end_date.present?
      available_groups = Stylist.select('TRIM(stylist_group) as stylist_groups').where(available: true).where("id <> '#{id}' AND vacation_start_date IS NULL").collect(&:stylist_groups).uniq
      group_condition_check = ENABLE_CUSTOM_STANDARD_STYLISTS.to_i == 1 ? (available_groups & ['custom','standard']).size < 2 : (available_groups & ['lehenga+saree', 'salwarkameez', 'mix']).size < 3
      if (stylist_group.blank? && ((available_groups & [nil, '']).blank? && group_condition_check)) || (stylist_group.present? && (available_groups & [stylist_group,'',nil]).size == 0)
        errors.add(:stylist_group,"Could Not Put on Vacation since This is Last Stylist of #{stylist_group} group")
      else
        delay(run_at: vacation_start_date,priority: 0).set_stylist_on_vacation('start')
        delay(run_at: vacation_end_date,priority: 0).set_stylist_on_vacation('end',vacation_end_date)
      end
    end
  end

  def set_stylist_on_vacation(action, date = nil)
    if action == 'start'
      update_column(:available,false) if available
    else
      update_attributes(vacation_start_date:nil,vacation_end_date:nil,vacation_message:nil,available:true) if !available && date == vacation_end_date
    end
  end

  def self.bulk_reassign(stylist_ids, order_numbers, email, stylist_names)
    all_orders = Order.select('id, number, stylist_id, notes, notification_count, other_details').where(number: order_numbers).where('stylist_id IS NULL OR stylist_id NOT IN (?) AND state NOT IN (?)',stylist_ids, ['cancel','dispatched'])
    shuffled_orders = all_orders.shuffle.in_groups(stylist_ids.size)
    old_stylist_counts = {}
    new_stylist_counts = {}
    shuffled_orders.each_with_index do |orders, index|
      orders.compact!
      elements_count = orders.collect(&:stylist_id).compact.group_by(&:itself).map{|a,v| [a, v.length]}.to_h
      old_stylist_counts.merge!(elements_count){|key, count1, count2| count1 + count2}
      orders.each do |ord|
        ord.stylist_id = stylist_ids[index]
        ord.add_notes_without_callback("Reassigned to #{stylist_names.key(stylist_ids[index]).split(' |')[0]}", 'stitching')
      end
      new_stylist_counts[stylist_ids[index]] = orders.size
      StitchingMeasurement.where(order_id: orders.collect(&:id)).update_all(stylist_id: stylist_ids[index])
    end
    old_stylist_counts = old_stylist_counts.map{|val| [val[0], -val[1]]}.to_h
    all_stylist_counts = old_stylist_counts.merge(new_stylist_counts)
    (stylists = Stylist.select('id, orders_count').where(id: (old_stylist_counts.keys + new_stylist_counts.keys).uniq)).each do |stylist|
      stylist.orders_count += all_stylist_counts[stylist.id]
    end
    shuffled_orders = shuffled_orders.flatten.compact
    Stylist.import stylists.to_a, validate: false, on_duplicate_key_update: {conflict_target: [:id], columns: [:orders_count]}
    Order.import shuffled_orders, validate: false, on_duplicate_key_update: {conflict_target: [:id], columns: [:stylist_id]}
    mail_stylist_reassign_report(shuffled_orders, order_numbers, stylist_names,email)
  end

  def self.single_reassign(order, old_stylist_id, new_stylist_id, current_account)
    stylist_ids = [new_stylist_id, old_stylist_id].compact.uniq
    stylist = Stylist.where(id: stylist_ids).map{|a| [a.id,a]}.to_h
    order.update_column(:stylist_id, new_stylist_id)
    order.add_notes_without_callback("Reassigned to #{stylist[new_stylist_id].try(:name)}", 'stitching', current_account)
    StitchingMeasurement.where(order_id: order.id).update_all(stylist_id: new_stylist_id)
    stylist[new_stylist_id].orders_count += 1
    stylist[old_stylist_id].orders_count -= 1 if old_stylist_id.present?
    Stylist.import stylist.values, validate: false, on_duplicate_key_update: { conflict_target: [:id], columns: [:orders_count]}
    return stylist[new_stylist_id].name
  end

  def self.get_stylists
    stylists = Rails.cache.fetch('stylist',expires_in: CACHE_EXPIRES_IN) do
      Stylist.select('id, name').to_a
    end
  end

  def self.get_current_track_report
    start_date = Date.today.beginning_of_day
    end_date = Date.today.end_of_day
    stylist_daily_hash = Hash.new
    ['stylist_measurement', 'stylist_tailoring', 'stylist_to_be_receive', 'stylist_slots'].each do |metric_name|
      method_name = "get_#{metric_name}_stats"
      public_send(method_name, start_date, end_date, stylist_daily_hash) if respond_to?(method_name)
    end
    get_stylist_pending_stats(STYLIST_TRACK_CONSTANTS['pending_from_days'].to_i.day.ago.beginning_of_day, Date.yesterday.end_of_day, stylist_daily_hash)
    stylist_daily_hash
  end

  private
    def self.get_stylist_measurement_stats(start_date, end_date, output_hash)
      state_wise_mes_count = StitchingMeasurement.select('stylist_id,state,count(id) as mes_count').where('stylist_id is not null').where('(rejected_at between ? and ?) or (approved_at between ? and ?) or (hold_at between ? and ?) or (phone_call_at between ? and ?)', start_date, end_date, start_date, end_date, start_date, end_date, start_date, end_date).reorder('').group('stylist_id, state')
      normalize_values(output_hash, state_wise_mes_count, [:state, :mes_count])
    end

    def self.get_stylist_tailoring_stats(start_date, end_date, output_hash)
      tailoring_status_count = TailoringInfo.select("orders.stylist_id, (case when material_received_status_timestamp is not null and material_received_status = true then 'Received' when reassign_material_timestamp > alteration_added_at then 'Reassigned' else 'Alteration' end) as tailoring_status, sum(line_items.quantity) as total_count").joins(:line_item, :order).where('(reassign_material_timestamp between ? and ?) or (material_received_status_timestamp between ? and ? and material_received_status = ?) or (alteration_added_at between ? and ?)', start_date, end_date, start_date, end_date, true, start_date, end_date).where('tailoring_material not in (?) and orders.stylist_id is not null', ['Saree', 'FNP']).group("orders.stylist_id, (case when material_received_status_timestamp is not null and material_received_status = true then 'Received' when reassign_material_timestamp > alteration_added_at then 'Reassigned' else 'Alteration' end)")
      normalize_values(output_hash, tailoring_status_count, [:tailoring_status, :total_count])
    end

    def self.get_stylist_to_be_receive_stats(start_date, end_date, output_hash)
      StylistReceive.select('stylist_id, sum(line_items.quantity) as to_be_receive_count').joins(:line_items).where(created_at: start_date..end_date).group('stylist_id').map{|i| (output_hash[i.stylist_id] ||= {})['to_be_received'] = i.to_be_receive_count } 
    end

    def self.get_stylist_slots_stats(start_date, end_date, output_hash)
      slot_wise_count = Scan.joins(line_item: :stitching_measurements).where(scanned_at: start_date..end_date).where('stylist_id is not null and scan_type = ?', 'Handover To Stylist For Working').select("stylist_id, (case when date_part('hour', scanned_at) <= 12 then 'Slot1' else 'Slot2' end) as slots, count( distinct stitching_measurements.id) as mes_count").group("stylist_id, (case when date_part('hour', scanned_at) <= 12 then 'Slot1' else 'Slot2' end)")
      normalize_values(output_hash, slot_wise_count, [:slots, :mes_count], true)
    end

    def self.get_stylist_pending_stats(start_date, end_date, output_hash)
      Scan.joins(line_item: :stitching_measurements).where(scanned_at: start_date..end_date).where('stylist_id is not null and scan_type = ?', 'Handover To Stylist For Working').select('stylist_id, count( distinct stitching_measurements.id) as mes_count').group('stylist_id').map{|i| (output_hash[i.stylist_id] ||= {})['total_pending'] = i.mes_count }       
    end    

    def self.normalize_values(output_hash, data, column_name, calc_total=false)
      data.each do |val| 
        (output_hash[val.stylist_id] ||= {})[val.public_send(column_name[0])] = val.public_send(column_name[1])
      end
      if calc_total
        output_hash.keys.each do |sty_id|
          stylist_hash = output_hash[sty_id]
          stylist_hash['total_worked_count'] = stylist_hash.values_at(*(stylist_hash.keys - ['Slot1', 'Slot2', 'to_be_received'])).sum
          stylist_hash['Planned'] = STYLIST_TRACK_CONSTANTS['stylist_capacity'].to_i
        end
      end
    end

    def self.mail_stylist_reassign_report(orders, all_order_numbers, stylist_names, email)
      orders = orders.flatten.compact
      file = CSV.generate do |csv|
        csv << ['Order Number', 'New Stylist']
        (orders.map{|o| [o.number, stylist_names.key(o.stylist_id).split(' |')[0]]}).each{|data| csv << data}
        3.times{ csv << [] }
        csv << ['Numbers','Error']
        ((all_order_numbers - orders.collect(&:number)).map{|num| [num, 'Was already assigned to selected stylists or in cancel or dispatch state']}).each{|data| csv << data}
      end
      emails = {'to_email'=> email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
      OrderMailer.report_mailer("Stylist Reassignment Report #{Date.today}", 'Please Find Attachment.', emails, {"Stylist Reassign Report #{Date.today}.csv"=> file}).deliver
    end

    def vacation_changed?
      vacation_start_date_changed? || vacation_end_date_changed?
    end
end
