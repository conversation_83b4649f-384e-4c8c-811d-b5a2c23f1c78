class ClickPostAutomation
  include Sidekiq::Extensions::Klass
  attr_accessor :order, :return_order, :request_credentials

  def initialize(order=nil, return_order=nil)
    self.order = order
    self.return_order = return_order
    self.request_credentials = Mirraw::Application.config.click_post
  end

  def get_pdd_response(path, payload = [], options = {}, path_type = :post)
    request_url = get_request_url(path_key: path, options: options)
    pdd_response = HTTParty.send(path_type, request_url, body: payload.to_json, headers: {'Content-Type' => 'application/json'})
  end

  def get_response_from_clickpost(payload = {})
    response = get_pdd_response(:pdd_check_path, payload).parsed_response.try(:deep_symbolize_keys)
    all_etas =  response[:result].collect{|k,v| k[:predicted_sla_min]}.uniq if response[:meta][:status] == 200
    eta = all_etas.min.to_i if all_etas.present? && (all_etas.max - all_etas.min) < 3 && all_etas.min.to_i > 0
    eta.to_i
  end

  def get_design_page_eta(city, pickup_pincode)
    Rails.cache.fetch("eta_#{pickup_pincode}_for_#{city}", expires_in: 7.day) do
      payload = []
      drop_pincodes = Pincode.get_city_wise_pincode(city.downcase)
      drop_pincodes.each do |drop_pincode|
        payload  << {
          pickup_pincode: pickup_pincode,
          drop_pincode: drop_pincode
        }
      end
      get_response_from_clickpost(payload)
    end
  end

  def get_order_new_page_eta(drop_pincodes = [], pickup_pincodes)
    payload = []
    drop_pincodes = Pincode.get_city_wise_pincode(drop_pincodes.downcase) if drop_pincodes.is_a?(String)
    drop_pincodes.each do |drop_pincode|
      pickup_pincodes.each do |pickup_pincode|
        payload  << {
          pickup_pincode: pickup_pincode,
          drop_pincode: drop_pincode
        }
      end
    end
    get_response_from_clickpost(payload)
  end 
  
  def get_eta_from_clickpost(drop_pincodes, pickup_pincodes)
    call_from =  pickup_pincodes.is_a?(String) ? 'design': 'order_new'
    method_to_invoke = "get_#{call_from}_page_eta".to_sym
    eta = send(method_to_invoke, drop_pincodes, pickup_pincodes) if respond_to?(method_to_invoke)
  end
  
  def get_response_for(path, payload = {}, options = {}, path_type = :post)
    request_url = get_request_url(path_key: path, options: options)
    tracking_response = HTTParty.send(path_type, request_url, body: payload.to_json, headers: {'Content-Type' => 'application/json'})
  end

  def get_recommended_courier(return_designer_orders,reverse = true, get_all_dso_recommendations=false)
    rvp_available, times_retried = {}, 0
    _, _, _, _, _, shipping_pincode, _, _  = order.get_warehouse_shipping_address
    all_dso_recommendations = []
    return_designer_orders.each do |rdo|
      rdo_total = rdo.total
      reference_number = rdo.id
      if reverse
        pickup_pincode = order.pincode
        drop_pincode = rdo.designer.business_pincode.presence || rdo.designer.pincode
        delivery_type = 'RVP'
        order_type = 'PREPAID'
      else
        pickup_pincode = rdo.designer.get_order_pickup_details(rdo,rdo.order)[:pincode]
        _, _, _, _, _, shipping_pincode, _, _  = order.get_warehouse_shipping_address
        drop_pincode = ((order.international? && rdo.ship_to.blank?) || rdo.ship_to == 'mirraw') ? shipping_pincode : order.pincode
        delivery_type = 'FORWARD'
        order_type = order.cod? ? 'COD' : 'PREPAID'
      end
      recommendation_hash = fetch_recommended_couriers(pickup_pincode,drop_pincode,delivery_type,order_type,rdo_total,reference_number)
      if get_all_dso_recommendations
        all_dso_recommendations << recommendation_hash
      else
        return recommendation_hash
      end
    end
    return all_dso_recommendations if get_all_dso_recommendations
  end

  def fetch_recommended_couriers(pickup_pincode,drop_pincode,delivery_type,order_type,rdo_total,reference_number)
    rvp_available, times_retried = {}, 0
    payload = [{
        pickup_pincode: pickup_pincode,
        drop_pincode: drop_pincode,
        delivery_type: delivery_type,
        order_type: order_type,
        invoice_value: rdo_total,
        reference_number: reference_number
      }]
      begin
        response = get_response_for(:recommendation_path, payload).parsed_response.try(:deep_symbolize_keys)
        if response[:meta][:status] == 200 && (result = response[:result].try(:first)).present? && result[:preference_array].present?
          if delivery_type == 'FORWARD'
            rvp_available[reference_number] = result[:preference_array].sort_by{|i| i[:priority]}.map{|x| x[:cp_id]}.uniq
          else
            rvp_available[reference_number] = result[:preference_array].sort_by{|i| i[:priority]}.first.try(:[],:cp_id)
          end
        end
      rescue Net::ReadTimeout, Net::OpenTimeout, Timeout::Error => e
        if !reverse && response.blank? && times_retried < 3
          times_retried += 1
          retry
        end
      end
    rvp_available
  end

  def create_reverse_pickup
    self.return_order.line_items.preload(design: [:categories, :master_img], vendor_addon_items: [:addon_type_value], return_designer_order: [:designer]).group_by(&:return_designer_order).each do |rdo, items|
      next if rdo.shipment_id.present? || rdo.shipper_id.blank?
      designer = rdo.designer
      reference = "#{order.number}-#{rdo.id}"
      packer_id = order.user.try(:account).try(:id) || rdo.designer_id
      all_items, invoice_items, total_invoice_value = [], [], 0
      items.each do |item|
        item_value = item.reverse_shipment_value
        invoice_items << item_value
        total_invoice_value += item_value[:total_price]
        item_name  = return_order.reason == 'Wrong Product' ? 'Wrong Product - ' : ''
        item_name += item.design.categories.first.name.gsub('-', ' ').camelize
        item_name += ' [sku: ' + item.design.design_code + '] - ' if item.design.design_code.present?
        item_name += item.design.title
        all_items.push({product_url: "https://www.mirraw.com#{Rails.application.routes.url_helpers.designer_design_path(designer, item.design)}", price: item_value[:total_price], description: item_name, sku: (item.variant_id.present? ? item.variant_id : item.design_id), cat: item.design.categories.first.name.gsub('-', ' ').camelize, color: item.design.color.presence || item.design.get_colour_values.first, quantity: item.return_quantity, image_urls: [item.small_image]})
      end
      ################ Currently if all products are served by warehouse then only available_in_warehouse is set true.
      ################ But in future if this configuration is changed then drop address will have to be changed and two reverse pickups should be created accordingly.
      ################ Since All warehouse orders are received in default warehouse and we are dispatching from that quantity
      ################ Drop Address will be default warehouse.
      ################ if warehouse order are receiving at multiple locations then it should be change according to address of dispatched warehouse.  
      company_name, shipping_telephone, shipping_address_1,shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_billing_address(DEFAULT_WAREHOUSE_ADDRESS_ID)
      drop_details = if items.first.available_in_warehouse
        {
          drop_name: company_name,
          drop_phone: shipping_telephone.to_s,
          drop_address: "#{shipping_address_1} #{shipping_address_2}",
          drop_pincode: shipping_pincode,
          drop_city: shipping_city,
          drop_state: shipping_state,
          drop_country: 'IN'
        }
      else
        {
          drop_name: designer.business_name.presence || designer.name,
          drop_phone: designer.pickup_phone.presence || designer.phone,
          drop_address: (designer.business_street.presence || designer.street).gsub(/\r/,' ').gsub(/\n/,' ').gsub(',',' '),
          drop_pincode: designer.pincode,
          drop_city: designer.business_city.presence || designer.city,
          drop_state: designer.business_state.presence || designer.state,
          drop_country: 'IN'
        }
      end
      payload = {
        pickup_info: {
          pickup_name: order.name,
          pickup_phone: order.phone.to_s,
          pickup_address: order.street.gsub(/\r/,' ').gsub(/\n/,' ').gsub(',',' '),
          pickup_pincode: order.pincode,
          pickup_city: order.city,
          pickup_state: order.buyer_state,
          pickup_country: 'IN',
          pickup_time: 2.days.from_now.strftime('%Y-%m-%dT%H:%M:00Z'),
          email: order.email  ######### should we give user's email
        },
        drop_info: drop_details,
        shipment_details: {
          height: 10,
          length: 10,
          breadth: 10,
          weight: 10,
          order_type: 'PREPAID',
          invoice_number: reference,
          invoice_value: total_invoice_value.round,
          invoice_date: Date.today.strftime('%Y-%m-%d'),
          reference_number: reference,
          items: all_items,
          cod_value: 0,
          courier_partner: rdo.shipper.clickpost_shipper_id,
        },
        gst_info: {
          seller_gstin: designer.gst_no,
          is_seller_registered_under_gst: designer.gst_no.present?,
          place_of_supply: designer.business_state.presence || designer.state,
          enterprise_gstin: MIRRAW_GST_NUMBER
          # invoice_reference: rdo.designer_order.invoice_number
          # hsn_code: '1234', ######### doubt
          # gst_total_tax: total_tax,
        },
        additional: {
          label: true,
          async: false,
          rvp_reason: return_order.reason,
          delivery_type: 'RVP',
          # account_code: "ecom reverse", #required when multiple accounts of same courier exists
          # awb_number: '*********'
          qc_type: (return_order.reason.downcase.include?('damaged product')) || (return_order.reason.downcase.include?('wrong size')) || (return_order.reason.downcase.include?('quality issue')) ? DOORSTEP : nil
        }
      }
      

      response = get_response_for(:create_shipment_path, payload).parsed_response.try(:deep_symbolize_keys)
      if [200, 323].include?(response[:meta][:status]) && (result = response[:result]).present? && result[:waybill].present?
        shipment = ReverseShipment.new(
          number:           result[:waybill],
          shipper:          rdo.shipper,
          price:            total_invoice_value,
          weight:           10,
          order_id:         order.id,
          mirraw_reference: reference,
          automated:        true,
          track:            true,
          shipment_type:    'PREPAID',
          payment_state:    'unpaid',
          courier_label_url: result[:label],
          pickup_scheduled_on: 2.days.from_now,
          packer_id: packer_id,
          invoicer_id: packer_id
        )
        shipment.invoice_data[:security_key] = result[:security_key]
        shipment.save!
        # shipment.sidekiq_delay.download_label
        SidekiqDelayGenericJob.perform_async(shipment.class.to_s, shipment.id, "download_label")
        rdo.reverse_shipment = shipment
        rdo.assign_awb!
        # ShipmentsController.generate_invoice(invoice_items, order.number, shipment.id)
      else
        rdo.update_attributes(notes: "#{rdo.notes}, Pickup_Shipment_Error - #{response[:meta][:message]}")
      end
    end
  end

  def create_forward_pickup(dos_ids = [], pre_gereration: false)
    bulk_dispatch = dos_ids.present?
    dos = bulk_dispatch ? DesignerOrder.where(id: dos_ids).last : self.return_order
    unless dos.shipper_id.blank?
      is_cod,all_line_items,invoice_items,shipment,invoice_data,payload,reference,invoice_items_without_scale,invoice_data_without_scale = get_forward_payload(dos,order,dos_ids,bulk_dispatch)
      response = get_response_for(:create_shipment_path, payload).parsed_response.try(:deep_symbolize_keys)
      if response.present? && [200,323].include?(response[:meta][:status]) && (result = response[:result]).present? && result[:waybill].present?
        shipping_data = invoice_items.select{|ii| ii[:name] == 'Shipping Charges'}
        invoice_items.reject!{|ii| ii[:name] == 'Shipping Charges'}
        shipping_data.each do |sd|
          invoice_data[:gst_total]  -= sd[:gst_tax].to_f
          invoice_data[:total_taxable_value] -= sd[:taxable_value].to_f
          invoice_data[:item_total] -= sd[:item_discounted_price] * ( sd[:quantity] || 1)
        end
        if [323].include?(response[:meta][:status]) && (old_shipment = Shipment.where(number: result[:waybill]).first).present?
          shipment = old_shipment
        else
          packer_id = dos.designer.account.id
          shipment.number = result[:waybill]
          shipment.shipper_id = dos.shipper_id
          shipment.packer_id = packer_id
          shipment.invoicer_id = packer_id
          shipment.courier_label_url = result[:label]
          shipment.mirraw_reference = reference
          shipment.track = false
          shipment.automated = false
          shipment.payment_state = is_cod ? 'unpaid' : 'paid'
          shipment.invoice_data[:security_key] = result[:security_key]
          shipment.invoice_data['latest_reference'] = reference
          shipment.service_type = 'ClickpostForward'
          shipment.invoice_number = dos.invoice_number if bulk_dispatch
          all_line_items = dos.line_items.dispatchable unless bulk_dispatch
          if (order.try(:international?) || dos.ship_to == 'mirraw' || bulk_dispatch)
            shipment.inbound_line_item_ids = all_line_items.collect(&:id)
          end
        end
        if shipment.save
          if bulk_dispatch
            DesignerOrder.where(id: dos_ids).update_all(bulk_shipment_id: shipment.id, shipment_status: 'created', tracking_partner: dos.shipper.name, tracking_num: shipment.number, shipment_error: nil)
            # shipment.sidekiq_delay(queue: 'critical').move_designer_orders_to_dispatched(dos.shipper.name)
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(shipment.class.to_s, shipment.id, "move_designer_orders_to_dispatched", dos.shipper.name)
            shipment.make_purchase_order_entry(dos_ids, shipment.invoice_number, shipment.number, all_line_items.to_a.sum(&:quantity))
            ShipmentsController.sidekiq_delay(queue: 'critical').generate_bulk_invoice(invoice_items, shipment.id, dos.designer)
          else
            ShipmentsController.sidekiq_delay(queue: 'critical').generate_invoice(invoice_items_without_scale, order.number, shipment.id, invoice_data: invoice_data_without_scale)
            dos.tracking_partner = dos.shipper.name
            dos.tracking_num = shipment.number
            dos.shipment_status = 'created'
            dos.shipment_error = nil
            dos.save
            if dos.pickedup?
              dos.destroy_additional_shipments
              dos.add_notes("Shipment Reallocated to #{dos.tracking_partner}",true)
            else
              dos.pickedup! if dos.can_pickedup? && !pre_gereration
            end
            # DesignerMailer.sidekiq_delay_until(15.minutes.from_now)
            #               .mail_label_to_designer(dos) if !pre_gereration
            SidekiqDelayGenericJob.perform_in(15.minutes.from_now, "DesignerMailer", nil, "mail_label_to_designer", {dos.class.to_s => dos.id }) if !pre_gereration
          end
          # shipment.sidekiq_delay.download_label
          SidekiqDelayGenericJob.perform_async(shipment.class.to_s, shipment.id, "download_label")
        end
      elsif response.present? && [321,319].include?(response[:meta][:status])
        dos.shipper_id = nil
        add_notes = false
        if is_cod
          dos.failed_shipment("The shipment cannot be created for the pickup, please wait until we get back to you about the action we need to take for this order")
          add_notes = true
        elsif response[:meta][:message].downcase.include? 'pincode'
          dos.clickpost_serviceable = false
          dos.failed_shipment("The shipment cannot be created for the pickup, please self-dispatch the order with your preferred shipper and update the AWB number")
          add_notes = true
        else
          dos.failed_shipment("#{response[:meta][:message]}")
        end
        dos.add_notes_without_callback("#{response[:meta][:message]}",'ClickpostForward') if add_notes
        dos.skip_before_after_filter = true
        dos.assign_priority
        dos.save
      else
        dos.shipper_id = nil
        dos.assign_priority
        dos.failed_shipment(response.present? ? "#{response[:meta][:message]}" : 'No Response from Courier')
        dos.skip_before_after_filter = true
        dos.save
      end
    end
    rescue Net::ReadTimeout, Net::OpenTimeout, Timeout::Error => e
      dos.failed_shipment(e.message)
      dos.skip_before_after_filter = true
      dos.save
  end

  def create_replacement_shipment
    dos = self.return_order
    line_items = dos.line_items.dispatchable.where('rtv_quantity > ?', 0)
    unless dos.shipper_id.blank?
      is_cod,all_line_items,invoice_items,shipment,invoice_data,payload,reference,_,_ = get_forward_payload(dos,order,[],false,true)
      response = get_response_for(:create_shipment_path, payload).parsed_response.try(:deep_symbolize_keys)
      if response.present? && [200,323].include?(response[:meta][:status]) && (result = response[:result]).present? && result[:waybill].present?
        if [323].include?(response[:meta][:status]) && (old_shipment = RtvShipment.where(number: result[:waybill]).first).present?
          rtv_shipment = old_shipment
        else
          rtv_shipment = RtvShipment.new(
            number: result[:waybill],
            shipper_name: dos.shipper.name,
            weight: line_items.size/10.0,
            shipment_type: 'replacement'
          )
          rtv_shipment.line_items << line_items
        end
        if rtv_shipment.save
          dos.update_columns(recent_tracking_number: rtv_shipment.number,shipment_status: 'created')
          dos.replacement_pickedup! if dos.can_replacement_pickedup?
          label_file_name = "clickpost_forwards/rtv_shipment/#{rtv_shipment.id}_#{order.number}"
          label = open(result[:label])
          AwsOperations.create_aws_file(label_file_name, label, false)
          download_url = AwsOperations.get_aws_file_path(label_file_name)
          rtv_shipment.update_column(:label_url, download_url)
          s = dos.shipment
          if s.present? 
            s.invoice_data['latest_reference'] = reference
            s.save
          end
        else
          dos.shipper_id = nil
          dos.failed_shipment(response.present? ? "#{response[:meta][:message]}" : 'No Response from Courier')
          dos.skip_before_after_filter = true
          dos.save
        end
      end
    end
  end

  def get_forward_payload(dos,order,dos_ids=[],bulk_dispatch=false,is_rtv=false)
    designer = dos.designer
      if bulk_dispatch
        is_cod = false
        reference = "#{designer.id} - #{dos.invoice_number} - #{Date.today}"
      else
        is_cod = order.try(:cod?) && !order.try(:international?)
        reference = "#{order.try(:number)} - #{dos.id}"
        if dos.pickedup? || dos.replacement_pending?
          reallocation_count = 1
          if (s = dos.shipment || dos.bulk_shipment).present?
            previous_reference = s.invoice_data['latest_reference'].presence || s.mirraw_reference
            reallocation_count = previous_reference.split('-')[2].to_i + 1 if previous_reference.present?
          end
          reference = "#{reference} - #{reallocation_count}"
        end
      end
      all_line_items = []
      pickup_info = designer.get_order_pickup_details(dos,order)

      if (international = (order.try(:international?) || dos.ship_to == 'mirraw' || bulk_dispatch))
        company_name, shipping_telephone, shipping_address_1,shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_shipping_address(dos.warehouse_address_id)
        drop_address = "#{shipping_address_1} #{shipping_address_2}"
        drop_phone = shipping_telephone.to_s
        drop_country =  'IN'
        drop_state = shipping_state
        drop_pincode = shipping_pincode
        drop_city = shipping_city
        drop_name = company_name
        all_line_items,invoice_items,shipment = Shipment.generate_international_invoice_details((bulk_dispatch ? dos_ids : dos.id),bulk_dispatch)
        invoice_data = bulk_dispatch ? Shipment.calculate_domestic_invoice(nil, invoice_items, false, nil, designer) : Shipment.calculate_domestic_invoice(order, invoice_items, false, dos)
        invoice_items_without_scale = invoice_items
        invoice_data_without_scale = invoice_data
      else
        drop_address = order.street
        drop_phone = order.phone.to_s
        drop_country = 'IN'
        drop_state = order.buyer_state
        drop_pincode = order.pincode
        drop_city = order.city
        drop_name = order.name
        invoice_items, total, cod_amount, cod_charge, invoice_data, shipment = Shipment.generate_domestic_invoice_details(dos)
        invoice_items_without_scale,_,_,_,invoice_data_without_scale = dos.get_designer_order_item_details
      end
      all_items = []
      invoice_items.each do |item|
        all_items.push({sku: item[:sku], price: item[:total_price].round(2),quantity: item[:quantity], description: item[:name]})
      end
      payload =
      {
        pickup_info: {
          pickup_state: pickup_info[:state],
          pickup_address: pickup_info[:address].gsub(/\r/,' ').gsub(/\n/,' ').gsub(',',' '),
          email: pickup_info[:email],
          pickup_time: Time.now.hour < 13 ? (DateTime.now + 4.hours ).strftime('%Y-%m-%dT%H:%M:00Z') : Date.tomorrow.in_time_zone.change(hour: 11).strftime('%Y-%m-%dT%H:%M:00Z'),
          pickup_pincode: pickup_info[:pincode],
          pickup_city: pickup_info[:city],
          pickup_name: pickup_info[:name],
          pickup_country: pickup_info[:country_code],
          pickup_phone: pickup_info[:phone]
        },
        drop_info: {
          drop_address: drop_address.gsub(/\r/,' ').gsub(/\n/,' ').gsub(',',' '),
          drop_phone: drop_phone.to_s,
          drop_country: drop_country,
          drop_state: drop_state,
          drop_pincode: drop_pincode.to_s,
          drop_city: drop_city,
          drop_name: drop_name
        },
        shipment_details: {
          height: 12,
          order_type: is_cod ? "COD" : "PREPAID",
          invoice_value: invoice_data[:item_total].round(0),
          invoice_number: dos.invoice_number,
          invoice_date: Date.today.strftime('%Y-%m-%d'),
          reference_number: reference,
          length: 10,
          breadth: 10,
          weight: dos.shipper.name == "WowExpress" ? 500 : 100,
          items: all_items,
          cod_value: is_cod ? invoice_data[:item_total].round(0) : 0,
          courier_partner: dos.shipper.clickpost_shipper_id
        },
        gst_info: {
          seller_gstin: designer.gst_no.to_s,
          taxable_value: invoice_data[:total_taxable_value].round(2),
          is_seller_registered_under_gst: designer.gst_no.present?,
          place_of_supply: designer.business_state.presence.try(:upcase) || designer.state.try(:upcase),
          enterprise_gstin: MIRRAW_GST_NUMBER,
          gst_total_tax: invoice_data[:gst_total].round(2),
          invoice_reference: dos.invoice_number
        },

        additional: {
          label: true,
          return_info: {
            pincode: pickup_info[:pincode],
            address: pickup_info[:address],
            state: pickup_info[:state],
            phone: pickup_info[:phone],
            name: pickup_info[:name],
            city: pickup_info[:state],
            country: pickup_info[:country_code]
          },
          delivery_type: "FORWARD",
          async: false,
          gst_number: MIRRAW_GST_NUMBER
        }
      }
      if (account_code_name = get_account_code_for_shipper(dos.shipper.name, international, dos)).present?
        payload[:additional].merge!(:account_code =>  account_code_name)
      end
      return [is_cod,all_line_items,invoice_items,shipment,invoice_data,payload,reference,invoice_items_without_scale,invoice_data_without_scale]
  end

  def get_account_code_for_shipper(shipper_name, international, designer_order)
    account_code = nil
    if shipper_name.to_s.downcase == 'xpress bees'
      account_code = international ? 'XpressBees International' : 'Xpressbees Domestic'
    elsif shipper_name.to_s.downcase == 'rapid delivery'
      account_code = international ? 'Rapid International First Mile' : 'Rapid Domestic'
    elsif shipper_name.to_s.downcase == 'delhivery'
      account_type = DISABLE_ADMIN_FUCTIONALITY['delivery_account_selection'] ? 'surface' : get_delhivery_account_type(designer_order)
      account_code = DELHIVERY_CP_ACC_NAME[account_type]
    elsif shipper_name.to_s.downcase == 'smartr'
      account_code = international ? 'Smartr B2B' : 'smartr'
    end
    return account_code
  end

  def get_delhivery_account_type(designer_order)
    type = is_order_essential(designer_order) ? (is_order_under_weight(designer_order) ? 'surface' : 'non_surface') : 'surface'
  end

  def is_order_essential(designer_order)
    all_categories = designer_order.line_items.not_canceled.map{|li| li.categories.pluck(:name)}.flatten.uniq
    (all_categories & ESSENTIAL_CATEGORIES).length == all_categories.length
  end

  def is_order_under_weight(designer_order)
    weight = designer_order.line_items.not_canceled.sum{|item| item.approx_weight('IN').to_i }
    weight < DELHIVERY_CP_ACC_NAME['weight']
  end

  def create_tracking_for_international(shipment_info, invoice_items)
    response={}
    all_items = []
    invoice_items.each do |item|
      all_items.push({price: item[:total_price].round(2),quantity: item[:quantity], description: item[:name]})
    end
    _, _, _, _, _, shipping_pincode, _, _ = order.get_warehouse_shipping_address
    payload = 
    {
      waybill: shipment_info.number,
      cp_id: shipment_info.shipper.clickpost_shipper_id,
      key: request_credentials[:key],
      consumer_details: {
        name: order.name,
        phone: order.phone,
        email: order.email
      },
      shipment_info: {
        order_type: order.cod? ? 'COD':'PREPAID',
        invoice_value: shipment_info.price,
        reference_number: shipment_info.mirraw_reference,
        length: 10,
        height: 10,
        weight: 100,
        breadth: 10,
        drop_pincode: order.pincode,
        pickup_pincode: shipping_pincode,
        delivery_type: 'FORWARD',
        cod_amount: order.cod? ? shipment_info.price : 0,
        drop_address: order.street.gsub(/\r/,' ').gsub(/\n/,' ').gsub(',',' ')+", "+order.city,
        additional: {
          items: all_items
        }
      },
      additional: {
        order_date: order.created_at.strftime('%Y-%m-%d'),
        ship_date: shipment_info.created_at.strftime('%Y-%m-%d')
      }
    }
    response = get_response_for(:tracking_path, payload).parsed_response.try(:deep_symbolize_keys)
    if [200, 303].include?(response[:meta][:status])
      if (result = response[:result]).present? && result[:tracking_id].present?
        shipment_info.invoice_data[:cp_track_tracking_id] = result[:tracking_id]
        shipment_info.invoice_data[:cp_track_security_key] = result[:security_key]
      end
      shipment_info.track = false
      shipment_info.save
    end
  rescue => e
    shipment_info.update_column(:track, true)
    ExceptionNotify.sidekiq_delay.notify_exceptions('CP tracking issue', e.message,{ error: (response[:meta].try([],:message)).to_s, order_number: shipment_info.mirraw_reference, waybill: shipment_info.number})
  end

  def cancel_shipment(shipment)
    if shipment.shipper.clickpost_shipper_id.present?
      response = get_response_for(:cancel_order_path, {}, {additional_url_params: {'waybill'=>shipment.number, 'cp_id'=> shipment.shipper.clickpost_shipper_id}}, :get).parsed_response.try(:deep_symbolize_keys)
      if response[:meta].present?
        shipment.shipment_canceled if response[:meta][:success]
        return {success: response[:meta][:success], msg: response[:meta][:message]}
      else
        return {success: false, msg: 'Clickpost provided no response'}
      end
    else
      return {success: false, msg: 'Shipment not found on clickpost'}
    end
  end

  private

  def get_request_url(path_key: nil, options: {})
    request_url = request_credentials[path_key] + "?username=#{request_credentials[:username]}&key=#{request_credentials[:key]}"
    options[:additional_url_params].to_h.each do |header_name, value|
      request_url += "&#{header_name}=#{value}"
    end
    request_url
  end

end