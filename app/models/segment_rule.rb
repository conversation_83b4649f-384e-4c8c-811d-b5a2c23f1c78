class SegmentRule < ActiveRecord::Base
    acts_as_taggable_on :segments  # e.g., gold, silver, first_time_user
    has_many :segment_rule_conditions, dependent: :destroy, inverse_of: :segment_rule

    has_many :user_segment_rules, dependent: :destroy
    has_many :users, through: :user_segment_rules
    
    validates :name, presence: true, uniqueness: true
  
    scope :active, -> { where(active: true) }  
  end
  