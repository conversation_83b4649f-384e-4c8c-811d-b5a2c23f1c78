# == Schema Information
#
# Table name: designer_orders
#
#  id                     :integer         not null, primary key
#  email_sent             :boolean
#  sms_sent               :boolean
#  pickup                 :datetime
#  designer_id            :integer
#  order_id               :integer
#  created_at             :datetime
#  updated_at             :datetime
#  shipping               :integer
#  state                  :string(255)
#  total                  :integer
#  payout                 :integer
#  tracking               :text
#  tracking_num           :string(255)
#  delivered_at           :datetime
#  discount               :integer
#  designer_payout_status :string(255)
#  designer_payout_date   :datetime
#  designer_payout_notes  :string(255)
#  scaled_total           :integer
#

class DesignerOrder < ActiveRecord::Base
  include LocalyticsNotification
  include SidekiqHandleAsynchronous
  include MobileGenericSidekiqConcern
  has_paper_trail
  acts_as_taggable
  scope :sane, -> { where(state: [:pending, :scheduled, :dispatched, :completed, :pickup_done] ) }
  scope :designer_orders_in_three_months, -> { where(confirmed_at: 3.months.ago..DateTime.current) }
  TRACKING_PARTNER = ['Bluedart', 'Fedex', 'Aramex', 'India Post', 'Ship Delight']
  MAX_RETURNABLE_DAYS = SystemConstant.get('DESIGN_ORDER_RETURN_DAYS').to_i
  CANCEL_ORDER_REASONS = [
    'Out of Stock',  'Vendor canceled', 'Operations call', 'Customer Requested', 'Tailor Issue',
    'Quality Issue', 'Critical Issue',  'Warehouse Issue', 'SLA violation', 'Plus size product'
  ]
  NO_DESIGNER_EVENT_REASONS = ['Customer Requested', 'Tailor Issue', 'Quality Issue', 'Critical Issue', 'Warehouse Issue', 'Plus size product']
  EVENTS_ON_LINEITEM = ['RefundOrDiscountInitiated','IncorrectProduct','DamagedItem','ItemMissing','DesignOOS','NegativeFeedback','SizeIssue','BadMaterial','OrderCanceled']

  scope :package_not_received, -> { where(package_received_on: nil, package_received_by: nil) }
  scope :package_received,     -> { where('designer_orders.package_received_on IS NOT NULL and designer_orders.package_received_by IS NOT NULL') }
  scope :package_states,       -> { where("designer_orders.state NOT IN ('canceled', 'buyer_returned')") }
  scope :sane_orders,          -> { where(state: ['pending', 'dispatched', 'completed'] ) }

  has_one :shipment
  belongs_to :bulk_shipment, class_name: 'Shipment', foreign_key: 'bulk_shipment_id'
  has_one :designer_invoice
  belongs_to :initial_payout, class_name: 'PayoutManagement', foreign_key: 'payout_management_id'
  belongs_to :gst_release_payout, class_name: 'PayoutManagement', foreign_key: 'release_payout_management_id'
  belongs_to :designer
  belongs_to :order
  belongs_to :payment_order, -> {select('orders.id,orders.number,orders.created_at,orders.name,orders.country,orders.buyer_state,orders.confirmed_at,orders.state,orders.pay_type,orders.email,orders.express_delivery, orders.actual_country_code, orders.city')}, class_name: 'Order', foreign_key: 'order_id'
  belongs_to :coupon
  has_many :line_items do
    def not_canceled
      select{|lineitem| lineitem.status != 'cancel'}
    end
  end
  has_many :designs, through: :line_items
  belongs_to :invoice,            polymorphic: true
  belongs_to :purchase_report,    foreign_key: :invoice_id
  belongs_to :commission_invoice, foreign_key: :invoice_id
  belongs_to :shipper
  has_many :return_designer_orders
  has_many :events, as: :eventable
  belongs_to :priority_shipper, class_name: 'Shipper'
  belongs_to :priority_shipper_cod, class_name: 'Shipper'
  has_one :delivery_nps_info, as: :element
  has_many :reverse_commissions, as: :element
  has_many :rtv_shipments, through: :line_items
  has_many :adjustments

  default_scope { order('designer_orders.id DESC') }
  before_save :update_fields_trigger, unless: :skip_before_after_filter
  after_update :update_order_fields_trigger, unless: :skip_before_after_filter
  after_save :update_design_quantity, if: -> (designer_order){ designer_order.newly_added_items.present?}
  after_commit do |designer_order|
    designer_order.newly_added_items.clear
  end
  validate :freeze_payout
  belongs_to :rack_list
  belongs_to :inward_bag
  belongs_to :purchase_order
  belongs_to :warehouse_address

  extend DynamicTemplateMethod
  dynamic_template_fields :pickup ,:created_at ,:updated_at ,:shipping ,:state ,:total ,:payout ,:delivered_at ,:discount ,:designer_payout_status ,:designer_payout_date ,:designer_payout_notes ,:tracking_partner ,:transaction_rate ,:in_critical_at ,:package_received_on ,:package_status ,:confirmed_at ,:shipment_status ,:ship_to ,:completed_at ,:recent_tracking_number

  attr_accessor :skip_after_transition
  attr_accessor :skip_before_after_filter
  attr_accessor :skip_order_callbacks
  # input params - none
  #
  # Returns string

  def newly_added_items
    @newly_added_items ||= {}
  end

  def set_ship_to
    order = self.order
    international_paid = (!order.international? && !order.billing_international? && order.actual_country_code.try(:downcase) != 'in' && !order.cod?) || (order.country.downcase == 'india' && order.billing_international?)
    temp_ship_to = (order.international? || international_paid || self.mirraw_payable_addons?) ? 'mirraw' : 'customer'
    temp_ship_to = 'customer' if (RAKHI_DELIVER_INT == 'true' && self.designer.name.try(:downcase) == 'aapno rajasthan' && self.line_items.map{|l| l.design.categories.collect(&:name)}.flatten.include?('rakhi-international'))
    if(!order.international? && self.check_stitching_or_jewellery_des_order == 'stitching')
      temp_ship_to =  'customer' 
    end
    self.update_attribute('ship_to', temp_ship_to)
    self.set_logistic_weight unless DISABLE_ADMIN_FUCTIONALITY['logistic_feature']
    self.set_logistic_zone unless DISABLE_ADMIN_FUCTIONALITY['logistic_feature']
    # update_shipping_cost if order.domestic?
  end

  def set_logistic_weight
    weight = []
    self.line_items.each do |li|
      weight_arr = DesignShipperWeight.get_design_logistic_weight(li.design_id)
      weight << (weight_arr.present? ? weight_arr.min : 0.0)
    end
    unless weight.include?(nil) || weight.include?(0.0)
      self.update_column(:weight, weight.sum)
    end
  end
  
  def set_logistic_zone
    pickup_pincode = self.designer.pincode.presence || self.designer.business_pincode
    if self.ship_to == 'mirraw'
      _,_,_,_,_, drop_pincode,_,_= self.order.get_warehouse_shipping_address
    else
      drop_pincode = self.order.pincode
    end
    zone = Pincode.get_logistic_zone(pickup_pincode, drop_pincode)
    self.update_column(:zone, zone) if zone.present?
  end

  # def update_shipping_cost
  #   shipping_hash = Cart.get_essential_designers_total(line_items)
  #   if shipping_hash[self.designer.id] != 0 && shipping_hash[self.designer.id] < ESSENTIAL_DESIGNERS["total_below_x"].to_i
  #     self.update_attribute(:mirraw_shipping_cost, ESSENTIAL_DESIGNERS["shipping_cost"].to_i)
  #   end
  # end

  def set_warehouse_address

    all_dos = self.order.designer_orders

    if self.order.app_name == 'luxe'
      address_id = LUXE_WAREHOUSE_ADDRESS_ID # warehouse address id 3 is for lower parel for luxe orders 
    elsif  all_dos.count > 1
      address_id = DEFAULT_WAREHOUSE_ADDRESS_ID
    else
      is_sor_order = self.line_items.all? { |item| item.available_in_warehouse}

      is_inhouse_vendor = INHOUSE_VENDOR_IDS.include? self.designer_id.to_s

      is_order_stitching = self.check_stitching_or_jewellery_des_order

      contains_stitching = is_order_stitching == 'stitching'

      address_id = (is_sor_order || is_inhouse_vendor || contains_stitching) ? DEFAULT_WAREHOUSE_ADDRESS_ID : self.designer.warehouse_address_id

      
    end
    address_id = DEFAULT_WAREHOUSE_ADDRESS_ID unless address_id.present?

    all_dos.each do |dos|
      dos.assign_attributes(warehouse_address_id: address_id)
    end
  end

  #foolish Dumb method not sure about param type. can be array and can be single integer.  
  def self.get_warehouse_shipping_address(w_a_ids)
    if (wa = WarehouseAddress.where(id: w_a_ids).first).present?
      return[wa.company_name, wa.shipping_phone, wa.shipping_address_line_1, wa.shipping_address_line_2, wa.shipping_city, wa.shipping_pincode, wa.shipping_state, wa.shipping_state_code]
    else
      return [COMPANY_NAME, SHIPPING_TELEPHONE, SHIPPING_ADDRESS, SHIPPING_ADDRESS_LINE_2, SHIPPING_CITY, SHIPPING_PINCODE, SHIPPING_STATE, SHIPPING_STATE_CODE]
    end
  end

  def self.get_warehouse_billing_address(w_a_ids)
    if (wa = WarehouseAddress.where(id: w_a_ids).first).present?
      return[wa.company_name, wa.phone, wa.address_line_1, wa.address_line_2, wa.city, wa.pincode, wa.state, wa.state_code]
    else
      return [COMPANY_NAME, SHIPPING_TELEPHONE, SHIPPING_ADDRESS, SHIPPING_ADDRESS_LINE_2, SHIPPING_CITY, SHIPPING_PINCODE, SHIPPING_STATE, SHIPPING_STATE_CODE]
    end
  end

  def update_designer_score
    designer.update_per_day_sale_score if designer && !designer.try(:score_flag)
  end
  handle_asynchronously :update_designer_score

  def update_invoice_number
    self.update_column(:invoice_number,self.designer.get_invoice_number)
  end

  def update_design_quantity
    newly_added_items.each do |item, quantity|
      item.update_item_quantity('decrease', quantity)
    end
  end

  def mark_package_received(name)
    self.package_received_on = DateTime.current
    self.package_received_by = name
  end

  # input params - none
  # Please keep logic consistent with cart method - same named
  #
  # Return true if DesignerOrder has LineItem with addons
  def mirraw_payable_addons?
    addons = LineItemAddon.joins(:line_item).where(:line_item => {:designer_order_id => self.id}, :snapshot_payable_to => 'mirraw').count(:id)
    addons > 0 ? true : false
  end

  def get_scaled_discount
    if scaled_discount.present?
      scaled_discount
    elsif discount.present?
      # for old data without scaled discount
      discount
    else
      0
    end
  end

  def get_item_total
    self.line_items.to_a.sum do |li|
      item = li.get_replacement_product
      (item.snapshot_price * item.quantity)
    end.to_f
  end

  def get_return_item_total
    self.line_items.where(status: "buyer_return").map{|li|(li.snapshot_price * li.return_quantity)}.sum.to_f
  end


  def ignore_states
    %w(canceled buyer_returned)
  end

  def scaled_total
    if self[:scaled_total].to_i != 0
      self[:scaled_total]
    else
      self[:total]
    end
  end

  def update_fields_trigger
    if self.state == "canceled"
      self.total = 0
    else
      all_line_items = self.new_record? ? line_items : line_items.sane_items
      total_change=0
      scaled_total_change=0
      all_line_items.each do |lt|
        total_change += lt.line_item_addons.select{|addon| addon.snapshot_payable_to =='designer'}.sum{ |addon| addon.snapshot_price(RETURN_NORMAL)} * lt.quantity
        scaled_total_change += lt.line_item_addons.select{|addon| addon.snapshot_payable_to =='designer'}.sum{ |addon| addon.snapshot_price(RETURN_SCALED)} * lt.quantity
      end
      self.total = all_line_items.to_a.sum{|li| li.sub_total(RETURN_NORMAL)}
      self.scaled_total = all_line_items.to_a.sum(&:sub_total).to_i
      self.total += total_change
      self.scaled_total += scaled_total_change
      self.total -= self.discount if self.discount.present?
      self.scaled_total -= self.get_scaled_discount
    end
    self.shipping = (self.total > MIN_TOTAL_PER_STORE || self.total <= 0) ? 0 : SHIPPING_CHARGE
    if self.designer_payout_status != 'paid' && self.designer_payout_notes.blank?
      self.transaction_rate = get_transaction_rate if self.transaction_rate.blank? || self.designer.present?

      vendor_payout, _, _, _ = get_vendor_payout
      if self.mirraw_shipping_cost.present?
        self.payout = vendor_payout - self.mirraw_shipping_cost
      else
        self.payout = vendor_payout
      end
    end
    if self.is_on_global_sale_period?
      note_text = 'Mirraw Discount Applied'
      if (self.order[:notes] =~ /#{note_text}/).blank?
        self.order.add_notes(note_text, false)
      end
      if (self[:notes] =~ /#{note_text}/).blank?
        self.add_notes(note_text, false)
      end
    end

    return true
  end

  def is_on_global_sale_period?
    if (sale = discount_promotion).present?
      country_code = sale.country_code ? sale.country_code : ''
      Time.zone.now.to_i.between?(Time.parse(sale.start_date.to_s).to_i,
      Time.parse(sale.end_date.to_s).to_i) && country_code.split(',').include?(self.order.actual_country_code)
    else
      false
    end
  end

  def create_shipping_adjustment(amount)
    order  = self.payment_order
    is_new_record = false
    if self.designer_payout_status == "paid" || self.designer_payout_notes.present? || self.state == "buyer_returned" || self.state == "rto" || (order.pay_type == COD && self.state == 'canceled')
      # Create Adjustment for the order
      adj = Adjustment.where(amount: -1 * amount,designer_id: self.designer_id,order_id: order.id, designer_order_id: self.id).first_or_initialize if self.designer.adjustment_allowed? && self.ellgible_for_shipping?
      if adj.present?
         adj.notes  =  "Shipping Cost for Order #{order.number}"
         adj.status =  'unpaid'
         is_new_record = adj.new_record?
         adj.save
      end
    elsif ellgible_for_shipping?
      # Add it in designer order only
      is_new_record = self.mirraw_shipping_cost.blank?

      vendor_payout, _, _, _ = self.get_vendor_payout
      self.payout = vendor_payout - amount
      self.update_columns(mirraw_shipping_cost: amount, payout: self.payout)
    end
    [adj, is_new_record]
  end

  def ellgible_for_shipping?
    line_items.any?{|item| !item.available_in_warehouse}
  end

  def get_vendor_payout
    item_total = 0
    sor_item_total = 0

    item_selling_price = 0
    item_commission = 0

    unless self.state == "canceled"
      (self.new_record? ? self.line_items : self.line_items.sane_items).each do |item|
        if item.sor_available?
          sor_item_total += item.vendor_selling_price * item.quantity
        else
          item_total += item.vendor_selling_price.to_f*item.quantity
        end
        item_total += item.line_item_addons.select{|li| li.snapshot_payable_to == 'designer' }.sum{|addon| addon.sub_total(RETURN_NORMAL)}
      end
    end
    item_selling_price = item_total
    payout = if (item_total + sor_item_total) > 0
      if (v_discount = self.discount).present?
        item_selling_price = item_selling_price - v_discount
      end
      item_commission = item_selling_price * self.transaction_rate/100
      item_total = item_selling_price - item_commission
      final_vendor_payout = (item_total + sor_item_total)
    elsif designer.is_transfer_model?
      final_vendor_payout = item_total + sor_item_total
    else
      discount_amt = (100 - self.promotion_discount)
      item_selling_price = self.total * 100 / discount_amt
      item_commission = self.total * self.transaction_rate / discount_amt
      final_vendor_payout = item_selling_price - item_commission
    end

    [final_vendor_payout, sor_item_total, item_selling_price, item_commission]
  end

  def get_payout_ratio
    if self.state == "canceled"
      self.total = 0
    elsif self.payout!=0
      @payout_ratio||=self.payout/((self.total + self.discount.to_i)*1.0)
    else
      1
    end
  end

  def self.update_vendor_payout do_ids
    @designer_orders = DesignerOrder.where(id: do_ids)
    @designer_orders.each do |designer_order|
      payout = designer_order.transaction_rate.to_i > 0 ? (designer_order.total * (100 - designer_order.transaction_rate) / (100 - sale_discount_percent.to_i)) : designer_order.line_items.sum(&:vendor_selling_price)
      if designer_order.mirraw_shipping_cost.present?
        payout -= designer_order.mirraw_shipping_cost
      end
      note_text = "Vendor Payout Updated By System"
      if (designer_order.notes =~ /#{note_text}/).blank?
        designer_order.add_notes_without_callback(note_text, 'vendor_payout')
      end
      designer_order.update_column(:payout, payout.ceil)
    end
  end

  def apply_bulk_shipment_invoice(state)
    DesignerOrder.preload(:designer_invoice).where(bulk_shipment_id: self.bulk_shipment_id).where.not(id: self.id).each do |dos|
      if state == 'invoice_uploaded' && dos.designer_invoice.blank?
        dos.build_designer_invoice(from_date: Date.today.beginning_of_month, to_date: Date.today.end_of_month, invoice_file: self.designer_invoice.invoice_file, designer_id: dos.designer_id)
        dos.skip_order_callbacks = true
        dos.designer_uploaded_invoice if dos.can_designer_uploaded_invoice?
      elsif state == 'completed'
        dos.skip_order_callbacks = true
        dos.mark_for_payout if dos.can_mark_for_payout?
        dos.payment_from_buyer if dos.can_payment_from_buyer?
      end
    end
  end

  def self.download_csv(designer_order_ids,email,status)
    attributes  = %w(confirmed_at state pickup tracking_partner tracking_num designer_payout_status designer_payout_date )
    order_attr  = %w(number)
    design_attr = %w(title design_code)
    item_attr   = %w(quantity note qc_status)
    warehouse_attr = %w(available_in_warehouse combo_variant_note)
    others      = status ? %w(promised_date days_past_promise ship_city ship_state pincode street) : []
    file=CSV.generate(headers: true) do |csv|
      csv << order_attr+attributes+design_attr+item_attr+others+warehouse_attr
      DesignerOrder.where(id: designer_order_ids).preload(:order,:shipment,line_items: [:design]).find_in_batches(batch_size: 200) do |dos_grp|
        dos_grp.each do |designer_order|
          _, _, shipping_address_1, _, shipping_city, shipping_pincode, shipping_state, _  = DesignerOrder.get_warehouse_billing_address([designer_order.warehouse_address_id])
          order=designer_order.order
          next if order.blank?
          designer_order.line_items.each do |item|
            all_values = []
            all_values += order.attributes.values_at(*order_attr)
            all_values += designer_order.attributes.values_at(*attributes)
            all_values += item.design.attributes.values_at(*design_attr)
            all_values += item.attributes.values_at(*item_attr)
            if status
              all_values += [(designer_order.confirmed_at || order.confirmed_at || designer_order.created_at).advance(days: 2).strftime('%d %B %Y')]
              all_values += [(Date.today - designer_order.created_at.to_date).to_i]
              if (designer_order.ship_to.blank? && order.international?) || (designer_order.ship_to == 'mirraw')
                all_values += [shipping_city,shipping_state,shipping_pincode,shipping_address_1]
              else
                all_values += [order.city,order.buyer_state,order.pincode.to_s,order.street]
              end
            end
            all_values << item.available_in_warehouse ? ['warehouse order'] : nil
            all_values << item.combo_variant_option_details if item.combo_variant.present?
            csv << all_values
          end
        end
      end
    end
    OrderMailer.report_mailer('Order details','Please find below attachment',{'to_email'=>email,'from_email_with_name'=>"Mirraw.com <<EMAIL>>"},{'Order Detail.csv' => file}).deliver
  end

  def self.download_quality_report(designer_id,date_range,headers,email)
    li_headers,des_headers = headers.partition{|h| EVENTS_ON_LINEITEM.include?(h)}
    file = CSV.generate(headers: true) do |csv|
      csv << ['Order Number'] + li_headers + des_headers
      DesignerOrder.where(designer_id: designer_id).where('order_id is not null').where(created_at: date_range).preload(:payment_order,line_items: :design).eager_load(:scope_events,line_items: :scope_events).find_in_batches(batch_size: 200) do |dos_grp|
        dos_grp.each do |des_order|
          next unless des_order.payment_order.present?
          row,events_occurred = [],Hash.new([])
          row = [des_order.payment_order.number]
          line_items = des_order.line_items
          des_events = des_order.scope_events.collect(&:name)
          line_items.each do |item|
            events = item.scope_events.collect(&:name)
            li_headers.collect{|h| events_occurred[h] += (events.include?(h) || des_events.include?(h) ? [item.design.title.to_s + ' : ' + item.design.design_code.to_s] : [nil])}
          end
          events_occurred.each {|_,value| row += [value.compact.join(',')]}
          events_occurred = des_headers.collect{|h| [h, des_events.include?(h) ? ['YES'] : [''] ]}.to_h
          events_occurred.each {|_,event| row += event}
          csv << row
        end
      end
    end
    OrderMailer.report_mailer('Order Quality Report','Please find below attachment',{'to_email'=>email,'from_email_with_name'=>"Mirraw.com <<EMAIL>>"},{'Order Quality Report.csv' => file}).deliver
  end

  def update_order_fields_trigger
    unless skip_order_callbacks
      self.order.check_items
      self.order.touch_with_callbacks
    end
    return true
  end

  def update_line_item_discount_price(order)
    discount_local = self.discount.present? ? self.discount : 0
    # Add order discount to discount_local in propotion to designer_order_total and order_total

    do_total_before_discount_local = self.total + discount_local

    order_total_local = order.total + order.discount.to_i - (order.cod_charge.to_i + order.shipping.to_i)

    order_discount_percent_local = order.discount.to_i / order_total_local.to_f
    order_discount_do_local = do_total_before_discount_local * order_discount_percent_local
    discount_local += order_discount_do_local

    discount_percent_local = do_total_before_discount_local > 0 ? (discount_local / do_total_before_discount_local.to_f) : 0

    discount_provided_local = 0

    line_items.sane_items.each do |lt|
      # Using update attribute to avoid line item and line item addon callback
      # Discount price for line item
      item_snapshot_discount_price = (lt.snapshot_price(RETURN_NORMAL) * (1 - discount_percent_local)).floor
      item_snapshot_discount_price = 0 if item_snapshot_discount_price < 0

      lt.update_attribute('snapshot_discount_price', item_snapshot_discount_price)

      discount_provided_local += ((lt.snapshot_price(RETURN_NORMAL) - lt.snapshot_discount_price) * lt.quantity)

      lt.line_item_addons.each do |lt_addon|
        item_percent_value = lt_addon.snapshot_payable_to == 'designer' ? discount_percent_local : order_discount_percent_local
        item_addon_snapshot_discount_price = (lt_addon.snapshot_price * (1 - item_percent_value)).floor
        item_addon_snapshot_discount_price = 0 if item_addon_snapshot_discount_price < 0
        lt_addon.update_attribute('snapshot_discount_price', item_addon_snapshot_discount_price)
        discount_provided_local += ((lt_addon.snapshot_price - lt_addon.snapshot_discount_price) * lt.quantity)
      end
    end

    DesignerOrder.where(:id => self.id).update_all(:discount_provided => discount_provided_local)
  end

  def completed?
    state == "completed"
  end

  # Here you are decrementing quantity of design.
  # What happens when decrementing quantity of some
  # other design fails ?
  #
  def add_line_items(cart, items, bmgnx_offer_line_items_hash = {})
    bmgnx_hash = PromotionPipeLine.bmgnx_hash
    order = self.order
    items.each do |item|
      if DESTROY_CART
        item.cart_id = nil
      end
      if bmgnx_hash.present? && item.buy_get_free == 1 && (bmgnx_value = bmgnx_offer_line_items_hash[item.id]).present?
        item.add_note("| Discount items (B#{bmgnx_hash[:m]}G#{bmgnx_hash[:n]}): #{bmgnx_value[1]} At #{bmgnx_hash[:x]} % off |", ignore_if_exists: true)
      end
      item.assign_synced_attributes
      item.vendor_selling_price = (item.variant || item.design).get_vendor_selling_amount
      line_items << item
      if RESPECT_QUANTITY
        # will update quantity in after save
        newly_added_items[item] = item.quantity
      end
      SalesRegister.sidekiq_delay(queue: 'high')
                   .create_new_entry('line_item_added', 'sales',
                     SalesRegister.object_to_json([order,self,item])
                   ) if ['new','pending','confirmed','cancel'].exclude? order.state  
    end
  end

  def post_order_design_quantity_reduce
    change_lineitem_design_quantity unless designer.try(:is_unicommerce_vendor)
  end

  def change_lineitem_design_quantity
    if RESPECT_QUANTITY
      self.line_items.each do |line_item|
        line_item.update_item_quantity('decrease', line_item.quantity)
      end
    end
  end

  def remove_line_item(item)
    order = self.order
    item.status = 'cancel'
    item.stitching_required = nil
    item.canceled_on = Time.current
    if item.save!
      # Issue
      SidekiqDelayGenericJob.perform_async("ScopeScoreEvent", nil,"apply_remove_cancel_event", {"LineItem": item.id}) if (CANCEL_ORDER_REASONS - NO_DESIGNER_EVENT_REASONS).include?(item.cancel_reason)
      #ScopeScoreEvent.sidekiq_delay.apply_remove_cancel_event(item) if (CANCEL_ORDER_REASONS - NO_DESIGNER_EVENT_REASONS).include?(item.cancel_reason)
      SidekiqDelayGenericJob.set(queue: 'high').perform_async("ReverseCommission", nil,"add_reverse_commission", {"LineItem": item.id}) if item.try(:designer_order).try(:confirmed_at).present?
      #ReverseCommission.sidekiq_delay(queue: 'high_priority',priority: -4).add_reverse_commission(item) if item.try(:designer_order).try(:confirmed_at).present?
      SidekiqDelayGenericJob.perform_async("OrderMailer", nil,"convey_cancel_update_to_designer", {"DesignerOrder": self.id}, {"LineItem": item.id}) if self.pickup.present?
      #OrderMailer.sidekiq_delay.convey_cancel_update_to_designer(self, item) if self.pickup.present?
      self.create_adjustment_for_cancelation(item.id) if self.designer_payout_status == "paid" || self.designer_payout_status == "processing" || self.designer_payout_notes.present?
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id,"check_if_all_items_dispatched?", true) if order.partial_dispatch?
      #order.sidekiq_delay
      #     .check_if_all_items_dispatched?(true) if order.partial_dispatch?
      item.create_searcher_entity_for_item(search_type: 'RtvSearcherEntity') if is_package_in_rack? && item.received_on.present? && item.qc_status      
    end
    item.warehouse_cancel_item(item.variant || item.design)
    SalesRegister.sidekiq_delay(queue: 'high')
                 .create_new_entry(
                   'line_item_cancel','sales_return',
                   SalesRegister.object_to_json([order,self,item])
                 ) if (['new','pending','confirmed','cancel'].exclude?(order.state) && ['buyer_returned','vendor_canceled'].exclude?(self.state))
    SidekiqDelayGenericJob.set(queue: 'critical').perform_async(order.class.to_s, order.id,"mark_order_as_stitching")
    #order.sidekiq_delay(queue: 'critical').mark_order_as_stitching
    # line_items.delete(item)
    # item.destroy
  end

  def cancel_designer_order_or_item(cancel_reason, line_item: nil)
    canceled = false
    if order.line_items.sane_items.count == 1
      order.order_cancel_reason = cancel_reason if order.can_cancel?
      canceled = order.cancel!
    elsif self.line_items.sane_items.count == 1
      self.cancel_reason = cancel_reason
      canceled = self.cancel!
      # order.save! if canceled
    elsif line_item.present?
      line_item.cancel_reason = cancel_reason
      self.remove_line_item(line_item)
      canceled = self.save!
      # order.save! if canceled
    end
    canceled
  end

  def cancelling_designer_order_or_item(cancel_reason, line_item)
    self.cancel_designer_order_or_item(cancel_reason, line_item: line_item)
  end

  def update_line_item(item, quantity)
    if quantity > 0 && RESPECT_QUANTITY
      product = item.variant || item.design
      if item.available_in_warehouse?
        item.update_warehouse_quantity(quantity,product)
      elsif quantity <= (item.quantity + product.quantity)
        item.update_item_quantity('increase', (item.quantity - quantity))
        item.quantity = quantity
        item.save!
      end
    end
  end

  def touch_with_callbacks
    self.updated_at = DateTime.now # Updating so that save callbacks gets called
    self.save!
  end

  def get_transaction_rate
    if self.designer.try(:transaction_rate).present?
      order.international? ? self.designer.transaction_rate : self.designer.domestic_transaction_rate
    else
      TRANSACTION_RATE
    end
  end

  def get_commission_factor
    transaction_rate.to_f > 0 ? (transaction_rate.to_f / 100) : 1
  end

  def commission_amount
    if transaction_rate.present? && transaction_rate > 0
      total_commission = (transaction_rate/100 * total)
    else
      final_rate = designer.transfer_model_rate * (1 + IGST / 100.0)
      total_commission = (final_rate/100 * total)
    end
  end

  def state_enum
    DesignerOrder.state_machine.states.map &:name
  end

  #                                            |--> reject (COD rejection)
  #                                            |
  #   pending --> scheduled_for_pickup --> dispatched --> completed
  #     | --> rejected (buyer/designer calls up and says cancel the order)
  #     | --> fraud
  #
  #  State will be restored in [:canceled, :reject_before_dispatch, reject_after_dispatch] state.
  #
  #
  # This happens when designer submits form.

  state_machine :initial => :new, :use_transactions => false do
    event :move_to_new do
      transition all - [:vendor_canceled] => :new
    end

    event :order_looks_sane do
      transition :new => :pending
    end

    event :reactivate do
      transition :canceled => :pending
    end

    event :confirmation_from_designer do
      transition :pending => :scheduled
    end

    # Most likely this event will be manually triggered after
    # we get an email from aramex. If not so, we can ask designer
    # to inform us when shipping guys picked up.
    event :invoice_rejected do
      transition completed: :dispatched
    end

    event :pickup_by_shipping_guys do
      transition :scheduled => :pickup_done
    end

    event :got_awb do
      transition [:pickedup,:critical,:rto] => :dispatched
    end

    event :shipment_pickedup do
      transition :pickedup => :dispatched
    end

    event :pickedup do
      transition :pending => :pickedup
    end

    event :delayed_by_designer do
      transition :scheduled => :pending
    end

    event :dispatched_by_designer do
      transition :pending => :dispatched
    end

    # Some fuckup has happened here.
    event :fuckup do
      transition [:dispatched, :completed, :pickedup] => :critical
    end

    event :replacement_required do 
      transition [:dispatched, :completed] => :replacement_pending
    end

    event :replacement_pickedup do
      transition :replacement_pending => :pickedup
    end

    event :replacement_dispatched do 
      transition :replacement_pending => :dispatched
    end

    event :replacement_completed do 
      transition :replacement_pending => :completed, if: lambda {|designer_order| designer_order.completed_at.present? }
    end

    # Due to major fuckup (buyer not reachable, address invalid)
    # we have to cancel the order
    event :cancel do
      transition all - [:rto, :buyer_returned, :canceled] => :canceled
    end

    event :vendor_cancel do
      transition all - [:vendor_canceled] => :vendor_canceled
    end

    # Shipping guys will inform us when payment was collected from them
    # It is then we trigger this event.
    event :critical_to_completed do
      transition :critical => :completed,if: lambda {|designer_order| ((designer_order.created_at > Date.parse('2017-09-01')) && ((designer_order.ship_to.blank? && designer_order.order.international?) || (designer_order.ship_to == 'mirraw'))) ? (designer_order.invoice_state == 'completed_from_designer' && (!designer_order.order.cod? || designer_order.shipment_update == 'completed')) : designer_order.invoice_state == 'not_uploaded'}
    end

    event :payment_from_buyer do
      transition :dispatched => :completed,if: lambda {|designer_order| ((designer_order.created_at > Date.parse('2017-09-01')) && ((designer_order.ship_to.blank? && designer_order.order.international?) || (designer_order.ship_to == 'mirraw'))) ? (designer_order.invoice_state == 'completed_from_designer' && (!designer_order.order.cod? || designer_order.shipment_update == 'completed')) : designer_order.invoice_state == 'not_uploaded'}
    end

    event :shipment_delivered_partner do
      transition [:dispatched, :completed] => :completed,if: lambda {|designer_order| ((designer_order.created_at > Date.parse('2017-09-01')) && ((designer_order.ship_to.blank? && designer_order.order.international?) || (designer_order.ship_to == 'mirraw'))) ? (designer_order.invoice_state == 'completed_from_designer' && (!designer_order.order.cod? || designer_order.shipment_update == 'completed')) : designer_order.invoice_state == 'not_uploaded'}
    end

    event :missed_dispatch_event do
      transition :scheduled => :completed,if: lambda {|designer_order| ((designer_order.created_at > Date.parse('2017-09-01')) && ((designer_order.ship_to.blank? && designer_order.order.international?) || (designer_order.ship_to == 'mirraw'))) ? (designer_order.invoice_state == 'completed_from_designer' && (!designer_order.order.cod? || designer_order.shipment_update == 'completed')) : designer_order.invoice_state == 'not_uploaded'}
    end

    event :pickupdone_to_completed do
      transition :pickup_done => :completed,if: lambda {|designer_order| ((designer_order.created_at > Date.parse('2017-09-01')) && ((designer_order.ship_to.blank? && designer_order.order.international?) || (designer_order.ship_to == 'mirraw'))) ? (designer_order.invoice_state == 'completed_from_designer' && (!designer_order.order.cod? || designer_order.shipment_update == 'completed')) : designer_order.invoice_state == 'not_uploaded'}
    end

    event :buyer_dissatisfied do
      transition :completed => :buyer_returned
    end

    event :didnt_reach_buyer do
      transition [:dispatched, :pickedup, :critical, :pickup_done, :completed] => :rto
    end

    event :force_buyer_return do
      transition any - :buyer_returned => :buyer_returned
    end

    event :again_dispatch_for_rto_return do
      transition [:buyer_returned,:rto] => :dispatched
    end

    state :pending do
      validate :designer_order_not_in_blacklist
    end

    before_transition :to => :critical do |designer_order|
      designer_order.in_critical_at = Time.now
    end

    after_transition :to => :critical do |designer_order|
      all_items = designer_order.line_items
      items = []
      existing_issues = DesignerIssue.where(design_id: all_items.collect(&:design_id), order_id: designer_order.order_id).pluck(:design_id)
      all_items.each {|lt|
        designer_issue = DesignerIssue.create(:designer_id => designer_order.designer_id,
         :order_id => designer_order.order_id,
         :issue_type => 'critical order',
         :design_id => lt.design_id,
         :design_snapshot_price => lt.snapshot_price(RETURN_NORMAL),
         line_item_id: lt.id, designer_order_id: designer_order.id) if existing_issues.exclude?(lt.design_id)}
      all_items.where('design_id in (?)',all_items.collect(&:design_id) - existing_issues).update_all(claim_flag: true)
    end

    before_transition :to => :pending do |designer_order|
      designer_order.set_warehouse_address
      designer_order.cancel_reason = nil
      if designer_order.state == "new"
        SidekiqDelayGenericJob.set(queue: 'critical').perform_in(15.minutes.from_now, "OrderMailer", nil,"dispatch_order_to_designer", {"Order": designer_order.order.id}, {"DesignerOrder": designer_order.id}) unless designer_order.confirmed_at?
=begin
        OrderMailer.sidekiq_delay(queue: 'high')
                   .dispatch_order_to_designer(
                     designer_order.order, 
                     designer_order
                   ) unless designer_order.confirmed_at?
=end
        designer = designer_order.designer
        SidekiqDelayGenericJob.set(queue: 'critical').perform_in(15.minutes.from_now, "DesignerMailer", nil,"designer_first_order", {"Designer": designer.id}) unless designer.designer_orders.where('state not in (?) or confirmed_at is not null',['new', 'canceled']).exists?
        #DesignerMailer.sidekiq_delay(queue: 'high')
        #              .designer_first_order(designer) unless designer.designer_orders.where('state not in (?) or confirmed_at is not null',['new', 'canceled']).exists?
      end
    end

    after_transition :to => :pending do |designer_order|
      designer_order.update_attributes(invoice_number: designer_order.designer.get_invoice_number) unless designer_order.invoice_number.present?
      unless designer_order.confirmed_at?
        designer_order.confirmed_at = DateTime.current
        designer_order.line_items.each do |item|
          design = item.design
          design.international_grade += 1 if design.international_grade.present?
          design.sell_count = 0 unless design.sell_count.present?
          if design.sell_count.present?
            design.sell_count = design.sell_count + item.quantity
          else
            design.sell_count = item.quantity
          end
          design.save!
        end
        designer_order.set_ship_to
        SidekiqDelayGenericJob.set(queue: 'critical').perform_async(designer_order.class.to_s, designer_order.id,"update_shipper_info")
        #designer_order.sidekiq_delay(queue: 'critical').update_shipper_info
        designer_order.hold_gst
      end
      designer_order.save!
      SidekiqDelayGenericJob.set(queue: 'high').perform_in(15.minutes.from_now, designer_order.class.to_s, designer_order.id,"reassign_invoice_number_if_duplicate") if designer_order.invoice_number.present?
      #designer_order.sidekiq_delay_until(15.minutes.from_now, queue: 'high')
      #              .reassign_invoice_number_if_duplicate if designer_order.invoice_number.present?
    end

    after_transition :to => :scheduled do |designer_order|
      # Send out mail to aramex for pickup on that date
      # OrderMailer.dispatch_shipping_info_to_shipping_company(designer_order).deliver
    end

    after_transition :to => :pickedup do |designer_order|
      order = designer_order.order
      if order.pay_type == COD && designer_order.skip_after_transition
        SidekiqDelayGenericJob.perform_async("OrderMailer", nil,"pickedup_notification_to_user", {"Order": order.id}, {"DesignerOrder": designer_order.id})
        #OrderMailer.sidekiq_delay.pickedup_notification_to_user(order, designer_order)
      end
      if designer_order.ship_to == 'mirraw'
        SidekiqDelayGenericJob.perform_async(designer_order.class.to_s, designer_order.id, "generate_combined_unpack_labels")
        #designer_order.sidekiq_delay(queue: 'high')
        #              .generate_combined_unpack_labels
      end
    end

    after_transition :to => :dispatched do |designer_order|
      if designer_order.skip_after_transition
        SidekiqDelayGenericJob.set(queue: 'critical').perform_async("PayWithAmazon", nil, "confirm_designer_order", {"DesignerOrder": designer_order.id}) if designer_order.order.amazon_order_id.present?
        #PayWithAmazon.sidekiq_delay(queue: 'critical')
        #             .confirm_designer_order(designer_order) if designer_order.order.amazon_order_id.present?
        unless designer_order.order.international?
          if designer_order.tracking_num?
            # Send out a mail to buyer giving him tracking id
            SidekiqDelayGenericJob.set(queue: 'critical').perform_async(designer_order.class.to_s, designer_order.id, "send_tracking_sms")
            #designer_order.sidekiq_delay(queue: 'critical').send_tracking_sms
            SidekiqDelayGenericJob.perform_in(2.hours.from_now, "OrderMailer", nil, "mail_tracking_info_to_buyer", {"Order": designer_order.order.id}, {"DesignerOrder": designer_order.id})
=begin            
            OrderMailer.sidekiq_delay_until(2.hours.from_now)
                       .mail_tracking_info_to_buyer(
                         designer_order.order,
                         designer_order
                       )
=end                      
          end
        end
      end
      if designer_order.ship_to == 'mirraw'
        designer_order.mark_for_payout! if designer_order.can_mark_for_payout?
        designer_order.shipment_delivered_partner! if designer_order.can_shipment_delivered_partner?
        SidekiqDelayGenericJob.sidekiq_delay(queue: 'critical').perform_async(designer_order.class.to_s, designer_order.id, "generate_combined_unpack_labels") if designer_order.other_details['combined_unpack_label'].blank?
        #designer_order.sidekiq_delay(queue: 'critical')
        #              .generate_combined_unpack_labels if designer_order.other_details['combined_unpack_label'].blank?
      end
      AppEvent::DesignerOrderEvent.new(designer_order.id, "Vendor Dispatched").trigger_clevertap_event_deliver_later
    end

    after_transition [:buyer_returned,:rto] => :dispatched do |designer_order|
      SidekiqDelayGenericJob.set(queue: 'high').perform_async("ReverseCommission", nil, "add_reverse_commission", {"DesignerOrder": designer_order.id}, 1) if designer_order.try(:confirmed_at).present?
      #ReverseCommission.sidekiq_delay(queue: 'high')
      #                 .add_reverse_commission(designer_order,1) if designer_order.try(:confirmed_at).present?
    end

    before_transition :to => [:pickedup, :dispatched] do |designer_order|
      designer_order.skip_after_transition = false
      if designer_order.pickup.blank?
        designer_order.pickup = DateTime.now
        designer_order.skip_after_transition = true
      end
      designer_order.skip_after_transition = true if designer_order.pickedup?
    end

    after_transition :to => :completed do |designer_order|
      # Optional: Either just update the status to success or send
      # an email to designer that order was successfully completed.
    end

    before_transition :to => :completed do |designer_order|
      designer_order.completed_at = DateTime.now unless designer_order.completed_at.present?
    end

    after_transition any - [:canceled,:vendor_canceled] => :canceled do |designer_order|
      designer_order.change_rack_quantity(designer_order.line_items.sum(:quantity)) unless designer_order.order.ready_for_dispatch_at.present?
      SidekiqDelayGenericJob.set(queue: 'critical').perform_async(designer_order.order.class.to_s, designer_order.order.id, "mark_order_as_stitching")
      #designer_order.order.sidekiq_delay(queue: 'critical')
      #                    .mark_order_as_stitching
      SidekiqDelayGenericJob.perform_async("ScopeScoreEvent", nil, "apply_remove_cancel_event", {"DesignerOrder": designer_order.id}) if (CANCEL_ORDER_REASONS - NO_DESIGNER_EVENT_REASONS).include? designer_order.cancel_reason
      #ScopeScoreEvent.sidekiq_delay.apply_remove_cancel_event(designer_order) if (CANCEL_ORDER_REASONS - NO_DESIGNER_EVENT_REASONS).include? designer_order.cancel_reason
      SidekiqDelayGenericJob.set({queue: 'high'}).perform_async("ReverseCommission", nil, "add_reverse_commission", {"DesignerOrder": designer_order.id}) if designer_order.try(:confirmed_at).present?
      #ReverseCommission.sidekiq_delay(queue: 'high')
      #                 .add_reverse_commission(designer_order) if designer_order.try(:confirmed_at).present?
      SidekiqDelayGenericJob.set(queue: 'low').perform_async(designer_order.order.class.to_s, designer_order.order.id, "update_cost_estimation") if designer_order.order.international?
      #designer_order.order.sidekiq_delay(queue: 'low').update_cost_estimation if designer_order.order.international?
    end

    after_transition :to => [:canceled] do |designer_order|
      order = designer_order.order
      designer_order.line_items.map{|li| li.warehouse_cancel_item(li.variant || li.design,false,nil,false)} if order.other_details[:previous_state] != 'followup'
      SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "convey_cancel_update_to_designer", {"DesignerOrder": designer_order.id}) unless designer_order.order.confirmed_at.blank?
      #OrderMailer.sidekiq_delay.convey_cancel_update_to_designer(designer_order) unless designer_order.order.confirmed_at.blank?
      if designer_order.notes.to_s.exclude?("canceled:#{designer_order.id}")
        SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_stitching_info_mail_to_user")
        #order.sidekiq_delay.send_stitching_info_mail_to_user
        SidekiqDelayGenericJob.set(queue: 'critical').perform_async("PayWithAmazon", nil, "cancel_designer_order", {"DesignerOrder": designer_order.id}) if designer_order.order.amazon_order_id.present?
        #PayWithAmazon.sidekiq_delay(queue: 'critical').cancel_designer_order(designer_order) if designer_order.order.amazon_order_id.present?
      end
      if designer_order.designer_payout_status == "paid" || designer_order.designer_payout_status == "processing" || designer_order.designer_payout_notes.present?
        # We have or are already making payment for this order. Create new adjustment entry for the same
        SidekiqDelayGenericJob.set(queue: 'critical').perform_async(designer_order.class.to_s, designer_order.id, "create_adjustment_for_cancelation")
        #designer_order.sidekiq_delay(queue: 'high')
        #              .create_adjustment_for_cancelation
      end
      SalesRegister.sidekiq_delay(queue: 'high')
                   .create_new_entry('designer_order_cancel','sales_return',SalesRegister.object_to_json([order,designer_order])) if ['new','pending','confirmed','cancel'].exclude? order.state
      can_increase_qty = (!designer_order.designer.is_unicommerce_vendor || order.increment_item_quantity)
      SidekiqDelayGenericJob.perform_async(designer_order.class.to_s, designer_order.id, "restore_state", can_increase_qty) if designer_order.order.tag_list.exclude?('oos') && order.other_details[:previous_state] != 'followup'
      #designer_order.sidekiq_delay.restore_state(can_increase_qty) if designer_order.order.tag_list.exclude?('oos') && order.other_details[:previous_state] != 'followup'
      designer_order.line_items.update_all(canceled_on: Time.current, cancel_reason: designer_order.cancel_reason)
    end

    after_transition to: [:vendor_canceled] do |designer_order|
      order = designer_order.order
      designer_order.change_rack_quantity(designer_order.line_items.sum(:quantity)) unless order.ready_for_dispatch_at.present?
      SalesRegister.sidekiq_delay(queue: 'high').create_new_entry('designer_order_cancel','sales_return',SalesRegister.object_to_json([order,designer_order])) if ['new','pending','confirmed','cancel'].exclude? order.state
      SidekiqDelayGenericJob.perform_async("ScopeScoreEvent", nil, "apply_remove_cancel_event", {"DesignerOrder": designer_order.id}) if (CANCEL_ORDER_REASONS - NO_DESIGNER_EVENT_REASONS).include? designer_order.cancel_reason
      #ScopeScoreEvent.sidekiq_delay.apply_remove_cancel_event(designer_order) if (CANCEL_ORDER_REASONS - NO_DESIGNER_EVENT_REASONS).include? designer_order.cancel_reason
      SidekiqDelayGenericJob.set(queue: 'high').perform_async("ReverseCommission", nil, "add_reverse_commission", {"DesignerOrder": designer_order.id}) if designer_order.try(:confirmed_at).present?
      #ReverseCommission.sidekiq_delay(queue: 'high').add_reverse_commission(designer_order) if designer_order.try(:confirmed_at).present?
      SidekiqDelayGenericJob.set(queue: 'critical').perform_async(order.class.to_s, order.id, "mark_order_as_stitching")
      #order.sidekiq_delay(queue: 'critical').mark_order_as_stitching
      SidekiqDelayGenericJob.set(queue: 'low').perform_async(order.class.to_s, order.id, "update_cost_estimation") if order.international?
      #order.sidekiq_delay(queue: 'low').update_cost_estimation if order.international?
      SidekiqDelayGenericJob.set(queue: 'low').perform_async("DesignerMailer", nil,"designer_order_vendor_cancel_to_vendor", designer_order.designer_id, order.number)
      #DesignerMailer.sidekiq_delay(queue: 'low').designer_order_vendor_cancel_to_vendor(designer_order.designer_id, order.number)
    end  

    after_transition to: [:vendor_canceled, :canceled] do |designer_order|
      order = designer_order.order
      order.check_if_all_items_dispatched?(true) if order.partial_dispatch?
      designer_order.shipment.cancel_clickpost_shipment if designer_order.shipment.present? && designer_order.clickpost_serviceable?
    end

    after_transition :canceled => [:new,:pending] do |designer_order|
      designer_order.change_rack_quantity(designer_order.line_items.to_a.sum(&:quantity),'add') unless designer_order.order.ready_for_dispatch_at.present?
      order = designer_order.order
      SalesRegister.sidekiq_delay(queue: 'high').create_new_entry('designer_order_reinitiate','sales',SalesRegister.object_to_json([order,designer_order])) if ['new','pending','confirmed','cancel','followup'].exclude? order.state
      SidekiqDelayGenericJob.perform_async("ScopeScoreEvent", nil, "apply_remove_cancel_event", {"DesignerOrder": designer_order.id}, :remove)
      #ScopeScoreEvent.sidekiq_delay.apply_remove_cancel_event(designer_order,:remove)
      SidekiqDelayGenericJob.set(queue: 'critical').perform_async(order.class.to_s, order.id, "mark_order_as_stitching")
      #order.sidekiq_delay(queue: 'critical').mark_order_as_stitching
      SidekiqDelayGenericJob.set(queue: 'high').perform_async(designer_order.class.to_s, designer_order.id, "remove_adjustment_for_cancelation") if designer_order.designer_payout_status == "paid" || designer_order.designer_payout_status == "processing" || designer_order.designer_payout_notes.present?
      #designer_order.sidekiq_delay(queue: 'high').remove_adjustment_for_cancelation if designer_order.designer_payout_status == "paid" || designer_order.designer_payout_status == "processing" || designer_order.designer_payout_notes.present?
      SidekiqDelayGenericJob.set(queue: 'critical').perform_async(order.class.to_s, order.id, "reduce_quantity_from_warehouse") unless order.followup?
      #order.sidekiq_delay(queue: 'critical').reduce_quantity_from_warehouse unless order.followup?
      SidekiqDelayGenericJob.set(queue: 'high').perform_async("ReverseCommission", nil, "add_reverse_commission", {"DesignerOrder": designer_order.id}, 1) if designer_order.try(:confirmed_at).present?
      #ReverseCommission.sidekiq_delay(queue: 'high').add_reverse_commission(designer_order,1) if designer_order.try(:confirmed_at).present?
    end

    after_transition :replacement_pending => [:dispatched, :completed, :pickedup] do |designer_order|
      replacement_needed_ids = designer_order.line_items.select{|item| item.issue_status == 'Y' && item.rtv_quantity.to_i > 0}.map(&:id)
      if replacement_needed_ids.present?
        LineItem.bulk_add_into_scan('LineItem', replacement_needed_ids, 'Replacement Dispatched From Vendor', designer_order.designer.try(:account).try(:id))
      end
    end

    # after_transition :to => [:buyer_returned] do |designer_order|
      # return_designer_order = designer_order.return_designer_orders.last
      # SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "shipment_notification_for_delivered", {designer_order.class.to_s => designer_order.id } ) if designer_order.order.domestic? && return_designer_order.present? && return_designer_order.reverse_shipment.shipment_state=='delivered'
    # end
    
    after_transition :to => [:rto, :buyer_returned] do |designer_order|
      order = designer_order.order
      SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "convey_status_update_to_designer", {"Order": order.id}, {"DesignerOrder": designer_order.id})
      #OrderMailer.sidekiq_delay.convey_status_update_to_designer(order, designer_order)
      ###### Payouts #####
      if designer_order.designer_payout_status == "paid" || designer_order.designer_payout_status == "processing" || designer_order.designer_payout_notes.present?
        # We have or are already making payment for this order. Create new adjustment entry for the same
        designer_order.designer.adjustments.where(:order => order, :notes => "Refund for #{order.number}", :amount => -1 * designer_order.payout, :status => 'unpaid', designer_order: designer_order).first_or_create
      end
      if (mirraw_cost = designer_order.mirraw_shipping_cost.to_i).present? && mirraw_cost > 0 && designer_order.designer.adjustment_allowed?
        Adjustment.where(amount: -1 * mirraw_cost,designer_id: designer_order.designer_id, order_id: designer_order.order_id, designer_order: designer_order).first_or_create(notes: "Shipping Cost for Order #{order.number}",status: 'unpaid')
      end
      # designs = designer_order.line_items.collect{|item| item.design_id}
      # Design.where(:id => designs).update_all('return_count = return_count + 1')
      SalesRegister.sidekiq_delay(queue: 'high')
                   .create_new_entry(
                     'designer_order_returned',
                     'sales_return',
                     SalesRegister.object_to_json([order,designer_order])
                   ) if ['new','pending','confirmed'].exclude? order.state
      SidekiqDelayGenericJob.set(queue: 'high').perform_async("ReverseCommission", nil, "add_reverse_commission", {"DesignerOrder": designer_order.id}) if designer_order.try(:confirmed_at).present?
      #ReverseCommission.sidekiq_delay(queue: 'high')
      #                 .add_reverse_commission(designer_order) if designer_order.try(:confirmed_at).present?
    end

# Commenting this Block As of Now. In case Reintegrate ThirdWatch or GoKwik Just need to Uncomment it. 
=begin
    after_transition :to => [:pickedup, :pickup_done, :dispatched, :completed, :canceled, :vendor_canceled, :rto, :buyer_returned] do |designer_order|
      order = designer_order.order
      if order.gokwik_successful_order.present?
        #gokwik = GoKwik.new(order)
        #designer_order.line_items.each{|l| gokwik.sidekiq_delay(queue: 'low').update_order_state(l,order.id) }
        designer_order.line_items.each{|l| SidekiqDelayClassSpecificGenericJob.set(queue: 'low').perform_async("GoKwik", "update_order_state", {"#{order.class}": order.id}, {"LineItem": l.id}, order.id)}
      end
    end
=end
  end

  def is_package_in_rack?
    rack_list_id.present? && rack_code.present?
  end

  def add_to_rtv_searcher_entity
    if is_package_in_rack?
      items_in_rack = line_items.select{|item| item.received_on.present? && item.qc_status}
      items_in_rack.each{|item| item.create_searcher_entity_for_item(search_type: 'RtvSearcherEntity')}
    end
  end

  state_machine :invoice_state, initial: :not_uploaded, use_transactions: false do
    event :designer_uploaded_invoice do
        transition not_uploaded: :invoice_uploaded ,if: lambda {|designer_order| designer_order.state == 'dispatched'}
    end
    event :mark_for_payout do
      transition invoice_uploaded: :completed_from_designer
    end

    event :reject do
      transition [:invoice_uploaded,:completed_from_designer] => :not_uploaded
    end
  end

  def create_adjustment_for_cancelation(item_id = nil)
    note = "Order Canceled after payout #{order.number}"
    if item_id.present?
      item       = LineItem.find_by_id item_id
      item_total = (item.vendor_selling_price * item.quantity * (100 - self.transaction_rate)/100).to_i
      note       = "Item #{item.design.try(:id)} Canceled after payout in order #{order.number}"
    else
      item_total = self.payout > 0 ? self.payout : ((self.line_items.sane_items.select('sum(COALESCE((line_items.vendor_selling_price * line_items.quantity),0)) as total')[0].try(:total).to_i - self.discount.to_i) * (100 - self.transaction_rate)/100).to_i
    end
    self.designer.adjustments.where(order: order, notes: note, amount: -1 * item_total, status: 'unpaid', designer_order_id: self.id).first_or_create if item_total > 0
  end

  def remove_adjustment_for_cancelation(item_id = nil)
    if item_id.present?
      item = LineItem.find_by_id item_id
      item_total = (item.vendor_selling_price * item.quantity * (100 - self.transaction_rate)/100).to_i
      if item_total > 0 && (adjustments = self.designer.adjustments.where(order: order, amount: -1 * item_total)).present? && adjustments.select{|a| a.notes.to_s.include?("Item #{item.design.try(:id)} Canceled after payout")}.present?
        self.designer.adjustments.where(order: order,notes: "Item #{item.design.try(:id)} added after payout in order #{order.number}",amount: item_total, status: 'unpaid', designer_order: self).first_or_create
      end
    else
      item_total = self.payout > 0 ? self.payout : ((self.line_items.sane_items.select('sum(COALESCE((line_items.vendor_selling_price * line_items.quantity),0)) as total')[0].try(:total).to_i - self.discount.to_i) * (100 - self.transaction_rate)/100).to_i
      if item_total > 0 && (adjustments = self.designer.adjustments.where(order: order, amount: -1 * item_total)).present? && adjustments.select{|a| a.notes.to_s.include?('Order Canceled after payout')}.present?
        self.designer.adjustments.where(order: order,notes: "Order marked sane #{order.number}",amount: item_total, status: 'unpaid', designer_order: self).first_or_create
      end
    end
  end

  def designer_order_not_in_blacklist
    errors.add(:id,'Customer is Blacklisted') if self.order.other_details.try(:[],"blacklist_user").present?
  end

  def change_rack_quantity(quantity,action = nil)
    if (rack = self.rack_list).present?
      if action == 'add'
        rack.change_quantity(quantity,'add')
      else
        rack.change_quantity(quantity)
      end
    end
  end

  def calculate_price(price, rate)
    if price.present?
      price/=rate
      price.round(2)
    else
      price.to_f
    end
  end

  def notification_image_url(img)
    "#{ActionController::Base.helpers.image_path(img)}"
  end

  def ship_as_cod?
    order.cod? && ship_to != 'mirraw'
  end

  def courier_trackable?
    rapid_delivery_serviceable || clickpost_serviceable.present? || DOMESTIC_PREPAID_COURIER_AUTOMATION[ship_to].find{|shipper| self.send("#{shipper.sub(' ','_')}_serviceable")}.present?
  end

  def calculate_params(currency_details, msg)
    params = {
      bundleFrom: "localytics",
      notificationMainTitle: msg['title'],
      id: self.order.id,
      created_at: "#{self.created_at}",
      courier_company: self.tracking_partner,
      tracking_number: self.tracking_num,
      currency_symbol: currency_details.symbol,
      hex_symbol: currency_details.hex_symbol,
      shipping: "#{shipping = calculate_price(self.order.shipping, currency_details.rate)}",
      cod: "#{cod = calculate_price(self.order.cod_charge, currency_details.rate)}",
      item_total: "#{@total.round(2)}",
      total: "#{(@total+shipping+cod).round(2)}",
      discounts: "#{self.discount}"
    }
    if self.shipment.shipment_state == "delivered"
      params[:type] = "Rate&Review"
      params[:PushImageHandler] = notification_image_url('order_delivered.jpg')
      params[:state] = "Delivered"
      params[:ignore_threshold] = true
    else
      params[:type] = "OrderDispatched"
      params[:PushImageHandler] = notification_image_url('dispatched_notify.png')
      params[:state] = "Dispatched"
      params[:ignore_threshold] = true
    end
    params
  end

  def variant_params(option_type_value)
    {
      p_name: option_type_value.p_name,
      option_type: option_type_value.option_type.p_name
    }.stringify_keys
  end

  def calculate_variants(current_line_item)
    if (variant = current_line_item.variant).present?
      option_type_values = []
      variant.option_type_values.each{ |option_type_value|
        option_type_values << variant_params(option_type_value)
      }
      variants = {
        variant_id: variant.id.to_s,
        option_type_values: option_type_values
      }.stringify_keys
    end
  end

  def chargeable?(line_item_addon)
    line_item_addon.snapshot_price > 0
  end

  def addon_param(line_item_addon, currency_details)
    if chargeable?(line_item_addon)
      {
        name: line_item_addon.addon_type_value.name,
        currency_symbol: currency_details.symbol,
        snapshot_price: "#{calculate_price(line_item_addon.addon_type_value.price, currency_details.rate)}"
      }.stringify_keys
    end
  end

  def calculate_addons(current_line_item, currency_details)
    addons = []
    if (li_addons = current_line_item.line_item_addons).present?
      li_addons.each{ |line_item_addon|
        addons << addon_param(line_item_addon, currency_details)
      }
    end
    addons.compact
  end

  def calculate_line_item_total(current_line_item, currency_details)
    @total ||= 0
    amt = calculate_price(current_line_item.snapshot_price(RETURN_NORMAL), currency_details.rate)
    current_line_item.line_item_addons.each{ |line_item_addon|
      amt += calculate_price(line_item_addon.addon_type_value.price, currency_details.rate)
    }
    amt = (amt * current_line_item.quantity).round(2)
    @total += amt
    amt
  end

  def returnable?(current_line_item)
    self.order.country.eql?('India') && self.pickup.present? &&
    self.pickup >= MAX_RETURNABLE_DAYS.days.ago &&
    current_line_item.stitching_done.blank? &&
    current_line_item.return_designer_order_id.blank?
  end

  def calculate_design_item(id, currency_details)
    current_line_item = self.line_items.find_by_design_id(id)
    if (cc=current_line_item.snapshot_country_code).present?
      currency_details = CurrencyConvert.find_by_country_code(cc)
      currency_details.rate = current_line_item.snapshot_currency_rate
    end
    {
      design_id: "#{id}",
      line_item_id: "#{current_line_item.id}",
      title: current_line_item.design.title,
      quantity: "#{current_line_item.quantity}",
      price: "#{calculate_price(current_line_item.snapshot_price, currency_details.rate)}",
      currency_symbol: currency_details.symbol,
      hex_symbol: currency_details.hex_symbol,
      discount_price: "#{calculate_price(self.discount, currency_details.rate)}",
      line_item_addons: calculate_addons(current_line_item, currency_details),
      line_item_variants: calculate_variants(current_line_item),
      designer_id: "#{self.id}",
      designer_name: self.designer.name,
      note: current_line_item.note,
      total: "#{calculate_line_item_total(current_line_item, currency_details)}",
      returnable: "#{returnable?(current_line_item)}",
      image: current_line_item.image(:small_m)[8..-1],
    }.stringify_keys
  end

  def extra_params(msg)
    currency_details = CurrencyConvert.find_by_country_code(self.order.country_code || "US")
    items = []
    @total = 0
    design_ids = self.line_items.pluck(:design_id)
    design_ids.each{ |id| items << self.calculate_design_item(id, currency_details) }
    params = self.calculate_params(currency_details, msg).merge(line_items: items)
    params.stringify_keys
  end

  def delivered_msg(account_id, msg)
    {
      target: account_id.to_s,
      alert: msg,
      android: {extra: self.extra_params(msg)}.stringify_keys
    }.stringify_keys
  end

  def android_delivered_notification
    order = self.order
    if (account = find_account(order.user_id)).present? && (app_source = order.app_source).present?
      message = []
      msg = LOCALYTICS_MESSAGES['designer_order']['delivered']
      if FCM_NOTIFICATION
        FirebaseNotification.fcm_push_notification(msg['title'], msg['body'], nil, account.fcm_registration_token, app_source) if account.try(:fcm_registration_token) && (app_source.include?('Android') || app_source.include?('iOS'))
      else
        # message << delivered_msg(account.id, msg)
        # campaign_key = "Order_Delivered"
        # app_source = ((app_source = app_source.split('-')).length == 2) ? app_source[0] : app_source[0..1].join('-')
        # LocalyticsNotification.customer_notification(message, campaign_key, app_source) if app_source.include?('Android')
      end
    end
  end

  def dispatched_msg(account_id, msg)
    {
      target: account_id.to_s,
      alert: msg,
      android: {extra: self.extra_params(msg)}.stringify_keys
    }.stringify_keys
  end

  def find_account(user_id)
    account = Account.find_by_accountable_id(user_id)
    if account && (api_data = account.api_data).present?
      api_data.app_version.in?(ALLOWED_APP_VERSIONS | ALLOWED_IOS_APP_VERSIONS[7..-1]) ? account  : nil
    end
  end

  def android_dispatched_notification
    order = self.order
    if (account = find_account(order.user_id)).present? && (app_source = order.app_source).present?
      message = []
      msg = LOCALYTICS_MESSAGES['designer_order']['dispatched']
      if FCM_NOTIFICATION
        FirebaseNotification.fcm_push_notification(msg['title'], msg['body'], nil, account.fcm_registration_token, app_source) if account.try(:fcm_registration_token) && (app_source.include?('Android') || app_source.include?('iOS'))
      else  
        message << dispatched_msg(account.id, msg)
        campaign_key = "Order_Dispatched"
        app_source = ((app_source = app_source.split('-')).length == 2) ? app_source[0] : app_source[0..1].join('-')
        # LocalyticsNotification.customer_notification(message, campaign_key, app_source) if app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
        ClevertapNotification.modify_and_push_notification(message.compact, campaign_key, app_source.downcase) if app_source.include?('iOS') || CLEVERTAP_NOTIFICATION == 'true'
      end
    end
  end

  def restore_state(increment_item_quantity = true)
    if RESPECT_QUANTITY && increment_item_quantity
      self.line_items.preload(:design,:variant).each do |item|
        item.update_item_quantity('increase',item.quantity) if item.status != "cancel"
      end
    end
    self.touch_with_callbacks if self.state == "canceled"
    return true
  end

  def self.get_delivered_date(d)
    doc = Nokogiri::HTML(open(d.tracking, 'User-Agent' => 'ruby'))
    delivered_at = doc.css('.ResultsTableCell1')[9].try(:text).try(:strip)
    if delivered_at.present?
      d.delivered_at = DateTime.strptime(delivered_at, "%m/%d/%Y")
      d.save!
    end
  end

  def total_count
    quantity = 0
    self.line_items.each {|lt| quantity += lt.quantity}
    quantity
  end

  def line_items_count
    self.line_items.length
  end

  def get_rack_hash_by_design(rack_list_code,design_id=nil)
    rack_hash =[]
    rack_code_for_pcot_options = ''
    line_items = self.line_items.preload(variant: :option_type_values, line_item_addons: :addon_type_value)
    timestamp = package_received_on.present? ? package_received_on : line_items.collect(&:received_on).compact.max
    designable_type = line_items.first.design.designable_type
    li_count = order.line_items.sane_items.count
    if design_id.present?
      line_item = line_items.select{|line_item| line_item.design_id == design_id.to_i}.first
      variant_name = line_item.variant ? line_item.variant.option_type_values[0].try(:p_name) : ''
      item_rack_list_code = line_item.get_item_rack_code(des_order_rack_code: rack_list_code)

      is_petticoat_present = line_item.line_item_addons.any? do |lia|  
        rack_code_for_pcot_options = get_rackcode_for_petticoat_options(lia.addon_type_value.name.downcase,lia.notes.downcase)
      end 
    
      line_item.quantity.times do
        rack_hash << {'o_number' => order.number,'customer_name' => order.name,'ds_id'=> id, 'timestamp' => timestamp,
                        'rack_list_code' => item_rack_list_code, 'rack_code' => rack_code, 'auto_approve' => line_item.item_details['std_auto_approve'], 'item_product_type' => line_item.item_details['product_type'],'line_item_count' => li_count, 'product_type' => designable_type, 'design_id' => design_id, 'order_conf_date' => order.confirmed_at, 'variant_name' => variant_name, 'item_id' => line_item.id, 'petticoat_material' => (rack_code_for_pcot_options if is_petticoat_present)}.compact
      end
    else
      line_items.each do |line_item|
        variant_name = line_item.variant ? line_item.variant.option_type_values[0].try(:p_name) : ''
        item_rack_list_code = line_item.get_item_rack_code(des_order_rack_code: rack_list_code)
        
        is_petticoat_present = line_item.line_item_addons.any? do |lia|  
          rack_code_for_pcot_options = get_rackcode_for_petticoat_options(lia.addon_type_value.name.downcase,lia.notes.downcase)
        end 

        line_item.quantity.times do
          rack_hash << {'o_number' => order.number,'customer_name' => order.name,'ds_id'=> id, 'timestamp' => timestamp,
                        'rack_list_code' => item_rack_list_code, 'rack_code' => rack_code, 'auto_approve' => line_item.item_details['std_auto_approve'], 'item_product_type' => line_item.item_details['product_type'],'line_item_count' => li_count, 'product_type' => designable_type, 'design_id' => line_item.design_id, 'order_conf_date' => order.confirmed_at, 'variant_name' => variant_name, 'item_id' => line_item.id, 'petticoat_material' => (rack_code_for_pcot_options if is_petticoat_present)}.compact
        end
      end
    end
    rack_hash
  end

  def get_designer_order_item_details(scale = false)
    invoice_items = Array.new
    cod_amount = 0
    order      = self.order

    cod_charge = 0
    cod_charge = self.calculate_domestic_shipping_charge
    all_items = self.line_items.not_canceled
    single_cod_charge  = cod_charge / all_items.sum(&:quantity).to_f
    all_items.each do |item|
      addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
      designable_type = item.design.designable_type
      name        = item.design.categories.first.name.gsub('-', ' ').camelize
      name        += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?
      name        += addon_text
      # addon_price = item.vendor_addon_items.to_a.sum{|addon|addon.snapshot_price(RETURN_NORMAL)}
      if order.cod? && order.currency_rate_market_value.present? && self.ship_to != "mirraw"
        price_per_item = order.international_cod_price(item.price_with_addons(scale ? RETURN_SCALED : RETURN_NORMAL))
      else
        price_per_item = item.price_with_addons(scale ? RETURN_SCALED : RETURN_NORMAL)
      end
      items_price =  price_per_item* item.quantity
      cod_amount  += items_price
      hsn_code,gst_rate = item.find_hscode_gst(price_per_item)
      sku = (item.variant_id.present? ? item.variant_id : item.design.design_code.present? ? item.design.design_code : item.design_id).to_s
      invoice_items << { name: name, quantity: item.quantity, price: price_per_item, total_price: items_price,gst_rate: gst_rate,hsn_code: hsn_code, designable_type: designable_type, sku: sku}
      invoice_items << { name: 'Shipping Charges', quantity: item.quantity, price: single_cod_charge, total_price: (single_cod_charge * item.quantity).round(2),hsn_code: '',gst_rate: gst_rate,sku: sku} if single_cod_charge > 0 && scale
    end
    invoice_data = Shipment.calculate_domestic_invoice(order, invoice_items, false, self)
    total = invoice_data[:item_total].to_i
    return [invoice_items, total, cod_amount, cod_charge, invoice_data]
  end

  def get_rackcode_for_petticoat_options(addon_type_value,notes)
    case addon_type_value
    when 'petticoat stitching'
      notes.include?('cotton') ? 'P-C' : 'P-S'
    when 'shapewear'
      arr = notes.gsub("\n",'').gsub(" ", '').split(/:|,/)
      color_value = arr[3] 
      size_value = arr[1] 
      "SW--" + color_value + "-"+ size_value  
    end 
  end 

  def check_stitching_or_jewellery_des_order(items=nil)
    is_stitch = false
    all_items = items || self.line_items
    all_items.each do |item|
      item_design = item.design
      des_ord_type_initial = item_design.get_product_type_initial
      if des_ord_type_initial == 'J-'
        return 'jewellery'
      elsif item.stitching_required == 'Y' || (item.line_item_addons.to_a.present? && item.paid_addons?)
        is_stitch = true
      end
    end
    return 'stitching' if is_stitch
  end

  def restore_combo_variant_quantity
    self.line_items.each do |line_item|
      if line_item.combo_variant.present?
        line_item.combo_variant.increment!(:quantity, line_item.quantity)
      end
    end
  end
  

  def send_tracking_sms
    phone = Order.get_mobile_num(self.order.phone)
    order = self
    if phone && phone != 0 && phone.length == 12 && self.shipment.blank?
      if self.order.designer_orders.count == 1
        template = "Hi, Your Order# #{self.order.number} has been shipped via #{self.tracking_partner} courier with Tracking No. #{self.tracking_num}. Please check your email for details. Thanks, Mirraw.com!"
      else
        template = "Dear Customer, part of Your Order# #{self.order.number} with Sub-Order ID #{self.id} has been shipped via #{self.tracking_partner} courier with Tracking No. #{self.tracking_num}. Please check your email for more details. Thanks, Mirraw.com!"
      end
      res = SmsNotification::NotificationService.notify(phone, template)
    end
  end

  def reassign_invoice_number_if_duplicate
    self.update_invoice_number if is_duplicate_invoice
  end

  def is_duplicate_invoice
    where_clause = "invoice_number = '#{self.invoice_number}' AND id <> #{self.id}"
    DesignerOrder.where(where_clause).present?
  end
  # inputs params - none
  #
  # update shipper info
  # Returns object
  def update_shipper_info
    self.update_shipper_availability
    if self.priority_shipper_id.blank? || self.priority_shipper_cod_id.blank?
      self.update_priority_shipper
      self.update_priority_shipper_cod
    end
    payout_ratio = get_payout_ratio
    self.line_items.each do |item|
      item.line_item_addons.select{|li| li.snapshot_payable_to == 'designer' }
      addon_price = item.line_item_addons.select{|li| li.snapshot_payable_to == 'designer' }.sum{ |addon| addon.snapshot_price(RETURN_NORMAL)}
      if self.ship_to == 'mirraw'
        item_snapshot_price = ((item.snapshot_price(RETURN_NORMAL)+ addon_price) * payout_ratio).round(2)
      elsif order.currency_rate_market_value.present?
        item_snapshot_price = order.international_cod_price(item.snapshot_price + addon_price)
      end
      item.find_hscode_gst(item_snapshot_price)
    end
    # pre generation of AWB.
    if awb_pre_genetation_allowed_designer && check_require_conditions_for_shipment
      if (shipper_id = get_shipper_for_shipment).present?
        unless self.invoice_number.present?
          invoice_num = self.designer.get_invoice_number
          self.update_column(:invoice_number, invoice_num)
        end
        self.update_columns(shipper_id: shipper_id, shipment_status: 'progress', shipment_error: nil)
        SidekiqDelayClassSpecificGenericJob.perform_async("ClickPostAutomation", "create_forward_pickup", {"#{self.order.class.to_s}": self.order.id, "#{self.class.to_s}": self.id}, {pre_gereration: true, sidekiq_request_params: true})
        #click_post = ClickPostAutomation.new(self.order, self)
        #click_post.sidekiq_delay(queue: 'critical')
        #          .create_forward_pickup(pre_gereration: true)
      else
        self.add_notes_without_callback('AWB Pre Gereration Failed', 'System', nil)
      end
    end
    self.save
  end


  def awb_pre_genetation_allowed_designer
    self.designer.is_unicommerce_vendor
  end

  def check_require_conditions_for_shipment
    self.order.domestic? &&  self.clickpost_serviceable? && (self.shipment_status == 'pending' || (self.shipment_status == 'failed' && self.shipment.blank?))
  end

  def get_shipper_for_shipment
    shipper_id  = self.order.cod? ? self.priority_shipper_cod_id : self.priority_shipper_id
  end


  # inputs params - none
  #
  # update priority shipper id field
  # Returns false or object based on conditions
  def update_priority_shipper
    shipper_names = get_serviceable_shipper_names
    w_shipper_name = 'UPPER(shippers.name) IN (?)', shipper_names
    o_priority = 'designer_shippers.priority DESC'
    priority_shipper = self.designer.designer_shippers.includes(:shipper).where(w_shipper_name).order(o_priority).limit(1).references(:shippers).first
    self.priority_shipper_id = priority_shipper.shipper.id if priority_shipper.present?
  end

  # inputs params - none
  #
  # update priority shipper cod id field
  # Returns false or object based on conditions
  def update_priority_shipper_cod
    shipper_id = get_serviceable_shippers_cod(DesignerShipper.where(cod: true,designer_id: designer_id).uniq.pluck(:shipper_id)).values
    if shipper_id.present?
      w_shipper_id = 'shippers.id IN (?)', shipper_id
      w_shipper_cod = 'couriers.cod = ?', 'Y'
      o_priority = 'designer_shippers.priority DESC'
      priority_shipper_cod = self.designer.designer_shippers.includes(:shipper => :couriers).where(w_shipper_id).where(w_shipper_cod).order(o_priority).limit(1).references(shippers: :couriers).first
      self.priority_shipper_cod_id = priority_shipper_cod.shipper.id if priority_shipper_cod.present?
    end
  end

  # input params - none
  #
  # Returns array of downcased serviceable shipper names
  def get_serviceable_shipper_names
    shipper_names = []
    shipper_names << 'DELHIVERY' if self.delhivery_serviceable == true
    shipper_names << 'ARAMEX' if self.aramex_serviceable == true
    shipper_names << 'FEDEX'        if self.fedex_serviceable == true
    shipper_names << 'FEDEX-MIRRAW' if self.fedex_mirraw_serviceable == true
    shipper_names << 'ECOMEXPRESS'  if self.ecomexpress_serviceable == true
    shipper_names << 'CITY COURIER' if self.city_courier_serviceable == true
    shipper_names << 'TIRUPATI COURIER' if self.tirupati_courier_serviceable == true
    shipper_names << 'BLUEDART'     if self.bluedart_serviceable == true
    shipper_names << 'DTDC'         if self.dtdc_serviceable == true
    shipper_names << 'SPEEDPOST'    if self.speedpost_serviceable == true
    shipper_names << 'GATI' if self.gati_serviceable == true
    shipper_names << 'SHIP DELIGHT'    if self.ship_delight_serviceable == true
    shipper_names << 'SHADOWFAX' if self.shadowfax_serviceable == true
    shipper_names << 'RAPID DELIVERY' if self.rapid_delivery_serviceable
    shipper_names << 'XPRESS BEES' if self.xpress_bees_serviceable
    shipper_names << 'WOWEXPRESS' if self.wowexpress_serviceable
    shipper_names << 'EKART' if self.ekart_serviceable
    shipper_names << 'KERRYINDEV' if self.kerryindev_serviceable
    shipper_names << 'SMARTR' if self.smartr_serviceable
    shipper_names << 'FIRST CALL' if self.first_call_serviceable
    shipper_names << 'ATS' if self.ats_serviceable
    shipper_names << 'DAK INDIA' if self.dak_india_serviceable
    shipper_names.push(*Shipper::ALL_SHIPPERS.slice(*Shipper::DEFAULT_SHIPPERS).keys) if self.ship_to == 'mirraw' || self.order.international?
    shipper_names
  end

  # input params - none
  #
  # Returns array of serviceable shipper objects
  def get_serviceable_shippers
    shipper_names = self.get_serviceable_shipper_names
    # shipper_names.insert(0, shipper_names.delete(self.priority_shipper.name.upcase)) if self.priority_shipper && shipper_names.include?(self.priority_shipper.name.upcase)
    shippers = Shipper::ALL_SHIPPERS.slice(*shipper_names) #Shipper.where('LOWER(shippers.name) IN (?)', shipper_names)
    shipper_logic(shippers)
  end

  # input params - none
  #
  # Returns array of serviceable cod shipper objects
  def get_serviceable_shippers_cod(cod_designer_shippers = [])
    shipper_names = self.get_serviceable_shipper_names
    # shipper_names.insert(0, shipper_names.delete(self.priority_shipper.name.upcase)) if self.priority_shipper && shipper_names.include?(self.priority_shipper.name.upcase)
    common_shipper_ids = Shipper::ALL_SHIPPERS.values_at(*shipper_names) & cod_designer_shippers
    shippers = Shipper::ALL_SHIPPERS.select{|_,v| common_shipper_ids.include?(v)} || []
    shippers = shippers.sort_by { |key, _value| SHIPPERS_PRIORITY.index(key) || SHIPPERS_PRIORITY.length }.to_h
    # shipper_logic(shippers)
  end

  def shipper_logic(shippers={})
    if self.priority_shipper && shippers
      shippers = {self.priority_shipper.name.upcase => self.priority_shipper_id}.merge(shippers) if shippers.delete(self.priority_shipper.name.upcase).present?
    end
    shippers
  end

  def alternate_priority_shipper
    order = self.order
    click_post = ClickPostAutomation.new(order, self)
    clickpost_shipper_id = click_post.get_recommended_courier([self], false)

    return nil if clickpost_shipper_id[self.id].blank?

    shippable_shippers = Shipper.where(clickpost_shipper_id: clickpost_shipper_id[self.id])
    return nil if shippable_shippers.blank?

    match = shippable_shippers.find do |shipper|
      Array(clickpost_shipper_id[self.id]).include?(shipper.clickpost_shipper_id) && shipper.id != self.priority_shipper_id
    end

    return match
  end

  def assign_priority
    priority = self.alternate_priority_shipper
    if priority.present?
      self.priority_shipper_id = priority
      self.priority_shipper_cod_id = priority
    end
  end

  # input params - none
  #
  # update serviceable field for various courier partners
  # Returns object
  def update_shipper_availability
    _, _, _,_,_, shipping_pincode,_,_= order.get_warehouse_shipping_address
    order_pincode = ((order.international? && ship_to.blank?) || ship_to == 'mirraw') ? shipping_pincode : order.pincode
    designer_pincode = (domestic_sor?(order) ? shipping_pincode : designer.pincode) if designer_id.present?
    if designer_pincode.to_s.length == 6 && order_pincode.to_s.length == 6
      cod = order.cod? && !order.international?
      region = self.order.international? ? 'international' : (cod ? 'cod' : 'domestic')
      shipper_ids = self.designer.allowed_shipper_ids(region)
      clickpost_shipper_id = {}
      shippable_shippers = []
      if CLICKPOST_FORWARD_SERVICEABLE
        click_post = ClickPostAutomation.new(order, self)
        dos = [self]
        clickpost_shipper_id = click_post.get_recommended_courier(dos,false)
        if clickpost_shipper_id[self.id].present?
          shippable_shippers = Shipper.where(clickpost_shipper_id: clickpost_shipper_id[self.id])
          # shippable_shippers = shippable_shippers.select{|s| shipper_ids.include? s.id}
        end
      end
      if shippable_shippers.present?
        servicable_shipper = shippable_shippers.collect{|shipper| [shipper.id,shipper.clickpost_shipper_id]}.to_h
        priority = servicable_shipper.key(clickpost_shipper_id[self.id].find{|x| servicable_shipper.key(x)})
        self.priority_shipper_id = priority
        self.priority_shipper_cod_id = priority
        self.clickpost_serviceable = true
      elsif !cod
        shippable_shippers = Courier.shippable_shippers(shipper_ids, designer_pincode, order_pincode, cod)
        self.clickpost_serviceable = false
      end
      shippable_shippers.each do |shipper|
        shipper_name_downcase = shipper.name.downcase
        case shipper_name_downcase
        when 'delhivery'
          self.delhivery_serviceable = true
        when 'ecomexpress'
          self.ecomexpress_serviceable = true
        when 'city courier'
          self.city_courier_serviceable = true
        when 'aramex'
          self.aramex_serviceable = true
        when 'bluedart'
          self.bluedart_serviceable = true
        when 'gati'
          self.gati_serviceable = true
        when 'ship delight'
          self.ship_delight_serviceable = true
        when 'shadowfax'
          self.shadowfax_serviceable = true
        when 'rapid delivery'
          self.rapid_delivery_serviceable = true if (self.ship_to.blank? && order.international?) || self.ship_to == 'mirraw'
        when 'xpress bees'
          self.xpress_bees_serviceable = true
        when 'wowexpress'
          self.wowexpress_serviceable = true
        when 'ekart'
          self.ekart_serviceable = true
        when 'kerryindev'
          self.kerryindev_serviceable = true if (self.ship_to.blank? && order.international?) || self.ship_to == 'mirraw' 
        when 'smartr'
          self.smartr_serviceable = true
        when 'first call'
          self.first_call_serviceable = true
        when 'ats'
          self.ats_serviceable = true
        when 'dak india'
          self.dak_india_serviceable = true
        end
        
      end
    end
    # Shippers to provide without checking availability
    self.dtdc_serviceable = true
    self.speedpost_serviceable = true
  end

  # inputs order pincode, designer pincode
  #
  # checks fedex service availability via fedex api
  # Returns false or object based on conditions

  def failed_shipment(error)
    self.shipment_status = 'failed'
    self.shipment_error = error
    unless self.pickedup?
      self.tracking_partner = nil
      self.pickup = nil
      self.state  = 'pending'
    end
  end

  def self.delhivery_fetch_awb(designer_order, designer, order, ship_to, reference, amount = 0)
    awb_number = Shipment.delhivery_awb('cod')
    designer_shipper = DesignerShipper.includes(:shipper).where(designer_id: designer.id, shipper: {name: 'Delhivery'}).first

    if Rails.env.development? || Rails.env.staging?
      pickup_location = {:country => 'India', :name => 'Mirraw COD-SRT-RedHot Fashion'}
    elsif designer_shipper.present?
      pickup_location = {:country => 'India', :name => designer_shipper.delhivery_client_warehouse.to_s.gsub(/[&;]/,'')}
    else
      pickup_location = {:country => 'India', :name => 'Mirraw COD'}
    end
    return_location = { return_country: pickup_location[:country], return_name: pickup_location[:name]}

    order_pickup_details = designer.get_order_pickup_details(designer_order, order)
    return_location[:return_phone] = pickup_location[:phone] = order_pickup_details[:phone]
    return_location[:return_add] = pickup_location[:add] = order_pickup_details[:address].gsub(/[&;]/,'')
    return_location[:return_city] = pickup_location[:city] = order_pickup_details[:city]
    return_location[:return_state] = pickup_location[:state] = order_pickup_details[:state]
    return_location[:return_pin] = pickup_location[:pin] = order_pickup_details[:pincode]

    send_location = if ship_to == 'mirraw'
      company_name, shipping_telephone, shipping_address_1,shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_shipping_address(designer_order.try(:warehouse_address_id))
      {add: "#{shipping_address_1}, #{shipping_address_2}", city: shipping_city, state: shipping_state, phone: shipping_telephone, pin: shipping_pincode, name: company_name}
    else
      {add: order.street.gsub(/[&;]/,''), city: order.city, state: order.buyer_state, phone: order.phone, pin: order.pincode, name:  order.name}
    end

    shipments_req = {:waybill => awb_number, :client => 'Mirraw COD', :order => reference,
     :order_date => (order.try(:confirmed_at) || order.try(:created_at) || Time.current).iso8601, :payment_mode => (designer_order.try(:ship_as_cod?) ? 'COD' : 'Prepaid'), :total_amount => amount, :cod_amount => (designer_order.try(:ship_as_cod?) ? amount : 0), :country => 'India', :weight => '0.0 gm'}

    request = 'format=json&data=' + {:pickup_location => pickup_location, :shipments => [shipments_req.merge(send_location).merge(return_location)]}.to_json

    api = Mirraw::Application.config.delhivery_baseurl + '/cmu/push/json/?token={token}'
    api_params = api.sub('{token}', Mirraw::Application.config.delhivery_cod_token)
    api_params_encoded = URI.encode(api_params)
    res = HTTParty.post(api_params_encoded, :body => request, :headers => {'Content-Type' => 'application/x-www-form-urlencoded'})

    if res.body.present? && res.headers.present? && res.headers.content_type.present? && res.headers.content_type.match('json').present? && res.response.code == '200'
      res_content = JSON.parse(res.body)
    else
      res_content = {'error' => res.response.code, 'rmk' => 'retry after 10 mins'}
    end
    return [awb_number, res_content]
  end

  def get_tcs_tds_and_gst_value
    gst_tax, total_taxable_value = 0.0, 0.0
    self.line_items.sane_items.preload(:line_item_addons,design: :categories).each do |item|
      i_price = item.snapshot_price(RETURN_NORMAL)
      hsn_code = (self.designer.hsn_approved && item.design.gst_rate.present? && item.design.hsn_code.present?) ? item.design.hsn_code.to_s : item.design.categories.hsn_code
      _,gst_rate = item.find_hscode_gst(i_price, hsn_code, false)
      taxable_value = i_price/(1+gst_rate/100.0)
      gst_tax += ((i_price - taxable_value)*item.quantity).round(2)
      total_taxable_value += (taxable_value * item.quantity).round(2)
    end
    tcs_value = (total_taxable_value/100.0).round(2)
    tds_value = (total_taxable_value * 0.1/100.0).round(2)
    can_apply_tds = DesignerOrder.is_tds_applicable(self, self.order)
    can_apply_tcs = DesignerOrder.is_tcs_applicable(self, self.order)
    [gst_tax.round(2), (can_apply_tcs ? tcs_value : 0.0), (can_apply_tds ? tds_value : 0.0)]
  end

  def self.is_tds_applicable(designer_order, order)
    (designer_order.ship_to != 'mirraw' && (order.cod? ? (designer_order.pickup || designer_order.created_at) : designer_order.created_at) >= Date.parse('2020-10-01'))
  end

  def self.is_tcs_applicable(designer_order, order)
    (designer_order.ship_to != 'mirraw' && (order.cod? ? (designer_order.pickup || designer_order.created_at) : designer_order.created_at) >= Date.parse('2018-10-01'))
  end

  def create_automated_shipment(shipper_name, international = false,current_account=nil)
    if international
      name_of_shipper = Shipper::ALL_SHIPPERS.key(shipper_name.to_i) 
      tracking, response = 
      case name_of_shipper
      when 'DELHIVERY'
        DesignerOrder.delhivery_fetch_awb(self, self.designer, self.order, 'mirraw', self.shipment_reference, self.total)        
      when 'XPRESS BEES'
        Shipment.xpress_bees_create_domestic(self)
      end
      if response.present?
        if response['error'].blank? && response['success']
          self.replacement_pending? ? Shipment.create_replacement_shipment(self.id, shipper_name, tracking) : Shipment.create_non_automated(self.id, shipper_name, tracking, true)
        else
          error_message = response['error'].to_s + ' ' + response['rmk'].to_s
          self.update_columns(state: 'pending', shipment_status: 'failed', shipment_error: error_message)
        end
      end
    else
      notice_message = ''
      if self.shipment_status == 'pending' || (self.shipment_status == 'failed' && self.shipment.blank?) || self.pickedup?
        begin
          self.shipment_status  = 'progress'
          self.shipment_error   = nil
          self.save!
          SidekiqDelayGenericJob.set(queue: 'critical').perform_async("#{self.class}", self.id, "create_cod_shipment", shipper_name, !self.pickedup?, {"#{current_account.class}": current_account.id})
          #self.sidekiq_delay(queue: 'critical')
          #    .create_cod_shipment(shipper_name, !self.pickedup?,current_account)
          notice_message = 'Please check pickedup orders tab to view your shipments.'
        rescue => error
          self.shipment_status = 'pending'
          self.shipment_error = error.message
          self.save!
        end
      else
        notice_message = 'Shipment creation in progress please wait'
      end
      return notice_message
    end
  end

  def create_cod_shipment(service, move_to_pickup = true,current_account=nil)
    ActiveRecord::Associations::Preloader.new.preload(self,order: :shipments,line_items: [:line_item_addons, [design: :categories], [variant: :option_type_values]]); nil
    if self.order.state != 'cancel'
      case service
      when 'Delhivery'
        Shipment.delhivery_create_domestic(self)
      when 'Aramex'
        Shipment.aramex_create_domestic(self)
      when 'Bluedart'
        Shipment.bluedart_create_domestic(self.id)
      when 'Gati'
        Shipment.gati_create_domestic(self)
      when 'Ship Delight'
        Shipment.ship_delight_create_domestic(self)
      when 'Shadowfax'
        Shipment.shadowfax_create_domestic(self)
      when 'Xpress Bees'
        Shipment.xpress_bees_create_domestic(self)
      end
      if self.shipment_status != 'failed' && move_to_pickup
        self.pickedup
        SidekiqDelayGenericJob.perform_in(15.minutes.from_now, "DesignerMailer", nil, "mail_label_to_designer", {"#{self.class}": self.id})
        #DesignerMailer.sidekiq_delay_until(15.minutes.from_now).mail_label_to_designer(self)
      elsif self.shipment_status != 'failed' && self.pickedup?
          self.destroy_additional_shipments
          self.add_notes_without_callback("Shipment Reallocated to #{service}",'admin', current_account)
          SidekiqDelayGenericJob.perform_in(15.minutes.from_now, "DesignerMailer", nil, "mail_label_to_designer", {"#{self.class}": self.id})
          #DesignerMailer.sidekiq_delay( 15.minutes.from_now).mail_label_to_designer(self)
      end
    end
  end

  def destroy_additional_shipments
    if (total_shipments = (self.order.shipments.select{|sh| sh.designer_order_id == self.id && sh.number != self.tracking_num})).count > 0
      total_shipments.each do |shipment|
        shipment.cancel_clickpost_shipment if shipment.clickpost_serviceable?
        shipment.destroy
      end
    end
  end

  def calculate_domestic_shipping_charge
    order = self.order
    cod_charge = 0
    domestic_shipping_charge = order.shipping.to_i + order.cod_charge.to_i
    if order.shipments.blank? && domestic_shipping_charge > 0
      if order.currency_rate_market_value.present? && self.ship_to != 'mirraw'
        cod_charge = order.international_cod_price(domestic_shipping_charge) 
      else
        cod_charge = domestic_shipping_charge
      end
    end
    cod_charge
  end

  def check_track_and_add_events(shipper_name,tracking_num,cod)
    # if tracking_num.present? || cod.present?
      if cod.present? || clickpost_serviceable.present?
        self.send(:order_quality_event!, :add, 'ValidTrackingNumber')
      # else
      #   case shipper_name
      #   when 'Fedex','Fedex-Mirraw'
      #     track_num_regex = /^(7|8)\d{11}$/
      #   when 'Delhivery'
      #     track_num_regex = /^2\d{11}$/
      #   when 'Aramex'
      #     track_num_regex = /^4\d{10}$/
      #   when 'Bluedart'
      #     track_num_regex = ''
      #   when 'Ship Delight'
      #     track_num_regex = /^8\d{9}$/
      #   when 'Shadowfax'
      #     track_num_regex = /^SF\d{8}MW$/
      #   when 'DTDC'
      #     track_num_regex = ''
      #   when 'SpeedPost'
      #     track_num_regex = ''
      #   else
      #     track_num_regex = ''
      #   end
      #   if track_num_regex.match(tracking_num.to_s.strip)
      #     self.send(:order_quality_event!, :add, 'ValidTrackingNumber')
      #   else
      #     self.send(:order_quality_event!, :add, 'InvalidTrackingNumber')
      #   end
      end
    # end
    promised_date = get_vendor_dispatch_days
    self.send(:delivery_event!, :add, 'LateDispatched') if promised_date.end_of_day.past?
  end

  # input params - none
  #
  # Provides shipment reference
  #
  # Returns String
  def shipment_reference
    "#{self.designer.id} - #{self.order.number} - #{self.id}"
  end

  def get_vendor_delivery_days
    if self.ship_to == 'mirraw' || self.order.international?
      self.designs.collect(&:eta).compact.max.to_i + self.designer.ship_time + self.designer.vacation_days_count.to_i
    else
      (self.get_vendor_dispatch_days.to_date.mjd - (self.confirmed_at || self.created_at).to_date.mjd) + DeliveryNpsInfo.get_city_based_pdd(self.order.city, [self.designer.pickup_location.to_s.downcase])
    end
  end

  def get_vendor_dispatch_days(express = nil)
    order_obj = (association_cache.keys.include?(:payment_order) ? payment_order : order)
    # express ||= order_obj.express_delivery
    eta_clause = "CASE WHEN designs.eta > 0 then designs.eta WHEN designers.eta > 0 THEN designers.eta else 0 END + CASE
     WHEN line_items.created_at >= designers.vacation_start_date and line_items.created_at <= designers.vacation_end_date THEN COALESCE(EXTRACT(EPOCH FROM ((designers.vacation_end_date - designers.vacation_start_date)/(3600*24))),0)
     ELSE
     0
     END"
    designer_lsr_days = (DESIGNER_LSR.hours.to_f/1.days)
    designer_lsr = designer_lsr_days > 1 ? designer_lsr_days : 0
    promised_date = (self.confirmed_at || self.created_at).advance(days: self.line_items.sane_items.joins(design: :designer).maximum(eta_clause).to_i + ((self.confirmed_at || self.created_at).try(:hour) > 10 ? 1 : 0) + (designer_lsr))
    promised_date += 1.day if promised_date.sunday?
    promised_date
  end

  def self.create_rtv_invoice_url(designer_order_id,line_items_id,rtv_shipment_id)
    rtv_shipment = RtvShipment.where(id: rtv_shipment_id).first
    designer_order = DesignerOrder.where(id: designer_order_id).first
    rtv = true
    order = designer_order.order
    designer = designer_order.designer
    label_file_name = "rtv_invoice/#{Time.now.strftime('%d/%m/%Y')}/#{designer_order_id}_#{line_items_id[0]}_Invoice.pdf"
    pdf_content =  ActionController::Base.new().render_to_string(
      :template => 'designer_orders/show.html.haml',
      :layout   => false ,
      :locals   => {:@designer_order => designer_order, :@rtv => rtv, line_items_id: line_items_id,
                    :@order => order, :@designer => designer, params: nil, :@awb_no => rtv_shipment.number},
      encoding: 'UTF-8'
    )
    delhivery_surface_label = WickedPdf.new.pdf_from_string(pdf_content)
    AwsOperations.create_aws_file(label_file_name, delhivery_surface_label, false)
    download_url = AwsOperations.get_aws_file_path(label_file_name)
    rtv_shipment.update_column(:invoice_url, download_url)
  end
  # input params - none
  #
  # Adds notes to order
  def add_notes(note, to_save, current_account = nil)
    first = current_account.nil? ? 'System' : current_account.email.split('@')[0]
    note_update(first,note)
    self.save if to_save
  end

  def add_notes_without_callback(note, note_type, current_account = nil)
    type = ['admin', 'accounts_admin', 'super_admin', nil, ''].include?(note_type) ? 'other' : note_type
    event_attrs = {note_type: type, notes: note, done_by: (current_account.try(:name) || 'System'), event_timestamp: Time.current, account_id: current_account.try(:id)}
    self.new_record? ? events.build(event_attrs) : events.create(event_attrs)
  end

  def note_update(user,note)
    note_content = "#{Date.today.strftime("%m/%d")} : #{user} : #{note}"
    self.notes = if self[:notes].blank?
      ''
    else
      self[:notes] + ' ... '
    end
    self.notes = self[:notes] + note_content
  end

  # input params - none
  #
  # Provides Amount for total and cod and array of invoice items
  #
  # Returns Array
  def shipment_attributes
    # items array for generating invoice
    cod_charge = self.calculate_domestic_shipping_charge
    total = cod_charge
    all_items = self.line_items.not_canceled.to_a
    single_cod_charge  = cod_charge / all_items.sum(&:quantity).to_f
    invoice_items = Array.new

    all_items.each do |item|
      name        = item.design.categories.first.name.gsub('-', ' ').camelize
      name        += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?

      if order.cod? && order.currency_rate_market_value.present? && self.ship_to != "mirraw"
        price_per_item = self.order.international_cod_price(item.price_with_addons)
      else
        price_per_item = item.price_with_addons
      end
      items_price = price_per_item * item.quantity
      total  += items_price
      hsn_code,gst_rate = (item.purchase_hsn_code.present? && item.purchase_gst_rate.present?) ? [item.purchase_hsn_code,item.purchase_gst_rate] : item.find_hscode_gst(price_per_item)
      invoice_items << {name: name, quantity: item.quantity, price: price_per_item, total_price: items_price,gst_rate: gst_rate,hsn_code: hsn_code }
      invoice_items << { name: 'Shipping Charges', quantity: item.quantity, price: single_cod_charge, total_price: (single_cod_charge * item.quantity).round(2),hsn_code: '',gst_rate: gst_rate } if single_cod_charge > 0
    end

    [total, cod_charge, invoice_items]
  end

  # input params
  # error_text - String
  #
  # Reverts DesignerOrder to pending state and adds shipment error
  #
  # Return Object
  def shipment_create_failed(error_text)
    self.state = 'pending'
    self.shipment_status = 'failed'
    error = ''
    if error_text.is_a?(Hash)
      error_text.each {|key, value| error += "#{key}: #{value} "}
    else
      error = error_text
    end
    self.shipment_error = error
    self
  end

  # input params
  # shipment - Shipment Object
  #
  # Updates DesignerOrder with shipment details
  #
  # Return Object
  def shipment_create_pass(shipment)
    self.shipper_id = shipment.shipper_id
    self.tracking_num = shipment.number
    self.tracking_partner = shipment.shipper.name
    self.shipment_status = 'created'
    self.shipment_error = nil
    self
  end

  def sale_discount_percent
    country_code = Design.country_code
    Rails.cache.fetch("sale_discount_percentage_#{country_code}", expires_in: 1.day) do
      var_hash = discount_promotion.present? ? discount_promotion.try(:variables_hash) : {}
      var_hash.present? ? JSON.parse(var_hash)['global_discount_percent'].to_i : 0
    end
  end

  def sale_on_country?(active_promotions,country=nil)
    if (country_code = Promotions.sale_discount_on_country(active_promotions,country)).present?
      country_code.split(',').include?(Design.country_code)
    else
      false
    end
  end

  def is_payout_paid?
    designer_payout_status == "paid"
  end

  def self.create_adjustment_for_issue(issue,designer_order,item_cost,weight,design_id=nil,quantity=nil)
    quantity_text = quantity.present? ? "Quantity: #{quantity}" : nil
    note_content = "#{issue} Cost for Order #{designer_order.order.number} - Design Id : #{design_id} #{quantity_text} "
    if issue == 'RTV' && weight.present?
      amount = 80 + 55*((weight.to_f/0.5)-1)
      note_content = "#{issue} Cost for Order #{designer_order.order.number}"
    elsif issue == 'OOS' && item_cost.present?
      amount = (OOS_CHARGES_PERCENTAGE/100) * item_cost.to_f
    elsif issue == 'QC Failed' && item_cost.present?
      amount = (QC_FAILED_CHARGES_PERCENTAGE/100) * item_cost.to_f
    elsif issue == "Critical" && item_cost.present?
      amount = 0.30 * item_cost.to_f
    end
    amount *= quantity.to_i if issue != 'RTV'
    if designer_order.present? && (issue != 'RTV' || (issue == 'RTV' && designer_order.designer.adjustment_allowed?))
      Adjustment.create(amount: -1*amount,designer_id: designer_order.designer.id, order_id: designer_order.order_id,notes: note_content,status: 'unpaid', designer_order_id: designer_order.id)
      return true
    else
      return false
    end
  end

  def get_line_items_stitching_data
    stitching_required_count,stitching_done_count,stitching_sent = 0,0,0
    line_items.each do |line_item|
      if line_item.status != 'cancel'
        stitching_required_count += 1 if line_item.stitching_required == 'Y'
        stitching_done_count += 1 if line_item.stitching_done == 'Y'
        stitching_sent += 1 if line_item.stitching_sent == 'Y'
      end
    end
    get_status_message_stitching(stitching_required_count,stitching_done_count,stitching_sent)
  end

  def self.mail_rack_reports(numbers, rack_codes, rack_id, rack_number, current_account)
    s_des_ord_clause = 'designer_orders.id,designer_orders.state,designer_orders.rack_list_id,designer_orders.rack_code,rack_check_done_by,rack_check_done_on,designer_orders.order_id'
    incorrect_scanned_orders = DesignerOrder.
                          select(s_des_ord_clause).
                          joins(:order,:line_items).
                          preload(:payment_order,:rack_list,:line_items).
                          where('designer_orders.rack_code IN (?)', rack_codes).
                          where('designer_orders.rack_list_id NOT IN (?) OR orders.state NOT IN (?)', rack_id,['sane','ready_for_dispatch']).
                          group('designer_orders.id')
    missing_orders =
    if rack_codes.present?
      DesignerOrder.joins(:order,:line_items).
                select(s_des_ord_clause).
                preload(:payment_order,:rack_list,:line_items).
                where('designer_orders.rack_code NOT IN (?) and designer_orders.rack_code is not null', rack_codes).
                where('designer_orders.rack_list_id IN (?) and orders.state IN (?)', rack_id, ['sane','ready_for_dispatch']).
                group('designer_orders.id')
    else
      DesignerOrder.
            select(s_des_ord_clause).
            joins(:order).
            preload(:payment_order,:rack_list,:line_items).
            where('rack_list_id IN (?) AND orders.state IN (?)', rack_id, ['sane','ready_for_dispatch'])
    end
    correct_scanned_orders = 
      DesignerOrder.joins(:order).
                select(s_des_ord_clause).
                preload(:payment_order,:rack_list,:line_items).
                where('designer_orders.rack_code IN (?) AND designer_orders.rack_list_id IN (?)' ,rack_codes,rack_id).
                where('orders.number IN (?) AND orders.state IN (?)',numbers,['sane','ready_for_dispatch']) if numbers.present?   

    pdf_content =  ActionController::Base.new().render_to_string(
      template: '/admin/rack_reports',
      layout: false,
      locals: {rack_id:rack_id,incorrect_scanned_orders: incorrect_scanned_orders, missing_orders:missing_orders,correct_scanned_orders:correct_scanned_orders}
    )
    rack_pdf = WickedPdf.new.pdf_from_string(pdf_content,{orientation: 'Landscape'})
    DesignerOrder.where('rack_list_id IN (?)', rack_id).update_all(rack_check_done_by: current_account.name, rack_check_done_on: Time.now) if numbers.compact.present?
    emails = {'to_email'=> current_account.email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer("#{rack_number.upcase} Rack Report #{Date.today}",'Please Find Attachment.',emails,{"#{rack_number.upcase}_rack_report.pdf" => rack_pdf}).deliver
    AwsOperations.create_aws_file("Rack Reports/#{rack_number.upcase}/#{rack_number.upcase}_rack_report_#{Time.now.strftime('%m_%d_%Y')}.pdf",rack_pdf,false) if numbers.compact.present?
  end

  def update_designer_order_position
    if ['pending', 'new', 'canceled', 'buyer_returned', 'vendor_canceled', 'rto'].include?(state)
      group_position = 1
    elsif ['pickedup', 'critical'].include?(state) || ((valid_state = ['dispatched', 'completed'].include?(state)) && (ship_to.try(:downcase) != 'customer' && (line_items.to_a.find{|item| item.status.blank? && (item.received.blank? || (!item.qc_status && item.issue_status.try(:downcase) != 'n') || (item.stitching_required.try(:downcase) == 'y' && item.stitching_done.blank?) || item.issue_status.try(:downcase) == 'y')}.present?)))
      group_position = 2
    elsif valid_state
      group_position = 3
    else
      group_position = 1
    end
  end

  def cancel_item
    dos_cancel = self.canceled? || self.buyer_returned?
    if dos_cancel
      line_items
    else
      line_items.select{|item| ['buyer_return', 'cancel'].include?(item.status)}
    end.collect do |item|
      name = item.design.categories.first.name.gsub('-', ' ').camelize
      name += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?
      name += ' [size: ' + item.variant.option_type_values.collect(&:name).last + ']' if  item.variant.present? && item.variant.option_type_values.present?
      li = Hash.new
      li[:name] = name,
      li[:quantity] = (dos_cancel || item.status == 'cancel') ? item.quantity : item.return_quantity.to_i
      li[:price] = ((item.vendor_selling_price.to_f * (100 - self.transaction_rate.to_f)) / 100 ).to_f.round(2)
      li[:total_price] = (li[:price] * li[:quantity]).round(2)
      li[:hsn_code], li[:gst_rate] = if item.purchase_hsn_code.present? && item.purchase_gst_rate.present?
        [item.purchase_hsn_code,item.purchase_gst_rate]
      else
        item.find_hscode_gst(li[:price])
      end
      li[:taxable_value] = (li[:total_price] / (1+ li[:gst_rate].to_f/100)).round(2)
      li[:gst_tax] = (li[:total_price] - li[:taxable_value]).round(2)
      li
    end
  end

  def get_credit_note_report_item
    cancel_item.map! do |item|
      item.merge({
        credit_note_number: credit_note_number,
        vendor_gst_number: designer.gst_no,
        mirraw_GSTIN_no: MIRRAW_GST_NUMBER,
        supplier_name: designer.business_name.presence || designer.name,
        referal_invoice_no: invoice_number,
        invoice_date: (pickup.presence || completed_at.presence || created_at).strftime("%d/ %m/ %Y"),
        order_number: payment_order.number,
        credit_note_date: credit_note_date.strftime("%d/ %m/ %Y")
      })
    end
  end

  def generate_credit_note
    ActiveRecord::Associations::Preloader.new.preload(self,[:payment_order,:designer,line_items: [design: [:categories],variant: :option_type_values]])
    return if (items = cancel_item).blank?
    gst_total, total_taxable_value, item_total = 0, 0, 0
    date = Date.current
    year = date.strftime('%y')

    if %w(01 02 03).include?(date.strftime('%m'))
      year = "#{year.to_i-1}-#{year}"
    else
      year = "#{year}-#{year.to_i+1}"
    end

    credit_note_uniq_no = SystemConstant.where(name: 'CREDIT_NOTE_UNIQ_NO').first
    credit_note_number = "CN/#{designer.id}/#{year}/#{credit_note_uniq_no.value}"
    total = Hash.new(0)
    items.each do |item|
      total[:gst_total] += item[:gst_tax]
      total[:total_taxable_value] += item[:taxable_value]
      total[:item_total] += (item[:price] * item[:quantity])
    end
    credit_note = {
      header: {
        name: designer.business_name.presence || designer.name,
        street: designer.business_street.presence || designer.street,
        pincode: (designer.business_city.presence || designer.city) + " - " + (designer.business_pincode.presence || designer.pincode),
        phone: designer.gst_no
      },
      items: items,
      credit_details: {
        'Credit Note No': credit_note_number,
        'Credit Note date': date.strftime('%d/%m/%Y'),
        'Ref Purchase Order No': payment_order.number
      },
      igst_flag: !(designer.state.try(:upcase) == SHIPPER_STATE.upcase),
    }
    credit_note.merge!(total)
    if designer.gst_no.blank?
      credit_note[:payable_gst] = (total[:gst_total]).round(2)
      credit_note[:total_taxable_value] = '-'
      credit_note[:gst_total] = 0.0
      credit_note[:items].each{|i| i.merge!({gst_rate: 0, gst_tax: 0, taxable_value: nil})}
    end
    credit_note[:gst_no] = "GST no : #{designer.gst_no}" if designer.gst_no.present?
    pdf_content =  ActionController::Base.new().render_to_string(
      :template => '/designer_orders/credit_note',
      :layout   => false,
      :locals   => {credit_note: credit_note, wa_ids: [self.warehouse_address_id]}
    )
    file_name = "credit_notes/#{date.strftime('%d_%m_%Y')}_#{self.id}.pdf"
    AwsOperations.create_aws_file(file_name, WickedPdf.new.pdf_from_string(pdf_content, encoding: "UTF-8"), false)
    self.credit_note_url = AwsOperations.get_aws_file_path(file_name)
    self.credit_note_number = credit_note_number
    self.credit_note_date = Time.now
    self.skip_before_after_filter = true
    if self.save
      credit_note_uniq_no.value = (credit_note_uniq_no.value.to_i + 1).to_s
      credit_note_uniq_no.save
    end
  end

  def self.generate_credit_report(date_range = nil)
    file = Tempfile.new('generate_credit_report')
    date_range ||= Time.current.beginning_of_month..Time.current.end_of_month
    column_row = false
    CSV.open(file, 'wb+') do |csv|
      DesignerOrder.preload(:payment_order,:designer,line_items: [design: [:categories],variant: :option_type_values]).group(:id).
      where(credit_note_date: date_range).
      find_each(batch_size: 500) do |designer_order|
        designer_order.get_credit_note_report_item.each do |item|
          unless column_row
            csv << item.keys
            column_row = true
          end
          csv << item.values
        end
      end
    end
    date = Time.current.strftime('%d_%m_%Y')
    file_name = "vendor_credit_note_reports/#{date}.csv"
    AwsOperations.create_aws_file(file_name, file)
    url = AwsOperations.get_aws_file_path(file_name)
    OrderMailer.report_mailer("Credit Not Report #{date}",url,
      {
        'to_email'=> DEPARTMENT_HEAD_EMAILS['accounts'],
        'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'
      },
    ).deliver
  end

  def self.update_gst_status(file, email)
    order_number, attachment = [],{}
    csv = CSV.new(open(AwsOperations.get_aws_file_path(file)),headers: :true, header_converters: :symbol)
    csv.each_slice(1000) do |lines|
      designer_order_ids = lines.collect{|row| row[:order_id].to_i}.uniq
      DesignerOrder.where(designer_orders: { state: ['dispatched','completed','buyer_returned'], gst_status: 'hold', id: designer_order_ids}).update_all(gst_status: 'process')
      order_number += (designer_order_ids - DesignerOrder.where(id: designer_order_ids).where(gst_status: 'process').pluck(:id))
    end
    if order_number.present?
      attachment[:file] = {'failed_gst_update.csv'=> "failed_order_id\n" + order_number.join("\n")}
      attachment[:msg] = 'GST update failed For following orders please find attachment of the same below'
    elsif attachment[:msg].blank?
      attachment[:msg] = 'GST update successfully done'
    end
    OrderMailer.report_mailer('GST Status Update', attachment[:msg], {'to_email'=> email,'from_email_with_name'=> email}, attachment[:file] || {}).deliver_now
  end

  def self.sla_violated(offset: Designer::SLA_CONFIG[:sla_critical_alert_days])
    max_eta = <<-MAX_ETA
      CASE
        WHEN COALESCE(designs.eta,0) > 0 THEN designs.eta
        WHEN COALESCE(designers.eta,0) > 0 THEN designers.eta
        ELSE #{Designer::SLA_CONFIG[:mirraw_designer_default_eta]}
      END + CASE WHEN designers.vacation_end_date is not null and designers.vacation_end_date > now()
        THEN (designers.vacation_end_date::date - CURRENT_DATE)::INT
        ELSE 0
        END + #{Designer::SLA_CONFIG[:sla_offset]} - #{offset}
    MAX_ETA

    joins(:designer, line_items: :design).with_state(:pending).
    where('line_items.available_in_warehouse is not True').
    where("designer_orders.confirmed_at < (now() - make_interval(days => (#{max_eta})))").
    reorder('confirmed_at').uniq
  end

  def violate_sla?(offset: Designer::SLA_CONFIG[:sla_critical_alert_days])
    pending? && confirmed_at.present? && confirmed_at <= (Date.current - (critical_eta - offset.to_i))
  end

  def critical_eta
    max_eta = line_items.map{|item| item.design.critical_eta}.max
    max_eta += (designer.vacation_end_date.to_date - Date.current.to_date) if designer.vacation_mode_on?
    max_eta
  end

  def notes
    self.events.present? ? (self[:notes].to_s + '... ' + self.events.collect{|ev| "#{ev.event_timestamp.strftime('%m/%d')}:#{ev.done_by}:#{ev.notes}"}.join('... ')) : self[:notes].to_s
  end

  def hold_gst
    self.gst_status = 'hold' if self.ship_to == 'mirraw' && GST_HOLD_EXCLUDED_DESIGNER_IDS.to_a.exclude?(self.designer_id)
  end

  def domestic_sor?(order)
    ALLOWED_DOMESTIC_SOR && !order.international? && line_items.all?{|item| item.available_in_warehouse}
  end


  def assign_available_rack
    rack = nil
    partially_filled_racks = self.order.designer_orders.where.not(designer_orders: {state: ['canceled','vendor_canceled']}).pluck(:rack_list_id).uniq
    rack_lists     = RackList.where('description is null OR description not in (?)', ['warehouse_items','rtv_rack','invalid_rack'].push(*RackList::FAKE_RACKS.keys)).order('max_capacity - space_filled asc,code asc')
    if !ENABLE_NEW_RACK_LOGIC['enable'] && self.rack_list_id.blank? && partially_filled_racks.count > 1 && partially_filled_racks.include?(nil)
      total_quantity = self.line_items.sum(:quantity)
      if (rack = RackList.where(id: partially_filled_racks.compact).where('max_capacity - space_filled >= ?', total_quantity).order('max_capacity - space_filled asc,code asc').first).present?
        rack.change_quantity(total_quantity,'add')
        self.update_column(:rack_list_id, rack.id)
      else
        rack = assign_rack_designer_order_wise(rack_lists, total_quantity,'partially')
      end
    elsif self.rack_list_id.blank?
      total_quantity = self.order.line_items.joins(:designer_order).where(designer_orders: {rack_list_id: nil}).where.not(designer_orders: {state: ['canceled','vendor_canceled']}).sum(:quantity)
      if (rack = rack_lists.where('space_filled = ? and max_capacity - space_filled >= ?',0 ,total_quantity).first || rack_lists.where('max_capacity - space_filled >= ?', total_quantity).first).present?
        rack.change_quantity(total_quantity,'add')
        dos_ids = self.order.designer_orders.where(rack_list_id: nil).pluck(:id)
        DesignerOrder.where(id: dos_ids).where(rack_list_id: nil).update_all(rack_list_id: rack.id) if dos_ids.present?
      else
        if ENABLE_NEW_RACK_LOGIC['enable']
          do_hash = {}
          dos = DesignerOrder.where(rack_list_id: nil, order_id: self.order_id).where.not(state: ['canceled','vendor_canceled'])
          dos.each{|d| do_hash[d.id] = d.line_items.sum(:quantity)}
          if do_hash.present?
            group_do_and_assign_rack( ENABLE_NEW_RACK_LOGIC['max_rack_capacity'], do_hash, rack_lists)
          else
            order.add_notes_without_callback("NO DO avl for rack assigning", 'new_rack_allocation')
          end
        else
          total_do_li_quantity = self.line_items.sum(:quantity)
          rack = assign_rack_designer_order_wise(rack_lists, total_do_li_quantity, 'newly')
        end
      end
    elsif rack_list_id.present? && rack_code.blank?
      rack = self.rack_list
    end
    allocate_rack(rack.code,new_rack: rack) if rack.present?
  end

  #this is called for designer order quantity greater than max rack capacity
  #do not call externally
  def assign_rack_designer_order_wise(rack_lists, total_quantity, rack_fill_type)
    if (rack = (rack_lists.where('max_capacity - space_filled >= ?', total_quantity).first || rack_lists.where('space_filled = ? ', 0).first)).present?
      rack.change_quantity(total_quantity,'add')
      update_column(:rack_list_id, rack.id)
      order.add_notes_without_callback("#{rack_fill_type} assigned #{rack.code} to DO #{id}", 'rack_allocation')
      return rack
    else
      order.add_notes_without_callback("rack allocation to DO #{id} failed", 'rack_allocation')
    end
  end

  def assign_rack_to_multiple_do(rack_lists, total_quantity, designer_order_hash)
    dos = DesignerOrder.where(id: designer_order_hash)
    if (rack = (rack_lists.where('max_capacity - space_filled >= ?', total_quantity).first || rack_lists.where('space_filled = ? ', 0).first)).present?
      rack.change_quantity(total_quantity,'add')
      dos.update_all(rack_list_id: rack.id)
      dos.each{|d| d.allocate_rack(rack.code,new_rack: rack) if d.rack_code.blank?}
      order.add_notes_without_callback("Assigned #{rack.code} to DO #{designer_order_hash}", 'new_rack_allocation')
    else
      order.add_notes_without_callback("Not assigned to  DO #{designer_order_hash}", 'new_rack_allocation')
      dos.each do |d|
        rack = d.assign_rack_designer_order_wise(rack_lists, d.line_items.sum(:quantity), 'again')
        d.allocate_rack(rack.code,new_rack: rack) if d.rack_code.blank? && rack.present?
      end
    end
  end


  def allocate_rack(new_rack_code, account=nil, new_rack: nil)
    response = {error: true, error_text: 'Incorrect Rack Code'}
    new_rack_list = new_rack.presence || RackList.find_by_code(new_rack_code)
    if new_rack_list.present? && (rack_list_id != new_rack_list.id || self.rack_code.blank?)
      rack = self.rack_list
      prev_rack_code = rack.present? ? ('from ' + rack.code) : ''
      self.add_notes_without_callback("Rack changed #{prev_rack_code} to #{new_rack_code}",'rack_change',account) unless new_rack.present?
      self.rack_list_id = new_rack_list.id
      if (self.rack_code.blank? && new_rack.present?) || self.save
        LineItem.bulk_add_into_scan('DesignerOrder', self.id, (prev_rack_code.present? ? 'Rack Changed' : 'Rack Assign'), account.try(:id))
        total_quantity = line_items.sum(:quantity) unless new_rack.present?
        if rack.present? && new_rack.blank?
          rack.change_quantity(total_quantity)
        else
          pm = PackageManagementController.new()
          pm.assign_item_code(self)
        end
        new_rack_list.change_quantity(total_quantity,'add') unless new_rack.present?
        LineItem.update_rack_status(condition: {designer_order_id: self.id}, rack_id: new_rack_list.id)
        response = {error: false, error_text: ''}
      end
    end
    response
  end

  def is_all_items_received
    line_items.all?{|item| item.status.present? || ((item.received_on.present? && item.qc_done_on.present?) || item.issue_created_at.present?)}
  end

  def self.get_gst_report(designer_id, email)
    financial_year_range = get_financial_year_range
    current_financial_year_gst_report = get_gst_report_by_year(designer_id, financial_year_range[0])
    previous_financial_year_gst_report = get_gst_report_by_year(designer_id, financial_year_range[1])
    emails = {'to_email'=> email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer('GST Report', "Please Find Attachment.", emails, {"Current Finanncial Year.csv"=> current_financial_year_gst_report, "Previous Finanncial Year.csv"=> previous_financial_year_gst_report, }).deliver
  end

  def self.get_gst_report_by_year(designer_id, financial_year_range)
    filepath = "GST_Report.csv"
    s_clause = 'designer_orders.id as id , orders.number as order_number, designer_orders.gst_tax as gst_hold_amount,  designer_orders.designer_payout_notes as gst_hold_payout_notes, designer_orders.release_payout_management_id, designer_orders.payout_management_id'
    CSV.open(filepath, "wb", {:col_sep => ","}) do |csv|
      csv <<  ['Order Number','GST hold amount','GST hold payout notes','Gst Release Payout Note']
      DesignerOrder.preload(:gst_release_payout).select(s_clause).joins(:order).where(created_at: financial_year_range, designer_id: designer_id, ship_to: 'mirraw').find_each(batch_size: 500) do |designer_order|
        gst_release_note = designer_order.gst_release_payout
        gst_release_note = gst_release_note ? gst_release_note.payout_version.to_s : "Payout Pending"
        csv << [designer_order.order_number, designer_order.gst_hold_amount, designer_order.gst_hold_payout_notes,gst_release_note]
      end
    end
    File.read(filepath)
  end

  def self.get_financial_year_range
    year_ranges = []
    date = Date.today.beginning_of_month.change(month: 4)
    date = date - 1.year if Date.today.month < 3
    year_ranges << (date.beginning_of_day .. (date + 1.year - 1.day).end_of_day)
    year_ranges << ((date - 1.year).beginning_of_day .. (date - 1.day).end_of_day)
  end

  def generate_combined_unpack_labels
    if self.other_details['combined_unpack_label'].blank?
      pdf = CombinePDF.new
      line_items_ids = self.shipment.try(:line_item_ids).presence || self.bulk_shipment.try(:inbound_line_item_ids).presence || self.line_item_ids
      line_items_ids.each do |item_id|
        url = MIRRAW_DESKTOP_DOMAIN + "/line_items/line_item_label.pdf?from_order_page=true&item_id=#{item_id}&label_type=unpacking&orientation=landscape"
        pdf << CombinePDF.parse(HTTParty.get(url).body)
      end
      file_name = "combined_unpack_label_#{self.id}"
      AwsOperations.create_aws_file(file_name, pdf.to_pdf, false)
      other_details_data = self.other_details
      other_details_data['combined_unpack_label'] = AwsOperations.get_aws_file_path(file_name)
      self.update_columns(other_details: other_details_data)
    end
  end

  def self.export_logistic_info_csv(order_ids, email)
    header = ['Designer Order ID','Order Number','Order Created_at','Order state','Designer Order State', 'Logistic Zone', 'Weight', 'Logistic Partner', 'AWB Number']
    filename  = 'LogisticData/'+ Time.now.strftime("%y_%m_%d_%H_%M") + SecureRandom.hex(2)+'.csv'
    file = CSV.generate(headers: true) do |csv|
      csv << header
      DesignerOrder.includes(:order).where(id: order_ids).find_each(batch_size: 500) do |dos|
        csv << [dos.id, dos.try(:order).try(:number), dos.try(:order).try(:created_at), dos.try(:order).try(:state), dos.try(:state), dos.zone, dos.weight, dos.try(:tracking_partner), dos.try(:tracking_num)]
      end
    end
    AwsOperations.create_aws_file(filename, file, false)
    logistic_url = AwsOperations.get_aws_file_path(filename)
    OrderMailer.report_mailer("Logistic Data Report - #{Date.today.strftime('%b%Y')}","Please click on below URL to get Logistic data <br> #{logistic_url}",{'to_email'=> email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'},{}).deliver_now
  end

  private

  def get_status_message_stitching(stitching_required_count,stitching_done_count,stitching_sent)
    if stitching_required_count == 0
      return 'Stitching Not Required'
    elsif stitching_done_count == stitching_required_count
      return 'All Items Stitching Done'
    else
      return "#{stitching_done_count} Items are Stitched. #{stitching_sent} Items are sent for stitching. #{stitching_required_count - stitching_sent} Items Require Stitching"
    end
  end

  def freeze_payout
    return unless is_payout_paid?
    errors[:base] << "You can't update paid payout." if payout_changed?
  end

  def discount_promotion
    country_code = self.order.actual_country_code
    Rails.cache.fetch("discount_sale_promotion_#{country_code}", expires_in: 1.day) do
      promotion_pipelines = PromotionPipeLine.active_promotions
      if country_code.present?
        promotion_pipelines.find_by_name("global_sale_#{country_code}") || promotion_pipelines.find_by_name("global_sale") || []
      else
        promotion_pipelines.find_by_name('global_sale') || []
      end
    end
  end

  def group_do_and_assign_rack(max_capacity, do_hash, rack_lists)
    do_pairs = pair_maker(max_capacity, do_hash.values)
    do_pairs.each do |pair|
      pair_array = []
      quantity_sum = 0
      pair.each do |val|
        quantity_sum += val
        k, do_hash = delete_and_return_key(do_hash, val)
        pair_array << k
      end
      rack = assign_rack_to_multiple_do(rack_lists, quantity_sum, pair_array)
    end
  end

  def pair_maker(cap, arr)
    rack_required = racks_required(cap,arr)
    arr.sort!
    while rack_required > 0 && arr.compact.sum != 0
      final_arr = []
      (0..(arr.length - 1)).reverse_each do |i|
        mid_arr = []
        sum = 0
        if arr[i] != 0
          sum += arr[i]
          mid_arr << arr[i]  
          arr[i] = 0
          (0..(i-1)).reverse_each do |j|
            if  arr[j]!=0 && (sum + arr[j]) <= cap
              sum += arr[j]
              mid_arr << arr[j]
              arr[arr.rindex(arr[j])] = 0
            end
          end
          final_arr << mid_arr
          rack_required = rack_required - 1
        end
      end
    end
    final_arr
  end

  def racks_required(capacity, count_array)
    (count_array.sum/capacity.to_f).ceil
  end

  def self.get_name_for_stitching_addon_note_value(addon)
    substitute_mapping_hash = {
    "Select Fabric:" => "", 
    "Select Size:" => "Size: ",
    "Select Color:" => "Color: ",
    "Select Fabric Color:"  => "Color: " 
    }
  note = addon.notes
  substitute_mapping_hash.each{|key, value| note.sub!(key, value)}
  note.chomp(", ")
  end


  def delete_and_return_key(do_hash, value)
    k = do_hash.key(value.to_i)
    do_hash.delete(k)
    return k, do_hash
  end
  include ScopeScoreEvent
end
