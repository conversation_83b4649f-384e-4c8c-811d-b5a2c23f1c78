class Playlist < ActiveRecord::Base
  belongs_to :page
  belongs_to :category
  validates :slug, uniqueness: { scope: :page_id }
  validate :at_least_one_filter_present
  serialize :country_ids, Array

  SORT_BY_OPTIONS = [
    'top_rated',
    'l2h',
    'h2l',
    'new',
    'discount',
    'popularity',
    'bstslr',
    'default',
    'trending',
    'trending-designs',
    'popular',
    'recommended',
    'recent-30'
  ].freeze

  def country_code_string
    country_code.join(', ')
  end
  
  def country_code_string=(value)
    self.country_code = value.split(',').map(&:strip)
  end
  

  def country_codes
    self[:country_code] || []
  end

  def country_codes=(codes)
    self[:country_code] = codes.reject(&:blank?)
  end  

  
  private
  def at_least_one_filter_present
    if search_query.blank? && designer_id.blank? && category_id.blank?
      errors.add(:base, "At least one of search_query, designer_id, or category_id must be present.")
    end
  end
end
